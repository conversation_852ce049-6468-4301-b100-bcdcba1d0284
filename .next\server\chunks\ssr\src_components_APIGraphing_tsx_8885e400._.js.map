{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/APIGraphing.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState, useMemo, useCallback } from 'react'\nimport ReactECharts from 'echarts-for-react'\nimport { useTheme } from 'next-themes'\nimport {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\n\nimport type { DashboardStats } from '@/types'\n\n// Advanced data types for enhanced analytics\ninterface ChartData {\n  salesData: number[]\n  debtData: number[]\n  categoryData: { name: string; value: number; color: string }[]\n  trendData: { month: string; sales: number; debt: number; profit: number }[]\n  performanceMetrics: {\n    revenue: { current: number; previous: number; change: number }\n    customers: { current: number; previous: number; change: number }\n    products: { current: number; previous: number; change: number }\n    efficiency: { current: number; previous: number; change: number }\n  }\n}\n\ninterface FilterOptions {\n  dateRange: 'week' | 'month' | 'quarter' | 'year'\n  chartType: 'line' | 'bar' | 'area'\n  dataType: 'revenue' | 'customers' | 'products' | 'all'\n  showTrends: boolean\n  showForecasting: boolean\n}\n\ninterface APIGraphingProps {\n  stats: DashboardStats\n}\n\nexport default function APIGraphing({ stats }: APIGraphingProps) {\n  const { resolvedTheme } = useTheme()\n  const [chartData, setChartData] = useState<ChartData>({\n    salesData: [],\n    debtData: [],\n    categoryData: [],\n    trendData: [],\n    performanceMetrics: {\n      revenue: { current: 0, previous: 0, change: 0 },\n      customers: { current: 0, previous: 0, change: 0 },\n      products: { current: 0, previous: 0, change: 0 },\n      efficiency: { current: 0, previous: 0, change: 0 }\n    }\n  })\n\n  const [filters, setFilters] = useState<FilterOptions>({\n    dateRange: 'month',\n    chartType: 'line',\n    dataType: 'all',\n    showTrends: true,\n    showForecasting: false\n  })\n\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n\n  const [isLoading, setIsLoading] = useState(false)\n  const [lastUpdated, setLastUpdated] = useState(new Date())\n  const [isMobile, setIsMobile] = useState(false)\n\n  // Responsive design hook\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768)\n    }\n\n    checkMobile()\n    window.addEventListener('resize', checkMobile)\n    return () => window.removeEventListener('resize', checkMobile)\n  }, [])\n\n  // Advanced data generation with realistic patterns and trends\n  const generateAdvancedData = useCallback(() => {\n    setIsLoading(true)\n\n    // Generate realistic sales data with seasonal patterns\n    const generateSalesData = () => {\n      const baseValue = 25000\n      const seasonalMultipliers = [0.8, 0.85, 0.9, 1.0, 1.1, 1.2, 1.3, 1.25, 1.15, 1.05, 0.95, 0.9]\n      return seasonalMultipliers.map((multiplier, index) => {\n        const randomVariation = (Math.random() - 0.5) * 0.2\n        return Math.floor(baseValue * multiplier * (1 + randomVariation))\n      })\n    }\n\n    // Generate debt data with weekly patterns\n    const generateDebtData = () => {\n      const baseDebt = 8000\n      const weeklyPattern = [0.9, 1.0, 1.1, 1.2, 1.3, 1.1, 0.8] // Mon-Sun pattern\n      return weeklyPattern.map(multiplier => {\n        const randomVariation = (Math.random() - 0.5) * 0.15\n        return Math.floor(baseDebt * multiplier * (1 + randomVariation))\n      })\n    }\n\n    // Generate category distribution data\n    const generateCategoryData = () => {\n      const categories = [\n        { name: 'Beverages', value: 35, color: '#22c55e' },\n        { name: 'Snacks', value: 28, color: '#3b82f6' },\n        { name: 'Household', value: 20, color: '#f59e0b' },\n        { name: 'Personal Care', value: 12, color: '#ef4444' },\n        { name: 'Others', value: 5, color: '#8b5cf6' }\n      ]\n\n      let filteredCategories = categories.map(cat => ({\n        ...cat,\n        value: cat.value + Math.floor((Math.random() - 0.5) * 10)\n      }))\n\n      // Filter based on selected category\n      if (selectedCategory !== 'all') {\n        filteredCategories = filteredCategories.filter(cat =>\n          cat.name.toLowerCase() === selectedCategory.toLowerCase()\n        )\n      }\n\n      return filteredCategories\n    }\n\n    // Generate trend data for advanced analytics\n    const generateTrendData = () => {\n      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']\n      return months.map((month, index) => {\n        const sales = 20000 + (index * 2000) + Math.floor(Math.random() * 5000)\n        const debt = 5000 + Math.floor(Math.random() * 3000)\n        const profit = sales * 0.3 - debt * 0.1\n        return { month, sales, debt, profit }\n      })\n    }\n\n    // Calculate performance metrics with comparisons\n    const calculatePerformanceMetrics = (salesData: number[], currentStats: DashboardStats) => {\n      const currentRevenue = salesData.reduce((a, b) => a + b, 0)\n      const previousRevenue = currentRevenue * (0.85 + Math.random() * 0.3)\n\n      return {\n        revenue: {\n          current: currentRevenue,\n          previous: previousRevenue,\n          change: ((currentRevenue - previousRevenue) / previousRevenue) * 100\n        },\n        customers: {\n          current: currentStats.totalDebts,\n          previous: Math.floor(currentStats.totalDebts * (0.9 + Math.random() * 0.2)),\n          change: Math.floor((Math.random() - 0.5) * 20)\n        },\n        products: {\n          current: currentStats.totalProducts,\n          previous: Math.floor(currentStats.totalProducts * (0.95 + Math.random() * 0.1)),\n          change: Math.floor((Math.random() - 0.3) * 15)\n        },\n        efficiency: {\n          current: 85 + Math.floor(Math.random() * 10),\n          previous: 80 + Math.floor(Math.random() * 10),\n          change: Math.floor((Math.random() - 0.4) * 10)\n        }\n      }\n    }\n\n    // Simulate API delay for realistic loading experience\n    setTimeout(() => {\n      const salesData = generateSalesData()\n      const debtData = generateDebtData()\n      const categoryData = generateCategoryData()\n      const trendData = generateTrendData()\n      const performanceMetrics = calculatePerformanceMetrics(salesData, stats)\n\n      setChartData({\n        salesData,\n        debtData,\n        categoryData,\n        trendData,\n        performanceMetrics\n      })\n\n      setLastUpdated(new Date())\n      setIsLoading(false)\n    }, 800)\n  }, [stats])\n\n  useEffect(() => {\n    generateAdvancedData()\n  }, [generateAdvancedData, filters.dateRange, selectedCategory])\n\n  // Advanced chart configurations with theme support\n  const getChartTheme = () => ({\n    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n    textStyle: {\n      color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937',\n      fontFamily: 'Inter, system-ui, sans-serif'\n    },\n    grid: {\n      borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'\n    }\n  })\n\n  // Enhanced Sales Chart with advanced features\n  const salesChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Monthly Sales Revenue',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'axis',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const data = params[0]\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${data.name}</div>\n            <div style=\"display: flex; align-items: center;\">\n              <div style=\"width: 10px; height: 10px; background: ${data.color}; border-radius: 50%; margin-right: 8px;\"></div>\n              Revenue: ₱${data.value.toLocaleString()}\n            </div>\n            <div style=\"font-size: 12px; color: #6b7280; margin-top: 4px;\">\n              ${data.value > (chartData.salesData[data.dataIndex - 1] || 0) ? '↗️ Increased' : '↘️ Decreased'} from previous month\n            </div>\n          </div>\n        `\n      }\n    },\n    legend: {\n      show: true,\n      top: 40,\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#cbd5e1' : '#4b5563'\n      }\n    },\n    xAxis: {\n      type: 'category',\n      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n        }\n      },\n      axisLabel: {\n        color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n        fontSize: 12\n      }\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n        color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n        fontSize: 12\n      },\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n        }\n      },\n      splitLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#334155' : '#f3f4f6',\n          type: 'dashed'\n        }\n      }\n    },\n    series: [\n      {\n        name: 'Revenue',\n        data: chartData.salesData,\n        type: filters.chartType,\n        smooth: true,\n        lineStyle: {\n          color: '#22c55e',\n          width: 3\n        },\n        itemStyle: {\n          color: '#22c55e',\n          borderRadius: filters.chartType === 'bar' ? [4, 4, 0, 0] : 0\n        },\n        areaStyle: filters.chartType === 'area' ? {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: 'rgba(34, 197, 94, 0.4)' },\n              { offset: 1, color: 'rgba(34, 197, 94, 0.05)' }\n            ]\n          }\n        } : undefined,\n        emphasis: {\n          focus: 'series',\n          itemStyle: {\n            shadowBlur: 10,\n            shadowColor: 'rgba(34, 197, 94, 0.5)'\n          }\n        },\n        markPoint: {\n          data: [\n            { type: 'max', name: 'Max' },\n            { type: 'min', name: 'Min' }\n          ],\n          itemStyle: {\n            color: '#facc15'\n          }\n        },\n        markLine: filters.showTrends ? {\n          data: [\n            { type: 'average', name: 'Average' }\n          ],\n          lineStyle: {\n            color: '#f59e0b',\n            type: 'dashed'\n          }\n        } : undefined\n      }\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '10%',\n      top: '15%',\n      containLabel: true\n    },\n    dataZoom: [\n      {\n        type: 'inside',\n        start: 0,\n        end: 100\n      },\n      {\n        start: 0,\n        end: 100,\n        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',\n        handleSize: '80%',\n        handleStyle: {\n          color: '#22c55e',\n          shadowBlur: 3,\n          shadowColor: 'rgba(0, 0, 0, 0.6)',\n          shadowOffsetX: 2,\n          shadowOffsetY: 2\n        }\n      }\n    ],\n    toolbox: {\n      feature: {\n        dataZoom: {\n          yAxisIndex: 'none'\n        },\n        restore: {},\n        saveAsImage: {\n          pixelRatio: 2\n        }\n      },\n      iconStyle: {\n        borderColor: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      }\n    }\n  }), [chartData.salesData, filters.chartType, resolvedTheme])\n\n  // Enhanced Debt Chart with advanced features\n  const debtChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Weekly Customer Debt Trends',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'axis',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const data = params[0]\n        const percentage = ((data.value / chartData.debtData.reduce((a: number, b: number) => a + b, 0)) * 100).toFixed(1)\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${data.name}</div>\n            <div style=\"display: flex; align-items: center;\">\n              <div style=\"width: 10px; height: 10px; background: ${data.color}; border-radius: 2px; margin-right: 8px;\"></div>\n              Total Debt: ₱${data.value.toLocaleString()}\n            </div>\n            <div style=\"font-size: 12px; color: #6b7280; margin-top: 4px;\">\n              ${percentage}% of weekly total\n            </div>\n          </div>\n        `\n      }\n    },\n    legend: {\n      show: true,\n      top: 40,\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#cbd5e1' : '#4b5563'\n      }\n    },\n    xAxis: {\n      type: 'category',\n      data: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n        }\n      },\n      axisLabel: {\n        color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n        fontSize: 12,\n        rotate: 45\n      }\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n        color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n        fontSize: 12\n      },\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n        }\n      },\n      splitLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#334155' : '#f3f4f6',\n          type: 'dashed'\n        }\n      }\n    },\n    series: [\n      {\n        name: 'Customer Debt',\n        data: chartData.debtData,\n        type: 'bar',\n        itemStyle: {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: '#facc15' },\n              { offset: 1, color: '#eab308' }\n            ]\n          },\n          borderRadius: [4, 4, 0, 0]\n        },\n        emphasis: {\n          focus: 'series',\n          itemStyle: {\n            color: '#f59e0b',\n            shadowBlur: 10,\n            shadowColor: 'rgba(245, 158, 11, 0.5)'\n          }\n        },\n        markPoint: {\n          data: [\n            { type: 'max', name: 'Peak Day' },\n            { type: 'min', name: 'Low Day' }\n          ],\n          itemStyle: {\n            color: '#ef4444'\n          }\n        }\n      }\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '15%',\n      top: '15%',\n      containLabel: true\n    },\n    toolbox: {\n      feature: {\n        dataZoom: {\n          yAxisIndex: 'none'\n        },\n        restore: {},\n        saveAsImage: {\n          pixelRatio: 2\n        }\n      },\n      iconStyle: {\n        borderColor: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      }\n    }\n  }), [chartData.debtData, resolvedTheme])\n\n  // Enhanced Product Categories Pie Chart\n  const categoryChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Product Categories Distribution',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'item',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${params.name}</div>\n            <div style=\"display: flex; align-items: center;\">\n              <div style=\"width: 10px; height: 10px; background: ${params.color}; border-radius: 50%; margin-right: 8px;\"></div>\n              Value: ${params.value}%\n            </div>\n            <div style=\"font-size: 12px; color: #6b7280; margin-top: 4px;\">\n              ${params.percent}% of total sales\n            </div>\n          </div>\n        `\n      }\n    },\n    legend: {\n      orient: 'horizontal',\n      bottom: 10,\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#cbd5e1' : '#4b5563',\n        fontSize: 12\n      }\n    },\n    grid: {\n      top: 40,\n      bottom: 50,\n      left: 20,\n      right: 20,\n      containLabel: true\n    },\n    series: [\n      {\n        name: 'Categories',\n        type: 'pie',\n        radius: ['25%', '45%'], // Further reduced radius for better label visibility\n        center: ['50%', '50%'], // Centered position\n        avoidLabelOverlap: true,\n        itemStyle: {\n          borderRadius: 6,\n          borderColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n          borderWidth: 2\n        },\n        label: {\n          show: true,\n          position: 'outside',\n          formatter: '{b}: {c}%',\n          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#4b5563',\n          fontSize: 12,\n          fontWeight: '500',\n          distanceToLabelLine: 8,\n          padding: [2, 4],\n          backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.8)' : 'rgba(255, 255, 255, 0.9)',\n          borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(229, 231, 235, 0.8)',\n          borderWidth: 1,\n          borderRadius: 4\n        },\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowOffsetX: 0,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          },\n          label: {\n            show: true,\n            fontSize: 13,\n            fontWeight: 'bold'\n          }\n        },\n        labelLine: {\n          show: true,\n          length: 20,\n          length2: 10,\n          lineStyle: {\n            color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',\n            width: 1.5\n          }\n        },\n        data: chartData.categoryData\n      }\n    ]\n  }), [chartData.categoryData, resolvedTheme])\n\n  // Advanced Heatmap Chart for hourly sales patterns\n  const heatmapChartOption = useMemo(() => {\n    const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`)\n    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n    const heatmapData = []\n\n    for (let day = 0; day < 7; day++) {\n      for (let hour = 0; hour < 24; hour++) {\n        const value = Math.floor(Math.random() * 100) + 10\n        heatmapData.push([hour, day, value])\n      }\n    }\n\n    return {\n      ...getChartTheme(),\n      title: {\n        text: 'Sales Activity Heatmap',\n        textStyle: {\n          fontSize: 18,\n          fontWeight: 'bold',\n          color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n        },\n        left: 'center',\n        top: 10\n      },\n      tooltip: {\n        position: 'top',\n        backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n        borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n        textStyle: {\n          color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n        },\n        formatter: (params: any) => {\n          return `\n            <div style=\"padding: 8px;\">\n              <div style=\"font-weight: bold; margin-bottom: 4px;\">${days[params.data[1]]} ${hours[params.data[0]]}</div>\n              <div style=\"display: flex; align-items: center;\">\n                <div style=\"width: 10px; height: 10px; background: ${params.color}; border-radius: 2px; margin-right: 8px;\"></div>\n                Sales Activity: ${params.data[2]}%\n              </div>\n            </div>\n          `\n        }\n      },\n      grid: {\n        height: '60%',\n        top: '15%'\n      },\n      xAxis: {\n        type: 'category',\n        data: hours,\n        splitArea: {\n          show: true\n        },\n        axisLabel: {\n          color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n          fontSize: 10\n        }\n      },\n      yAxis: {\n        type: 'category',\n        data: days,\n        splitArea: {\n          show: true\n        },\n        axisLabel: {\n          color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n          fontSize: 12\n        }\n      },\n      visualMap: {\n        min: 0,\n        max: 100,\n        calculable: true,\n        orient: 'horizontal',\n        left: 'center',\n        bottom: '5%',\n        inRange: {\n          color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']\n        },\n        textStyle: {\n          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#4b5563'\n        }\n      },\n      series: [{\n        name: 'Sales Activity',\n        type: 'heatmap',\n        data: heatmapData,\n        label: {\n          show: false\n        },\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          }\n        }\n      }]\n    }\n  }, [resolvedTheme])\n\n  // Gauge Chart for performance metrics\n  const gaugeChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Business Performance',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    series: [\n      {\n        name: 'Performance',\n        type: 'gauge',\n        center: ['50%', '60%'],\n        startAngle: 200,\n        endAngle: -40,\n        min: 0,\n        max: 100,\n        splitNumber: 10,\n        itemStyle: {\n          color: '#22c55e'\n        },\n        progress: {\n          show: true,\n          width: 30\n        },\n        pointer: {\n          show: false\n        },\n        axisLine: {\n          lineStyle: {\n            width: 30,\n            color: [\n              [0.3, '#ef4444'],\n              [0.7, '#f59e0b'],\n              [1, '#22c55e']\n            ]\n          }\n        },\n        axisTick: {\n          distance: -45,\n          splitNumber: 5,\n          lineStyle: {\n            width: 2,\n            color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n          }\n        },\n        splitLine: {\n          distance: -52,\n          length: 14,\n          lineStyle: {\n            width: 3,\n            color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n          }\n        },\n        axisLabel: {\n          distance: -20,\n          color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n          fontSize: 12\n        },\n        anchor: {\n          show: false\n        },\n        title: {\n          show: false\n        },\n        detail: {\n          valueAnimation: true,\n          width: '60%',\n          lineHeight: 40,\n          borderRadius: 8,\n          offsetCenter: [0, '-15%'],\n          fontSize: 24,\n          fontWeight: 'bold',\n          formatter: '{value}%',\n          color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n        },\n        data: [\n          {\n            value: chartData.performanceMetrics.efficiency.current,\n            name: 'Efficiency'\n          }\n        ]\n      }\n    ]\n  }), [chartData.performanceMetrics.efficiency.current, resolvedTheme])\n\n  // Enhanced KPI calculations with advanced metrics\n  const kpiCards = useMemo(() => {\n    const totalRevenue = chartData.salesData.reduce((a: number, b: number) => a + b, 0)\n    const avgMonthlyRevenue = totalRevenue / chartData.salesData.length\n    const totalDebt = chartData.debtData.reduce((a: number, b: number) => a + b, 0)\n    const avgDailyDebt = totalDebt / chartData.debtData.length\n\n    return [\n      {\n        title: 'Total Revenue',\n        value: '₱' + totalRevenue.toLocaleString(),\n        icon: DollarSign,\n        color: 'text-green-600 dark:text-green-400',\n        bgColor: 'bg-green-50 dark:bg-green-900/20',\n        change: '+12.5%',\n        changeColor: 'text-green-600 dark:text-green-400',\n        trend: 'up',\n        subtitle: `Avg: ₱${avgMonthlyRevenue.toLocaleString()}/month`\n      },\n      {\n        title: 'Active Customers',\n        value: chartData.performanceMetrics.customers.current.toString(),\n        icon: Users,\n        color: 'text-blue-600 dark:text-blue-400',\n        bgColor: 'bg-blue-50 dark:bg-blue-900/20',\n        change: `${chartData.performanceMetrics.customers.change > 0 ? '+' : ''}${chartData.performanceMetrics.customers.change.toFixed(1)}%`,\n        changeColor: chartData.performanceMetrics.customers.change > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400',\n        trend: chartData.performanceMetrics.customers.change > 0 ? 'up' : 'down',\n        subtitle: 'Customer base growth'\n      },\n      {\n        title: 'Products Listed',\n        value: chartData.performanceMetrics.products.current.toString(),\n        icon: Package,\n        color: 'text-purple-600 dark:text-purple-400',\n        bgColor: 'bg-purple-50 dark:bg-purple-900/20',\n        change: `${chartData.performanceMetrics.products.change > 0 ? '+' : ''}${chartData.performanceMetrics.products.change.toFixed(1)}%`,\n        changeColor: chartData.performanceMetrics.products.change > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400',\n        trend: chartData.performanceMetrics.products.change > 0 ? 'up' : 'down',\n        subtitle: 'Inventory expansion'\n      },\n      {\n        title: 'Business Efficiency',\n        value: `${chartData.performanceMetrics.efficiency.current}%`,\n        icon: Target,\n        color: 'text-orange-600 dark:text-orange-400',\n        bgColor: 'bg-orange-50 dark:bg-orange-900/20',\n        change: `${chartData.performanceMetrics.efficiency.change > 0 ? '+' : ''}${chartData.performanceMetrics.efficiency.change.toFixed(1)}%`,\n        changeColor: chartData.performanceMetrics.efficiency.change > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400',\n        trend: chartData.performanceMetrics.efficiency.change > 0 ? 'up' : 'down',\n        subtitle: 'Operational performance'\n      },\n      {\n        title: 'Weekly Debt Avg',\n        value: '₱' + avgDailyDebt.toLocaleString(),\n        icon: TrendingDown,\n        color: 'text-yellow-600 dark:text-yellow-400',\n        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',\n        change: '-3.2%',\n        changeColor: 'text-green-600 dark:text-green-400',\n        trend: 'down',\n        subtitle: 'Daily average debt'\n      },\n      {\n        title: 'Growth Rate',\n        value: `${((totalRevenue / (totalRevenue * 0.85)) * 100 - 100).toFixed(1)}%`,\n        icon: TrendingUp,\n        color: 'text-emerald-600 dark:text-emerald-400',\n        bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',\n        change: '+15.8%',\n        changeColor: 'text-green-600 dark:text-green-400',\n        trend: 'up',\n        subtitle: 'Monthly growth rate'\n      }\n    ]\n  }, [chartData, stats.totalDebtAmount])\n\n  // Filter controls component\n  const FilterControls = () => (\n    <div className=\"card p-4 mb-6\">\n      <div className=\"flex flex-wrap items-center justify-between gap-4\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-2\">\n            <Filter className=\"h-4 w-4 text-gray-500 dark:text-gray-400\" />\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">Filters:</span>\n          </div>\n\n          <select\n            value={filters.dateRange}\n            onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value as FilterOptions['dateRange'] }))}\n            className=\"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n          >\n            <option value=\"week\">This Week</option>\n            <option value=\"month\">This Month</option>\n            <option value=\"quarter\">This Quarter</option>\n            <option value=\"year\">This Year</option>\n          </select>\n\n          <select\n            value={filters.chartType}\n            onChange={(e) => setFilters(prev => ({ ...prev, chartType: e.target.value as FilterOptions['chartType'] }))}\n            className=\"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n          >\n            <option value=\"line\">Line Chart</option>\n            <option value=\"bar\">Bar Chart</option>\n            <option value=\"area\">Area Chart</option>\n          </select>\n        </div>\n\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={generateAdvancedData}\n            disabled={isLoading}\n            className=\"flex items-center space-x-2 px-3 py-1 text-sm bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200\"\n          >\n            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />\n            <span>{isLoading ? 'Updating...' : 'Refresh'}</span>\n          </button>\n\n          <button className=\"flex items-center space-x-2 px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200\">\n            <Download className=\"h-4 w-4\" />\n            <span>Export</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Filter Controls */}\n      <FilterControls />\n\n      {/* Enhanced KPI Cards - Responsive Grid */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-3 sm:gap-4\">\n        {kpiCards.map((kpi, index) => (\n          <div key={index} className=\"card p-4 hover:shadow-lg transition-all duration-300 group\">\n            <div className=\"flex flex-col space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <div className={`p-2 rounded-lg ${kpi.bgColor} group-hover:scale-110 transition-transform duration-200`}>\n                  <kpi.icon className={`h-5 w-5 ${kpi.color}`} />\n                </div>\n                <div className=\"flex items-center space-x-1\">\n                  {kpi.trend === 'up' && <ArrowUp className=\"h-3 w-3 text-green-500\" />}\n                  {kpi.trend === 'down' && <ArrowDown className=\"h-3 w-3 text-red-500\" />}\n                  {kpi.trend === 'neutral' && <Minus className=\"h-3 w-3 text-gray-500\" />}\n                </div>\n              </div>\n\n              <div>\n                <p className=\"text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide\">\n                  {kpi.title}\n                </p>\n                <p className=\"text-lg font-bold text-gray-900 dark:text-white mt-1\">\n                  {kpi.value}\n                </p>\n                {kpi.subtitle && (\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                    {kpi.subtitle}\n                  </p>\n                )}\n                <div className=\"flex items-center justify-between mt-2\">\n                  <span className={`text-xs font-medium ${kpi.changeColor}`}>\n                    {kpi.change}\n                  </span>\n                  <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    vs last period\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Enhanced Charts Grid - Responsive Layout */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6\">\n        {/* Sales Revenue Chart */}\n        <div className=\"card p-6 hover:shadow-lg transition-shadow duration-300\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"p-2 bg-green-100 dark:bg-green-900/20 rounded-lg\">\n                <TrendingUp className=\"h-5 w-5 text-green-600 dark:text-green-400\" />\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Sales Revenue</h3>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Monthly performance trends</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center space-x-1\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">Live</span>\n              </div>\n            </div>\n          </div>\n          {isLoading ? (\n            <div className=\"flex items-center justify-center h-96\">\n              <div className=\"flex flex-col items-center space-y-4\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500\"></div>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Loading chart data...</p>\n              </div>\n            </div>\n          ) : (\n            <ReactECharts\n              option={salesChartOption}\n              style={{ height: isMobile ? '300px' : '400px' }}\n            />\n          )}\n        </div>\n\n        {/* Customer Debt Chart */}\n        <div className=\"card p-6 hover:shadow-lg transition-shadow duration-300\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg\">\n                <Users className=\"h-5 w-5 text-yellow-600 dark:text-yellow-400\" />\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Customer Debt</h3>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Weekly debt patterns</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center space-x-1\">\n                <div className=\"w-2 h-2 bg-yellow-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">Active</span>\n              </div>\n            </div>\n          </div>\n          {isLoading ? (\n            <div className=\"flex items-center justify-center h-96\">\n              <div className=\"flex flex-col items-center space-y-4\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500\"></div>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Loading chart data...</p>\n              </div>\n            </div>\n          ) : (\n            <ReactECharts\n              option={debtChartOption}\n              style={{ height: isMobile ? '300px' : '400px' }}\n            />\n          )}\n        </div>\n      </div>\n\n      {/* Advanced Visualization Grid - Mobile Optimized */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6\">\n        {/* Category Distribution Chart */}\n        <div className=\"card p-6 hover:shadow-lg transition-all duration-300 hover:scale-[1.02] border border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/20 rounded-lg shadow-sm\">\n                <Package className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Product Categories</h3>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Interactive sales distribution by category</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center space-x-1 px-2 py-1 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-xs text-green-600 dark:text-green-400 font-medium\">Live</span>\n              </div>\n              <button className=\"p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200\" title=\"View options\">\n                <Eye className=\"h-4 w-4 text-gray-500 dark:text-gray-400\" />\n              </button>\n              <button className=\"p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200\" title=\"Chart settings\">\n                <Settings className=\"h-4 w-4 text-gray-500 dark:text-gray-400\" />\n              </button>\n            </div>\n          </div>\n\n          {/* Category Filter Buttons */}\n          <div className=\"mb-6 px-2\">\n            <div className=\"flex flex-wrap items-center justify-center gap-2 sm:gap-3\">\n              {[\n                { key: 'all', label: 'All Categories', color: 'bg-gray-500 hover:bg-gray-600', darkColor: 'dark:bg-gray-600 dark:hover:bg-gray-700' },\n                { key: 'beverages', label: 'Beverages', color: 'bg-green-500 hover:bg-green-600', darkColor: 'dark:bg-green-600 dark:hover:bg-green-700' },\n                { key: 'snacks', label: 'Snacks', color: 'bg-blue-500 hover:bg-blue-600', darkColor: 'dark:bg-blue-600 dark:hover:bg-blue-700' },\n                { key: 'household', label: 'Household', color: 'bg-yellow-500 hover:bg-yellow-600', darkColor: 'dark:bg-yellow-600 dark:hover:bg-yellow-700' },\n                { key: 'personal care', label: 'Personal Care', color: 'bg-red-500 hover:bg-red-600', darkColor: 'dark:bg-red-600 dark:hover:bg-red-700' },\n                { key: 'others', label: 'Others', color: 'bg-purple-500 hover:bg-purple-600', darkColor: 'dark:bg-purple-600 dark:hover:bg-purple-700' }\n              ].map((category) => (\n                <button\n                  key={category.key}\n                  onClick={() => setSelectedCategory(category.key)}\n                  className={`px-3 py-2 sm:px-4 sm:py-2 rounded-lg text-white text-xs sm:text-sm font-medium transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50 shadow-md backdrop-blur-sm ${category.color} ${category.darkColor} ${\n                    selectedCategory === category.key\n                      ? 'ring-2 ring-white dark:ring-gray-300 ring-opacity-50 scale-105 shadow-lg'\n                      : 'opacity-90 hover:opacity-100'\n                  }`}\n                  style={{\n                    boxShadow: selectedCategory === category.key\n                      ? '0 8px 16px rgba(0, 0, 0, 0.25), 0 0 0 2px rgba(255, 255, 255, 0.4)'\n                      : '0 4px 8px rgba(0, 0, 0, 0.15)',\n                    minWidth: isMobile ? '80px' : '100px'\n                  }}\n                >\n                  <span className=\"block truncate\">{category.label}</span>\n                </button>\n              ))}\n            </div>\n            <div className=\"text-center mt-3\">\n              <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                Click on a category to filter the chart data\n              </p>\n            </div>\n          </div>\n\n          {isLoading ? (\n            <div className=\"flex items-center justify-center h-96\">\n              <div className=\"flex flex-col items-center space-y-4\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Loading chart data...</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"transition-all duration-500 ease-in-out\">\n              <ReactECharts\n                option={categoryChartOption}\n                style={{ height: isMobile ? '400px' : '500px' }}\n                opts={{ renderer: 'svg' }}\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Performance Gauge Chart */}\n        <div className=\"card p-6 hover:shadow-lg transition-shadow duration-300\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"p-2 bg-emerald-100 dark:bg-emerald-900/20 rounded-lg\">\n                <Target className=\"h-5 w-5 text-emerald-600 dark:text-emerald-400\" />\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Performance Gauge</h3>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Overall business efficiency</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center space-x-1\">\n                <div className=\"w-2 h-2 bg-emerald-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">Real-time</span>\n              </div>\n            </div>\n          </div>\n          {isLoading ? (\n            <div className=\"flex items-center justify-center h-96\">\n              <div className=\"flex flex-col items-center space-y-4\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500\"></div>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Loading gauge data...</p>\n              </div>\n            </div>\n          ) : (\n            <ReactECharts\n              option={gaugeChartOption}\n              style={{ height: isMobile ? '300px' : '400px' }}\n            />\n          )}\n        </div>\n      </div>\n\n      {/* Sales Activity Heatmap */}\n      <div className=\"card p-6 hover:shadow-lg transition-shadow duration-300\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg\">\n              <Activity className=\"h-5 w-5 text-purple-600 dark:text-purple-400\" />\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Sales Activity Heatmap</h3>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">Hourly sales patterns throughout the week</p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n              <span className=\"text-xs text-gray-500 dark:text-gray-400\">Pattern Analysis</span>\n            </div>\n          </div>\n        </div>\n        {isLoading ? (\n          <div className=\"flex items-center justify-center h-96\">\n            <div className=\"flex flex-col items-center space-y-4\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500\"></div>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">Loading heatmap data...</p>\n            </div>\n          </div>\n        ) : (\n          <ReactECharts\n            option={heatmapChartOption}\n            style={{ height: isMobile ? '400px' : '500px' }}\n          />\n        )}\n      </div>\n\n      {/* Advanced Analytics Dashboard */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Predictive Insights */}\n        <div className=\"card p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Predictive Insights</h3>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-sm text-blue-600 dark:text-blue-400 font-medium\">AI Powered</span>\n            </div>\n          </div>\n          <div className=\"space-y-4\">\n            <div className=\"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <TrendingUp className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n                <span className=\"text-sm font-medium text-blue-800 dark:text-blue-300\">Revenue Forecast</span>\n              </div>\n              <p className=\"text-xs text-blue-700 dark:text-blue-400\">\n                Expected 15% growth next month based on current trends\n              </p>\n              <div className=\"mt-2 w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2\">\n                <div className=\"bg-blue-600 h-2 rounded-full\" style={{ width: '75%' }}></div>\n              </div>\n            </div>\n\n            <div className=\"p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <Users className=\"h-4 w-4 text-green-600 dark:text-green-400\" />\n                <span className=\"text-sm font-medium text-green-800 dark:text-green-300\">Customer Growth</span>\n              </div>\n              <p className=\"text-xs text-green-700 dark:text-green-400\">\n                New customer acquisition rate increasing by 8%\n              </p>\n              <div className=\"mt-2 w-full bg-green-200 dark:bg-green-800 rounded-full h-2\">\n                <div className=\"bg-green-600 h-2 rounded-full\" style={{ width: '68%' }}></div>\n              </div>\n            </div>\n\n            <div className=\"p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <Package className=\"h-4 w-4 text-yellow-600 dark:text-yellow-400\" />\n                <span className=\"text-sm font-medium text-yellow-800 dark:text-yellow-300\">Inventory Alert</span>\n              </div>\n              <p className=\"text-xs text-yellow-700 dark:text-yellow-400\">\n                3 products predicted to run low stock within 5 days\n              </p>\n              <div className=\"mt-2 w-full bg-yellow-200 dark:bg-yellow-800 rounded-full h-2\">\n                <div className=\"bg-yellow-600 h-2 rounded-full\" style={{ width: '30%' }}></div>\n              </div>\n            </div>\n          </div>\n        </div>\n        {/* Real-time Status */}\n        <div className=\"card p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">System Status</h3>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-sm text-green-600 dark:text-green-400 font-medium\">Online</span>\n            </div>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <Activity className=\"h-4 w-4 text-green-500\" />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Data Streaming</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                <span className=\"text-sm text-green-600 dark:text-green-400\">Active</span>\n              </div>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <Zap className=\"h-4 w-4 text-blue-500\" />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">API Response</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-blue-600 dark:text-blue-400\">~250ms</span>\n              </div>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <Clock className=\"h-4 w-4 text-yellow-500\" />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Last Update</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {lastUpdated.toLocaleTimeString()}\n                </span>\n              </div>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <Target className=\"h-4 w-4 text-purple-500\" />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Data Quality</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-purple-600 dark:text-purple-400\">98.5%</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Performance Metrics */}\n        <div className=\"card p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Performance</h3>\n            <div className=\"flex items-center space-x-2\">\n              <Target className=\"h-4 w-4 text-blue-500\" />\n              <span className=\"text-sm text-blue-600 dark:text-blue-400 font-medium\">Optimized</span>\n            </div>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">Chart Rendering</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div className=\"bg-green-500 h-2 rounded-full\" style={{ width: '95%' }}></div>\n                </div>\n                <span className=\"text-sm text-green-600 dark:text-green-400\">95%</span>\n              </div>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">Data Processing</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div className=\"bg-blue-500 h-2 rounded-full\" style={{ width: '88%' }}></div>\n                </div>\n                <span className=\"text-sm text-blue-600 dark:text-blue-400\">88%</span>\n              </div>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">Memory Usage</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div className=\"bg-yellow-500 h-2 rounded-full\" style={{ width: '72%' }}></div>\n                </div>\n                <span className=\"text-sm text-yellow-600 dark:text-yellow-400\">72%</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Business Intelligence Summary */}\n      <div className=\"card p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"p-2 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg\">\n              <TrendingUp className=\"h-5 w-5 text-indigo-600 dark:text-indigo-400\" />\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Business Intelligence Summary</h3>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">Key insights and recommendations</p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-2 h-2 bg-indigo-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-xs text-gray-500 dark:text-gray-400\">Auto-generated</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-600 dark:text-green-400\" />\n              <span className=\"text-sm font-medium text-green-800 dark:text-green-300\">Strong Performance</span>\n            </div>\n            <p className=\"text-xs text-green-700 dark:text-green-400\">\n              Revenue growth is exceeding targets by 12%. Continue current marketing strategies.\n            </p>\n          </div>\n\n          <div className=\"p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Users className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n              <span className=\"text-sm font-medium text-blue-800 dark:text-blue-300\">Customer Retention</span>\n            </div>\n            <p className=\"text-xs text-blue-700 dark:text-blue-400\">\n              Customer loyalty programs showing positive impact. 85% retention rate achieved.\n            </p>\n          </div>\n\n          <div className=\"p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-lg\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Package className=\"h-4 w-4 text-yellow-600 dark:text-yellow-400\" />\n              <span className=\"text-sm font-medium text-yellow-800 dark:text-yellow-300\">Inventory Optimization</span>\n            </div>\n            <p className=\"text-xs text-yellow-700 dark:text-yellow-400\">\n              Consider increasing stock for high-demand items. Seasonal patterns identified.\n            </p>\n          </div>\n\n          <div className=\"p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Target className=\"h-4 w-4 text-purple-600 dark:text-purple-400\" />\n              <span className=\"text-sm font-medium text-purple-800 dark:text-purple-300\">Efficiency Gains</span>\n            </div>\n            <p className=\"text-xs text-purple-700 dark:text-purple-400\">\n              Operational efficiency improved by 15%. Focus on peak hour optimization.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg\">\n          <div className=\"flex items-center space-x-2 mb-3\">\n            <Activity className=\"h-4 w-4 text-gray-600 dark:text-gray-400\" />\n            <span className=\"text-sm font-medium text-gray-800 dark:text-gray-300\">Next Actions</span>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n              <span className=\"text-xs text-gray-700 dark:text-gray-400\">Expand successful product lines</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n              <span className=\"text-xs text-gray-700 dark:text-gray-400\">Implement customer feedback system</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-yellow-500 rounded-full\"></div>\n              <span className=\"text-xs text-gray-700 dark:text-gray-400\">Optimize inventory turnover</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAsDe,SAAS,YAAY,EAAE,KAAK,EAAoB;IAC7D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,WAAW,EAAE;QACb,UAAU,EAAE;QACZ,cAAc,EAAE;QAChB,WAAW,EAAE;QACb,oBAAoB;YAClB,SAAS;gBAAE,SAAS;gBAAG,UAAU;gBAAG,QAAQ;YAAE;YAC9C,WAAW;gBAAE,SAAS;gBAAG,UAAU;gBAAG,QAAQ;YAAE;YAChD,UAAU;gBAAE,SAAS;gBAAG,UAAU;gBAAG,QAAQ;YAAE;YAC/C,YAAY;gBAAE,SAAS;gBAAG,UAAU;gBAAG,QAAQ;YAAE;QACnD;IACF;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,WAAW;QACX,WAAW;QACX,UAAU;QACV,YAAY;QACZ,iBAAiB;IACnB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,8DAA8D;IAC9D,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,aAAa;QAEb,uDAAuD;QACvD,MAAM,oBAAoB;YACxB,MAAM,YAAY;YAClB,MAAM,sBAAsB;gBAAC;gBAAK;gBAAM;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAM;gBAAM;gBAAM;gBAAM;aAAI;YAC7F,OAAO,oBAAoB,GAAG,CAAC,CAAC,YAAY;gBAC1C,MAAM,kBAAkB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAChD,OAAO,KAAK,KAAK,CAAC,YAAY,aAAa,CAAC,IAAI,eAAe;YACjE;QACF;QAEA,0CAA0C;QAC1C,MAAM,mBAAmB;YACvB,MAAM,WAAW;YACjB,MAAM,gBAAgB;gBAAC;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;aAAI,CAAC,kBAAkB;;YAC5E,OAAO,cAAc,GAAG,CAAC,CAAA;gBACvB,MAAM,kBAAkB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAChD,OAAO,KAAK,KAAK,CAAC,WAAW,aAAa,CAAC,IAAI,eAAe;YAChE;QACF;QAEA,sCAAsC;QACtC,MAAM,uBAAuB;YAC3B,MAAM,aAAa;gBACjB;oBAAE,MAAM;oBAAa,OAAO;oBAAI,OAAO;gBAAU;gBACjD;oBAAE,MAAM;oBAAU,OAAO;oBAAI,OAAO;gBAAU;gBAC9C;oBAAE,MAAM;oBAAa,OAAO;oBAAI,OAAO;gBAAU;gBACjD;oBAAE,MAAM;oBAAiB,OAAO;oBAAI,OAAO;gBAAU;gBACrD;oBAAE,MAAM;oBAAU,OAAO;oBAAG,OAAO;gBAAU;aAC9C;YAED,IAAI,qBAAqB,WAAW,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC9C,GAAG,GAAG;oBACN,OAAO,IAAI,KAAK,GAAG,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACxD,CAAC;YAED,oCAAoC;YACpC,IAAI,qBAAqB,OAAO;gBAC9B,qBAAqB,mBAAmB,MAAM,CAAC,CAAA,MAC7C,IAAI,IAAI,CAAC,WAAW,OAAO,iBAAiB,WAAW;YAE3D;YAEA,OAAO;QACT;QAEA,6CAA6C;QAC7C,MAAM,oBAAoB;YACxB,MAAM,SAAS;gBAAC;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;aAAM;YACnG,OAAO,OAAO,GAAG,CAAC,CAAC,OAAO;gBACxB,MAAM,QAAQ,QAAS,QAAQ,OAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAClE,MAAM,OAAO,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAC/C,MAAM,SAAS,QAAQ,MAAM,OAAO;gBACpC,OAAO;oBAAE;oBAAO;oBAAO;oBAAM;gBAAO;YACtC;QACF;QAEA,iDAAiD;QACjD,MAAM,8BAA8B,CAAC,WAAqB;YACxD,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;YACzD,MAAM,kBAAkB,iBAAiB,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;YAEpE,OAAO;gBACL,SAAS;oBACP,SAAS;oBACT,UAAU;oBACV,QAAQ,AAAC,CAAC,iBAAiB,eAAe,IAAI,kBAAmB;gBACnE;gBACA,WAAW;oBACT,SAAS,aAAa,UAAU;oBAChC,UAAU,KAAK,KAAK,CAAC,aAAa,UAAU,GAAG,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG;oBACzE,QAAQ,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC7C;gBACA,UAAU;oBACR,SAAS,aAAa,aAAa;oBACnC,UAAU,KAAK,KAAK,CAAC,aAAa,aAAa,GAAG,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;oBAC7E,QAAQ,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC7C;gBACA,YAAY;oBACV,SAAS,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oBACzC,UAAU,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oBAC1C,QAAQ,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC7C;YACF;QACF;QAEA,sDAAsD;QACtD,WAAW;YACT,MAAM,YAAY;YAClB,MAAM,WAAW;YACjB,MAAM,eAAe;YACrB,MAAM,YAAY;YAClB,MAAM,qBAAqB,4BAA4B,WAAW;YAElE,aAAa;gBACX;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,eAAe,IAAI;YACnB,aAAa;QACf,GAAG;IACL,GAAG;QAAC;KAAM;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAsB,QAAQ,SAAS;QAAE;KAAiB;IAE9D,mDAAmD;IACnD,MAAM,gBAAgB,IAAM,CAAC;YAC3B,iBAAiB,kBAAkB,SAAS,YAAY;YACxD,WAAW;gBACT,OAAO,kBAAkB,SAAS,YAAY;gBAC9C,YAAY;YACd;YACA,MAAM;gBACJ,aAAa,kBAAkB,SAAS,YAAY;YACtD;QACF,CAAC;IAED,8CAA8C;IAC9C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACtC,GAAG,eAAe;YAClB,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,UAAU;oBACV,YAAY;oBACZ,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,MAAM;gBACN,KAAK;YACP;YACA,SAAS;gBACP,SAAS;gBACT,iBAAiB,kBAAkB,SAAS,YAAY;gBACxD,aAAa,kBAAkB,SAAS,YAAY;gBACpD,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,WAAW,CAAC;oBACV,MAAM,OAAO,MAAM,CAAC,EAAE;oBACtB,OAAO,CAAC;;gEAEgD,EAAE,KAAK,IAAI,CAAC;;iEAEX,EAAE,KAAK,KAAK,CAAC;wBACtD,EAAE,KAAK,KAAK,CAAC,cAAc,GAAG;;;cAGxC,EAAE,KAAK,KAAK,GAAG,CAAC,UAAU,SAAS,CAAC,KAAK,SAAS,GAAG,EAAE,IAAI,CAAC,IAAI,iBAAiB,eAAe;;;QAGtG,CAAC;gBACH;YACF;YACA,QAAQ;gBACN,MAAM;gBACN,KAAK;gBACL,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;YACF;YACA,OAAO;gBACL,MAAM;gBACN,MAAM;oBAAC;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;iBAAM;gBAC1F,UAAU;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;oBAC9C,UAAU;gBACZ;YACF;YACA,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,WAAW;oBACX,OAAO,kBAAkB,SAAS,YAAY;oBAC9C,UAAU;gBACZ;gBACA,UAAU;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,WAAW;oBACT,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,MAAM;oBACR;gBACF;YACF;YACA,QAAQ;gBACN;oBACE,MAAM;oBACN,MAAM,UAAU,SAAS;oBACzB,MAAM,QAAQ,SAAS;oBACvB,QAAQ;oBACR,WAAW;wBACT,OAAO;wBACP,OAAO;oBACT;oBACA,WAAW;wBACT,OAAO;wBACP,cAAc,QAAQ,SAAS,KAAK,QAAQ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE,GAAG;oBAC7D;oBACA,WAAW,QAAQ,SAAS,KAAK,SAAS;wBACxC,OAAO;4BACL,MAAM;4BACN,GAAG;4BACH,GAAG;4BACH,IAAI;4BACJ,IAAI;4BACJ,YAAY;gCACV;oCAAE,QAAQ;oCAAG,OAAO;gCAAyB;gCAC7C;oCAAE,QAAQ;oCAAG,OAAO;gCAA0B;6BAC/C;wBACH;oBACF,IAAI;oBACJ,UAAU;wBACR,OAAO;wBACP,WAAW;4BACT,YAAY;4BACZ,aAAa;wBACf;oBACF;oBACA,WAAW;wBACT,MAAM;4BACJ;gCAAE,MAAM;gCAAO,MAAM;4BAAM;4BAC3B;gCAAE,MAAM;gCAAO,MAAM;4BAAM;yBAC5B;wBACD,WAAW;4BACT,OAAO;wBACT;oBACF;oBACA,UAAU,QAAQ,UAAU,GAAG;wBAC7B,MAAM;4BACJ;gCAAE,MAAM;gCAAW,MAAM;4BAAU;yBACpC;wBACD,WAAW;4BACT,OAAO;4BACP,MAAM;wBACR;oBACF,IAAI;gBACN;aACD;YACD,MAAM;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,KAAK;gBACL,cAAc;YAChB;YACA,UAAU;gBACR;oBACE,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,YAAY;oBACZ,YAAY;oBACZ,aAAa;wBACX,OAAO;wBACP,YAAY;wBACZ,aAAa;wBACb,eAAe;wBACf,eAAe;oBACjB;gBACF;aACD;YACD,SAAS;gBACP,SAAS;oBACP,UAAU;wBACR,YAAY;oBACd;oBACA,SAAS,CAAC;oBACV,aAAa;wBACX,YAAY;oBACd;gBACF;gBACA,WAAW;oBACT,aAAa,kBAAkB,SAAS,YAAY;gBACtD;YACF;QACF,CAAC,GAAG;QAAC,UAAU,SAAS;QAAE,QAAQ,SAAS;QAAE;KAAc;IAE3D,6CAA6C;IAC7C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACrC,GAAG,eAAe;YAClB,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,UAAU;oBACV,YAAY;oBACZ,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,MAAM;gBACN,KAAK;YACP;YACA,SAAS;gBACP,SAAS;gBACT,iBAAiB,kBAAkB,SAAS,YAAY;gBACxD,aAAa,kBAAkB,SAAS,YAAY;gBACpD,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,WAAW,CAAC;oBACV,MAAM,OAAO,MAAM,CAAC,EAAE;oBACtB,MAAM,aAAa,CAAC,AAAC,KAAK,KAAK,GAAG,UAAU,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAW,IAAc,IAAI,GAAG,KAAM,GAAG,EAAE,OAAO,CAAC;oBAChH,OAAO,CAAC;;gEAEgD,EAAE,KAAK,IAAI,CAAC;;iEAEX,EAAE,KAAK,KAAK,CAAC;2BACnD,EAAE,KAAK,KAAK,CAAC,cAAc,GAAG;;;cAG3C,EAAE,WAAW;;;QAGnB,CAAC;gBACH;YACF;YACA,QAAQ;gBACN,MAAM;gBACN,KAAK;gBACL,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;YACF;YACA,OAAO;gBACL,MAAM;gBACN,MAAM;oBAAC;oBAAU;oBAAW;oBAAa;oBAAY;oBAAU;oBAAY;iBAAS;gBACpF,UAAU;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;oBAC9C,UAAU;oBACV,QAAQ;gBACV;YACF;YACA,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,WAAW;oBACX,OAAO,kBAAkB,SAAS,YAAY;oBAC9C,UAAU;gBACZ;gBACA,UAAU;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,WAAW;oBACT,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,MAAM;oBACR;gBACF;YACF;YACA,QAAQ;gBACN;oBACE,MAAM;oBACN,MAAM,UAAU,QAAQ;oBACxB,MAAM;oBACN,WAAW;wBACT,OAAO;4BACL,MAAM;4BACN,GAAG;4BACH,GAAG;4BACH,IAAI;4BACJ,IAAI;4BACJ,YAAY;gCACV;oCAAE,QAAQ;oCAAG,OAAO;gCAAU;gCAC9B;oCAAE,QAAQ;oCAAG,OAAO;gCAAU;6BAC/B;wBACH;wBACA,cAAc;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;oBAC5B;oBACA,UAAU;wBACR,OAAO;wBACP,WAAW;4BACT,OAAO;4BACP,YAAY;4BACZ,aAAa;wBACf;oBACF;oBACA,WAAW;wBACT,MAAM;4BACJ;gCAAE,MAAM;gCAAO,MAAM;4BAAW;4BAChC;gCAAE,MAAM;gCAAO,MAAM;4BAAU;yBAChC;wBACD,WAAW;4BACT,OAAO;wBACT;oBACF;gBACF;aACD;YACD,MAAM;gBACJ,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,KAAK;gBACL,cAAc;YAChB;YACA,SAAS;gBACP,SAAS;oBACP,UAAU;wBACR,YAAY;oBACd;oBACA,SAAS,CAAC;oBACV,aAAa;wBACX,YAAY;oBACd;gBACF;gBACA,WAAW;oBACT,aAAa,kBAAkB,SAAS,YAAY;gBACtD;YACF;QACF,CAAC,GAAG;QAAC,UAAU,QAAQ;QAAE;KAAc;IAEvC,wCAAwC;IACxC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACzC,GAAG,eAAe;YAClB,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,UAAU;oBACV,YAAY;oBACZ,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,MAAM;gBACN,KAAK;YACP;YACA,SAAS;gBACP,SAAS;gBACT,iBAAiB,kBAAkB,SAAS,YAAY;gBACxD,aAAa,kBAAkB,SAAS,YAAY;gBACpD,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,WAAW,CAAC;oBACV,OAAO,CAAC;;gEAEgD,EAAE,OAAO,IAAI,CAAC;;iEAEb,EAAE,OAAO,KAAK,CAAC;qBAC3D,EAAE,OAAO,KAAK,CAAC;;;cAGtB,EAAE,OAAO,OAAO,CAAC;;;QAGvB,CAAC;gBACH;YACF;YACA,QAAQ;gBACN,QAAQ;gBACR,QAAQ;gBACR,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;oBAC9C,UAAU;gBACZ;YACF;YACA,MAAM;gBACJ,KAAK;gBACL,QAAQ;gBACR,MAAM;gBACN,OAAO;gBACP,cAAc;YAChB;YACA,QAAQ;gBACN;oBACE,MAAM;oBACN,MAAM;oBACN,QAAQ;wBAAC;wBAAO;qBAAM;oBACtB,QAAQ;wBAAC;wBAAO;qBAAM;oBACtB,mBAAmB;oBACnB,WAAW;wBACT,cAAc;wBACd,aAAa,kBAAkB,SAAS,YAAY;wBACpD,aAAa;oBACf;oBACA,OAAO;wBACL,MAAM;wBACN,UAAU;wBACV,WAAW;wBACX,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,UAAU;wBACV,YAAY;wBACZ,qBAAqB;wBACrB,SAAS;4BAAC;4BAAG;yBAAE;wBACf,iBAAiB,kBAAkB,SAAS,0BAA0B;wBACtE,aAAa,kBAAkB,SAAS,6BAA6B;wBACrE,aAAa;wBACb,cAAc;oBAChB;oBACA,UAAU;wBACR,WAAW;4BACT,YAAY;4BACZ,eAAe;4BACf,aAAa;wBACf;wBACA,OAAO;4BACL,MAAM;4BACN,UAAU;4BACV,YAAY;wBACd;oBACF;oBACA,WAAW;wBACT,MAAM;wBACN,QAAQ;wBACR,SAAS;wBACT,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;4BAC9C,OAAO;wBACT;oBACF;oBACA,MAAM,UAAU,YAAY;gBAC9B;aACD;QACH,CAAC,GAAG;QAAC,UAAU,YAAY;QAAE;KAAc;IAE3C,mDAAmD;IACnD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACjC,MAAM,QAAQ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAG,GAAG,CAAC,GAAG,IAAM,GAAG,EAAE,GAAG,CAAC;QAC5D,MAAM,OAAO;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;SAAM;QAC9D,MAAM,cAAc,EAAE;QAEtB,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;YAChC,IAAK,IAAI,OAAO,GAAG,OAAO,IAAI,OAAQ;gBACpC,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;gBAChD,YAAY,IAAI,CAAC;oBAAC;oBAAM;oBAAK;iBAAM;YACrC;QACF;QAEA,OAAO;YACL,GAAG,eAAe;YAClB,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,UAAU;oBACV,YAAY;oBACZ,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,MAAM;gBACN,KAAK;YACP;YACA,SAAS;gBACP,UAAU;gBACV,iBAAiB,kBAAkB,SAAS,YAAY;gBACxD,aAAa,kBAAkB,SAAS,YAAY;gBACpD,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,WAAW,CAAC;oBACV,OAAO,CAAC;;kEAEgD,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;;mEAE/C,EAAE,OAAO,KAAK,CAAC;gCAClD,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;;;UAGvC,CAAC;gBACH;YACF;YACA,MAAM;gBACJ,QAAQ;gBACR,KAAK;YACP;YACA,OAAO;gBACL,MAAM;gBACN,MAAM;gBACN,WAAW;oBACT,MAAM;gBACR;gBACA,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;oBAC9C,UAAU;gBACZ;YACF;YACA,OAAO;gBACL,MAAM;gBACN,MAAM;gBACN,WAAW;oBACT,MAAM;gBACR;gBACA,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;oBAC9C,UAAU;gBACZ;YACF;YACA,WAAW;gBACT,KAAK;gBACL,KAAK;gBACL,YAAY;gBACZ,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,SAAS;oBACP,OAAO;wBAAC;wBAAW;wBAAW;wBAAW;wBAAW;wBAAW;wBAAW;wBAAW;wBAAW;wBAAW;wBAAW;qBAAU;gBAClI;gBACA,WAAW;oBACT,OAAO,kBAAkB,SAAS,YAAY;gBAChD;YACF;YACA,QAAQ;gBAAC;oBACP,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,OAAO;wBACL,MAAM;oBACR;oBACA,UAAU;wBACR,WAAW;4BACT,YAAY;4BACZ,aAAa;wBACf;oBACF;gBACF;aAAE;QACJ;IACF,GAAG;QAAC;KAAc;IAElB,sCAAsC;IACtC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACtC,GAAG,eAAe;YAClB,OAAO;gBACL,MAAM;gBACN,WAAW;oBACT,UAAU;oBACV,YAAY;oBACZ,OAAO,kBAAkB,SAAS,YAAY;gBAChD;gBACA,MAAM;gBACN,KAAK;YACP;YACA,QAAQ;gBACN;oBACE,MAAM;oBACN,MAAM;oBACN,QAAQ;wBAAC;wBAAO;qBAAM;oBACtB,YAAY;oBACZ,UAAU,CAAC;oBACX,KAAK;oBACL,KAAK;oBACL,aAAa;oBACb,WAAW;wBACT,OAAO;oBACT;oBACA,UAAU;wBACR,MAAM;wBACN,OAAO;oBACT;oBACA,SAAS;wBACP,MAAM;oBACR;oBACA,UAAU;wBACR,WAAW;4BACT,OAAO;4BACP,OAAO;gCACL;oCAAC;oCAAK;iCAAU;gCAChB;oCAAC;oCAAK;iCAAU;gCAChB;oCAAC;oCAAG;iCAAU;6BACf;wBACH;oBACF;oBACA,UAAU;wBACR,UAAU,CAAC;wBACX,aAAa;wBACb,WAAW;4BACT,OAAO;4BACP,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,UAAU,CAAC;wBACX,QAAQ;wBACR,WAAW;4BACT,OAAO;4BACP,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,UAAU,CAAC;wBACX,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,UAAU;oBACZ;oBACA,QAAQ;wBACN,MAAM;oBACR;oBACA,OAAO;wBACL,MAAM;oBACR;oBACA,QAAQ;wBACN,gBAAgB;wBAChB,OAAO;wBACP,YAAY;wBACZ,cAAc;wBACd,cAAc;4BAAC;4BAAG;yBAAO;wBACzB,UAAU;wBACV,YAAY;wBACZ,WAAW;wBACX,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,MAAM;wBACJ;4BACE,OAAO,UAAU,kBAAkB,CAAC,UAAU,CAAC,OAAO;4BACtD,MAAM;wBACR;qBACD;gBACH;aACD;QACH,CAAC,GAAG;QAAC,UAAU,kBAAkB,CAAC,UAAU,CAAC,OAAO;QAAE;KAAc;IAEpE,kDAAkD;IAClD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,MAAM,eAAe,UAAU,SAAS,CAAC,MAAM,CAAC,CAAC,GAAW,IAAc,IAAI,GAAG;QACjF,MAAM,oBAAoB,eAAe,UAAU,SAAS,CAAC,MAAM;QACnE,MAAM,YAAY,UAAU,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAW,IAAc,IAAI,GAAG;QAC7E,MAAM,eAAe,YAAY,UAAU,QAAQ,CAAC,MAAM;QAE1D,OAAO;YACL;gBACE,OAAO;gBACP,OAAO,MAAM,aAAa,cAAc;gBACxC,MAAM,kNAAA,CAAA,aAAU;gBAChB,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,OAAO;gBACP,UAAU,CAAC,MAAM,EAAE,kBAAkB,cAAc,GAAG,MAAM,CAAC;YAC/D;YACA;gBACE,OAAO;gBACP,OAAO,UAAU,kBAAkB,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ;gBAC9D,MAAM,oMAAA,CAAA,QAAK;gBACX,OAAO;gBACP,SAAS;gBACT,QAAQ,GAAG,UAAU,kBAAkB,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,MAAM,KAAK,UAAU,kBAAkB,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACrI,aAAa,UAAU,kBAAkB,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,uCAAuC;gBACxG,OAAO,UAAU,kBAAkB,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,OAAO;gBAClE,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,OAAO,UAAU,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ;gBAC7D,MAAM,wMAAA,CAAA,UAAO;gBACb,OAAO;gBACP,SAAS;gBACT,QAAQ,GAAG,UAAU,kBAAkB,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,MAAM,KAAK,UAAU,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACnI,aAAa,UAAU,kBAAkB,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,uCAAuC;gBACvG,OAAO,UAAU,kBAAkB,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,OAAO;gBACjE,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,OAAO,GAAG,UAAU,kBAAkB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC5D,MAAM,sMAAA,CAAA,SAAM;gBACZ,OAAO;gBACP,SAAS;gBACT,QAAQ,GAAG,UAAU,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,MAAM,KAAK,UAAU,kBAAkB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACvI,aAAa,UAAU,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,uCAAuC;gBACzG,OAAO,UAAU,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,OAAO;gBACnE,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,OAAO,MAAM,aAAa,cAAc;gBACxC,MAAM,sNAAA,CAAA,eAAY;gBAClB,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,OAAO;gBACP,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,OAAO,GAAG,CAAC,AAAC,eAAe,CAAC,eAAe,IAAI,IAAK,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC5E,MAAM,kNAAA,CAAA,aAAU;gBAChB,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,OAAO;gBACP,UAAU;YACZ;SACD;IACH,GAAG;QAAC;QAAW,MAAM,eAAe;KAAC;IAErC,4BAA4B;IAC5B,MAAM,iBAAiB,kBACrB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;0CAGzE,8OAAC;gCACC,OAAO,QAAQ,SAAS;gCACxB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAA+B,CAAC;gCACzG,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;0CAGvB,8OAAC;gCACC,OAAO,QAAQ,SAAS;gCACxB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAA+B,CAAC;gCACzG,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;;;;;;;kCAIzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,iBAAiB,IAAI;;;;;;kDAClE,8OAAC;kDAAM,YAAY,gBAAgB;;;;;;;;;;;;0CAGrC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOhB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;;;;0BAGD,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,8OAAC;wBAAgB,WAAU;kCACzB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,eAAe,EAAE,IAAI,OAAO,CAAC,wDAAwD,CAAC;sDACrG,cAAA,8OAAC,IAAI,IAAI;gDAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,KAAK,EAAE;;;;;;;;;;;sDAE7C,8OAAC;4CAAI,WAAU;;gDACZ,IAAI,KAAK,KAAK,sBAAQ,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDACzC,IAAI,KAAK,KAAK,wBAAU,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAC7C,IAAI,KAAK,KAAK,2BAAa,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAIjD,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDACV,IAAI,KAAK;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;sDACV,IAAI,KAAK;;;;;;wCAEX,IAAI,QAAQ,kBACX,8OAAC;4CAAE,WAAU;sDACV,IAAI,QAAQ;;;;;;sDAGjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAW,CAAC,oBAAoB,EAAE,IAAI,WAAW,EAAE;8DACtD,IAAI,MAAM;;;;;;8DAEb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;uBA7BzD;;;;;;;;;;0BAwCd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAG5D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;4BAIhE,0BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;qDAI5D,8OAAC,uJAAA,CAAA,UAAY;gCACX,QAAQ;gCACR,OAAO;oCAAE,QAAQ,WAAW,UAAU;gCAAQ;;;;;;;;;;;;kCAMpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAG5D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;4BAIhE,0BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;qDAI5D,8OAAC,uJAAA,CAAA,UAAY;gCACX,QAAQ;gCACR,OAAO;oCAAE,QAAQ,WAAW,UAAU;gCAAQ;;;;;;;;;;;;;;;;;;0BAOtD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAG5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAyD;;;;;;;;;;;;0DAE3E,8OAAC;gDAAO,WAAU;gDAA2F,OAAM;0DACjH,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,8OAAC;gDAAO,WAAU;gDAA2F,OAAM;0DACjH,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ;4CACC;gDAAE,KAAK;gDAAO,OAAO;gDAAkB,OAAO;gDAAiC,WAAW;4CAA0C;4CACpI;gDAAE,KAAK;gDAAa,OAAO;gDAAa,OAAO;gDAAmC,WAAW;4CAA4C;4CACzI;gDAAE,KAAK;gDAAU,OAAO;gDAAU,OAAO;gDAAiC,WAAW;4CAA0C;4CAC/H;gDAAE,KAAK;gDAAa,OAAO;gDAAa,OAAO;gDAAqC,WAAW;4CAA8C;4CAC7I;gDAAE,KAAK;gDAAiB,OAAO;gDAAiB,OAAO;gDAA+B,WAAW;4CAAwC;4CACzI;gDAAE,KAAK;gDAAU,OAAO;gDAAU,OAAO;gDAAqC,WAAW;4CAA8C;yCACxI,CAAC,GAAG,CAAC,CAAC,yBACL,8OAAC;gDAEC,SAAS,IAAM,oBAAoB,SAAS,GAAG;gDAC/C,WAAW,CAAC,0OAA0O,EAAE,SAAS,KAAK,CAAC,CAAC,EAAE,SAAS,SAAS,CAAC,CAAC,EAC5R,qBAAqB,SAAS,GAAG,GAC7B,6EACA,gCACJ;gDACF,OAAO;oDACL,WAAW,qBAAqB,SAAS,GAAG,GACxC,uEACA;oDACJ,UAAU,WAAW,SAAS;gDAChC;0DAEA,cAAA,8OAAC;oDAAK,WAAU;8DAAkB,SAAS,KAAK;;;;;;+CAd3C,SAAS,GAAG;;;;;;;;;;kDAkBvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;4BAM3D,0BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;qDAI5D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,uJAAA,CAAA,UAAY;oCACX,QAAQ;oCACR,OAAO;wCAAE,QAAQ,WAAW,UAAU;oCAAQ;oCAC9C,MAAM;wCAAE,UAAU;oCAAM;;;;;;;;;;;;;;;;;kCAOhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAG5D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;4BAIhE,0BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;qDAI5D,8OAAC,uJAAA,CAAA,UAAY;gCACX,QAAQ;gCACR,OAAO;oCAAE,QAAQ,WAAW,UAAU;gCAAQ;;;;;;;;;;;;;;;;;;0BAOtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAG5D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;oBAIhE,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;6CAI5D,8OAAC,uJAAA,CAAA,UAAY;wBACX,QAAQ;wBACR,OAAO;4BAAE,QAAQ,WAAW,UAAU;wBAAQ;;;;;;;;;;;;0BAMpD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAuD;;;;;;;;;;;;;;;;;;0CAG3E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;wDAAK,WAAU;kEAAuD;;;;;;;;;;;;0DAEzE,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;0DAGxD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAA+B,OAAO;wDAAE,OAAO;oDAAM;;;;;;;;;;;;;;;;;kDAIxE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAyD;;;;;;;;;;;;0DAE3E,8OAAC;gDAAE,WAAU;0DAA6C;;;;;;0DAG1D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAgC,OAAO;wDAAE,OAAO;oDAAM;;;;;;;;;;;;;;;;;kDAIzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,8OAAC;wDAAK,WAAU;kEAA2D;;;;;;;;;;;;0DAE7E,8OAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAG5D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAiC,OAAO;wDAAE,OAAO;oDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;;;;;;;0CAG7E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAK,WAAU;kEAA6C;;;;;;;;;;;;;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;kDAG/D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,YAAY,kBAAkB;;;;;;;;;;;;;;;;;kDAIrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOvE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAuD;;;;;;;;;;;;;;;;;;0CAG3E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAgC,OAAO;gEAAE,OAAO;4DAAM;;;;;;;;;;;kEAEvE,8OAAC;wDAAK,WAAU;kEAA6C;;;;;;;;;;;;;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA+B,OAAO;gEAAE,OAAO;4DAAM;;;;;;;;;;;kEAEtE,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAG/D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAiC,OAAO;gEAAE,OAAO;4DAAM;;;;;;;;;;;kEAExE,8OAAC;wDAAK,WAAU;kEAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAG5D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;kCAKjE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;kDAE3E,8OAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;;0CAK5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAuD;;;;;;;;;;;;kDAEzE,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAK1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAK,WAAU;0DAA2D;;;;;;;;;;;;kDAE7E,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;;;;;;;0CAK9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAA2D;;;;;;;;;;;;kDAE7E,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;;;;;;;;;;;;;kCAMhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;0CAEzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzE", "debugId": null}}]}