{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/ThemeProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { ThemeProvider as NextThemesProvider } from 'next-themes'\nimport type { ComponentProps } from 'react'\n\ntype ThemeProviderProps = ComponentProps<typeof NextThemesProvider>\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAOO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, ReactNode } from 'react'\n\ninterface User {\n  id: string\n  email: string\n  name: string\n  role: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  login: (email: string, password: string) => Promise<boolean>\n  logout: () => void\n  isLoading: boolean\n  isAuthenticated: boolean\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // Check if user is logged in on app start\n    const checkAuth = () => {\n      const savedUser = localStorage.getItem('revantad_user')\n      if (savedUser) {\n        try {\n          const userData = JSON.parse(savedUser)\n          setUser(userData)\n        } catch (error) {\n          console.error('Error parsing saved user data:', error)\n          localStorage.removeItem('revantad_user')\n        }\n      }\n      setIsLoading(false)\n    }\n\n    checkAuth()\n  }, [])\n\n  const login = async (email: string, password: string): Promise<boolean> => {\n    setIsLoading(true)\n    \n    try {\n      // Simulate API call - replace with actual authentication\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      // Demo credentials\n      if (email === '<EMAIL>' && password === 'admin123') {\n        const userData: User = {\n          id: '1',\n          email: '<EMAIL>',\n          name: 'Admin User',\n          role: 'Store Owner'\n        }\n        \n        setUser(userData)\n        localStorage.setItem('revantad_user', JSON.stringify(userData))\n        setIsLoading(false)\n        return true\n      } else {\n        setIsLoading(false)\n        return false\n      }\n    } catch (error) {\n      console.error('Login error:', error)\n      setIsLoading(false)\n      return false\n    }\n  }\n\n  const logout = () => {\n    setUser(null)\n    localStorage.removeItem('revantad_user')\n  }\n\n  const value: AuthContextType = {\n    user,\n    login,\n    logout,\n    isLoading,\n    isAuthenticated: !!user\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAmBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0CAA0C;QAC1C,MAAM,YAAY;YAChB,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,WAAW;gBACb,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC;oBAC5B,QAAQ;gBACV,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAChD,aAAa,UAAU,CAAC;gBAC1B;YACF;YACA,aAAa;QACf;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,aAAa;QAEb,IAAI;YACF,yDAAyD;YACzD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,mBAAmB;YACnB,IAAI,UAAU,6BAA6B,aAAa,YAAY;gBAClE,MAAM,WAAiB;oBACrB,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBAEA,QAAQ;gBACR,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;gBACrD,aAAa;gBACb,OAAO;YACT,OAAO;gBACL,aAAa;gBACb,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,aAAa;YACb,OAAO;QACT;IACF;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/contexts/SettingsContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\n\n// Settings Types\nexport interface StoreSettings {\n  name: string\n  address: string\n  phone: string\n  email: string\n  website: string\n  currency: string\n  timezone: string\n  businessHours: {\n    open: string\n    close: string\n  }\n  operatingDays: string[]\n  businessRegistration: {\n    registrationNumber: string\n    taxId: string\n    businessType: string\n    registrationDate: string\n  }\n  locations: Array<{\n    id: number\n    name: string\n    address: string\n    phone: string\n    isMain: boolean\n  }>\n  branding: {\n    logo: string | null\n    primaryColor: string\n    secondaryColor: string\n    slogan: string\n  }\n}\n\nexport interface ProfileSettings {\n  firstName: string\n  lastName: string\n  email: string\n  phone: string\n  role: string\n  avatar: string | null\n  bio: string\n  dateOfBirth: string\n  address: string\n  emergencyContact: {\n    name: string\n    phone: string\n    relationship: string\n  }\n  preferences: {\n    language: string\n    timezone: string\n    dateFormat: string\n    numberFormat: string\n  }\n}\n\nexport interface NotificationSettings {\n  lowStock: boolean\n  newDebt: boolean\n  paymentReceived: boolean\n  dailyReport: boolean\n  weeklyReport: boolean\n  emailNotifications: boolean\n  smsNotifications: boolean\n  pushNotifications: boolean\n  channels: {\n    email: string\n    sms: string\n    webhook: string\n  }\n  customRules: Array<{\n    id: number\n    name: string\n    condition: string\n    action: string\n    enabled: boolean\n  }>\n  templates: {\n    lowStock: string\n    newDebt: string\n    paymentReceived: string\n  }\n}\n\nexport interface SecuritySettings {\n  twoFactorAuth: boolean\n  sessionTimeout: string\n  passwordExpiry: string\n  loginAttempts: string\n  currentPassword: string\n  newPassword: string\n  confirmPassword: string\n  apiKeys: Array<{\n    id: number\n    name: string\n    key: string\n    created: string\n    lastUsed: string\n    permissions: string[]\n  }>\n  loginHistory: Array<{\n    id: number\n    timestamp: string\n    ip: string\n    device: string\n    location: string\n    success: boolean\n  }>\n  passwordPolicy: {\n    minLength: number\n    requireUppercase: boolean\n    requireLowercase: boolean\n    requireNumbers: boolean\n    requireSymbols: boolean\n  }\n}\n\nexport interface AppearanceSettings {\n  theme: string\n  language: string\n  dateFormat: string\n  numberFormat: string\n  colorScheme: {\n    primary: string\n    secondary: string\n    accent: string\n    background: string\n    surface: string\n  }\n  layout: {\n    sidebarPosition: string\n    density: string\n    showAnimations: boolean\n    compactMode: boolean\n  }\n  typography: {\n    fontFamily: string\n    fontSize: string\n    fontWeight: string\n  }\n}\n\nexport interface BackupSettings {\n  autoBackup: boolean\n  backupFrequency: string\n  retentionDays: string\n  lastBackup: string\n  cloudStorage: {\n    provider: string\n    bucket: string\n    accessKey: string\n    secretKey: string\n  }\n  backupHistory: Array<{\n    id: number\n    timestamp: string\n    size: string\n    status: string\n    type: string\n  }>\n  verification: {\n    enabled: boolean\n    lastVerified: string\n    status: string\n  }\n}\n\nexport interface AllSettings {\n  store: StoreSettings\n  profile: ProfileSettings\n  notifications: NotificationSettings\n  security: SecuritySettings\n  appearance: AppearanceSettings\n  backup: BackupSettings\n}\n\ninterface SettingsContextType {\n  settings: AllSettings\n  updateSettings: (section: keyof AllSettings, newSettings: Partial<AllSettings[keyof AllSettings]>) => void\n  saveSettings: () => Promise<void>\n  resetSettings: (section?: keyof AllSettings) => void\n  isLoading: boolean\n  hasUnsavedChanges: boolean\n}\n\nconst SettingsContext = createContext<SettingsContextType | undefined>(undefined)\n\n// Default settings\nconst defaultSettings: AllSettings = {\n  store: {\n    name: 'Revantad Store',\n    address: '123 Barangay Street, Manila, Philippines',\n    phone: '+63 ************',\n    email: '<EMAIL>',\n    website: 'https://revantadstore.com',\n    currency: 'PHP',\n    timezone: 'Asia/Manila',\n    businessHours: { open: '06:00', close: '22:00' },\n    operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],\n    businessRegistration: {\n      registrationNumber: 'REG-2024-001',\n      taxId: 'TAX-*********',\n      businessType: 'Retail',\n      registrationDate: '2024-01-01',\n    },\n    locations: [{\n      id: 1,\n      name: 'Main Store',\n      address: '123 Barangay Street, Manila, Philippines',\n      phone: '+63 ************',\n      isMain: true,\n    }],\n    branding: {\n      logo: null,\n      primaryColor: '#22c55e',\n      secondaryColor: '#facc15',\n      slogan: 'Your Neighborhood Store',\n    },\n  },\n  profile: {\n    firstName: 'Admin',\n    lastName: 'User',\n    email: '<EMAIL>',\n    phone: '+63 ************',\n    role: 'Store Owner',\n    avatar: null,\n    bio: 'Experienced store owner managing Revantad Store operations.',\n    dateOfBirth: '1990-01-01',\n    address: '123 Barangay Street, Manila, Philippines',\n    emergencyContact: {\n      name: 'Emergency Contact',\n      phone: '+63 ************',\n      relationship: 'Family',\n    },\n    preferences: {\n      language: 'en',\n      timezone: 'Asia/Manila',\n      dateFormat: 'MM/DD/YYYY',\n      numberFormat: 'en-US',\n    },\n  },\n  notifications: {\n    lowStock: true,\n    newDebt: true,\n    paymentReceived: true,\n    dailyReport: false,\n    weeklyReport: true,\n    emailNotifications: true,\n    smsNotifications: false,\n    pushNotifications: true,\n    channels: {\n      email: '<EMAIL>',\n      sms: '+63 ************',\n      webhook: '',\n    },\n    customRules: [{\n      id: 1,\n      name: 'Critical Stock Alert',\n      condition: 'stock < 5',\n      action: 'email + sms',\n      enabled: true,\n    }],\n    templates: {\n      lowStock: 'Product {{productName}} is running low ({{currentStock}} remaining)',\n      newDebt: 'New debt recorded for {{customerName}}: ₱{{amount}}',\n      paymentReceived: 'Payment received from {{customerName}}: ₱{{amount}}',\n    },\n  },\n  security: {\n    twoFactorAuth: false,\n    sessionTimeout: '30',\n    passwordExpiry: '90',\n    loginAttempts: '5',\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n    apiKeys: [{\n      id: 1,\n      name: 'Main API Key',\n      key: 'sk_live_***************',\n      created: '2024-01-01',\n      lastUsed: '2024-01-20',\n      permissions: ['read', 'write'],\n    }],\n    loginHistory: [{\n      id: 1,\n      timestamp: '2024-01-20T10:30:00Z',\n      ip: '***********',\n      device: 'Chrome on Windows',\n      location: 'Manila, Philippines',\n      success: true,\n    }],\n    passwordPolicy: {\n      minLength: 8,\n      requireUppercase: true,\n      requireLowercase: true,\n      requireNumbers: true,\n      requireSymbols: true,\n    },\n  },\n  appearance: {\n    theme: 'light',\n    language: 'en',\n    dateFormat: 'MM/DD/YYYY',\n    numberFormat: 'en-US',\n    colorScheme: {\n      primary: '#22c55e',\n      secondary: '#facc15',\n      accent: '#3b82f6',\n      background: '#ffffff',\n      surface: '#f8fafc',\n    },\n    layout: {\n      sidebarPosition: 'left',\n      density: 'comfortable',\n      showAnimations: true,\n      compactMode: false,\n    },\n    typography: {\n      fontFamily: 'Inter',\n      fontSize: 'medium',\n      fontWeight: 'normal',\n    },\n  },\n  backup: {\n    autoBackup: true,\n    backupFrequency: 'daily',\n    retentionDays: '30',\n    lastBackup: '2024-01-20T10:30:00Z',\n    cloudStorage: {\n      provider: 'local',\n      bucket: '',\n      accessKey: '',\n      secretKey: '',\n    },\n    backupHistory: [\n      {\n        id: 1,\n        timestamp: '2024-01-20T10:30:00Z',\n        size: '2.5 MB',\n        status: 'completed',\n        type: 'automatic',\n      },\n      {\n        id: 2,\n        timestamp: '2024-01-19T10:30:00Z',\n        size: '2.4 MB',\n        status: 'completed',\n        type: 'automatic',\n      }\n    ],\n    verification: {\n      enabled: true,\n      lastVerified: '2024-01-20T10:35:00Z',\n      status: 'verified',\n    },\n  },\n}\n\nexport function SettingsProvider({ children }: { children: ReactNode }) {\n  const [settings, setSettings] = useState<AllSettings>(defaultSettings)\n  const [isLoading, setIsLoading] = useState(false)\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)\n\n  // Load settings from localStorage on mount\n  useEffect(() => {\n    const savedSettings = localStorage.getItem('revantad-settings')\n    if (savedSettings) {\n      try {\n        const parsed = JSON.parse(savedSettings)\n        setSettings({ ...defaultSettings, ...parsed })\n      } catch (error) {\n        console.error('Error loading settings:', error)\n      }\n    }\n  }, [])\n\n  const updateSettings = (section: keyof AllSettings, newSettings: Partial<AllSettings[keyof AllSettings]>) => {\n    setSettings(prev => ({\n      ...prev,\n      [section]: { ...prev[section], ...newSettings }\n    }))\n    setHasUnsavedChanges(true)\n  }\n\n  const saveSettings = async () => {\n    setIsLoading(true)\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      // Save to localStorage\n      localStorage.setItem('revantad-settings', JSON.stringify(settings))\n      \n      setHasUnsavedChanges(false)\n      \n      // Apply theme changes immediately\n      if (settings.appearance.theme === 'dark') {\n        document.documentElement.classList.add('dark')\n      } else {\n        document.documentElement.classList.remove('dark')\n      }\n      \n      console.warn('Settings saved successfully:', settings)\n    } catch (error) {\n      console.error('Error saving settings:', error)\n      throw error\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const resetSettings = (section?: keyof AllSettings) => {\n    if (section) {\n      setSettings(prev => ({\n        ...prev,\n        [section]: defaultSettings[section]\n      }))\n    } else {\n      setSettings(defaultSettings)\n    }\n    setHasUnsavedChanges(true)\n  }\n\n  return (\n    <SettingsContext.Provider value={{\n      settings,\n      updateSettings,\n      saveSettings,\n      resetSettings,\n      isLoading,\n      hasUnsavedChanges\n    }}>\n      {children}\n    </SettingsContext.Provider>\n  )\n}\n\nexport function useSettings() {\n  const context = useContext(SettingsContext)\n  if (context === undefined) {\n    throw new Error('useSettings must be used within a SettingsProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AA+LA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEvE,mBAAmB;AACnB,MAAM,kBAA+B;IACnC,OAAO;QACL,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;YAAE,MAAM;YAAS,OAAO;QAAQ;QAC/C,eAAe;YAAC;YAAU;YAAW;YAAa;YAAY;YAAU;SAAW;QACnF,sBAAsB;YACpB,oBAAoB;YACpB,OAAO;YACP,cAAc;YACd,kBAAkB;QACpB;QACA,WAAW;YAAC;gBACV,IAAI;gBACJ,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;SAAE;QACF,UAAU;YACR,MAAM;YACN,cAAc;YACd,gBAAgB;YAChB,QAAQ;QACV;IACF;IACA,SAAS;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,KAAK;QACL,aAAa;QACb,SAAS;QACT,kBAAkB;YAChB,MAAM;YACN,OAAO;YACP,cAAc;QAChB;QACA,aAAa;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,cAAc;QAChB;IACF;IACA,eAAe;QACb,UAAU;QACV,SAAS;QACT,iBAAiB;QACjB,aAAa;QACb,cAAc;QACd,oBAAoB;QACpB,kBAAkB;QAClB,mBAAmB;QACnB,UAAU;YACR,OAAO;YACP,KAAK;YACL,SAAS;QACX;QACA,aAAa;YAAC;gBACZ,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,QAAQ;gBACR,SAAS;YACX;SAAE;QACF,WAAW;YACT,UAAU;YACV,SAAS;YACT,iBAAiB;QACnB;IACF;IACA,UAAU;QACR,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,aAAa;QACb,iBAAiB;QACjB,SAAS;YAAC;gBACR,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,UAAU;gBACV,aAAa;oBAAC;oBAAQ;iBAAQ;YAChC;SAAE;QACF,cAAc;YAAC;gBACb,IAAI;gBACJ,WAAW;gBACX,IAAI;gBACJ,QAAQ;gBACR,UAAU;gBACV,SAAS;YACX;SAAE;QACF,gBAAgB;YACd,WAAW;YACX,kBAAkB;YAClB,kBAAkB;YAClB,gBAAgB;YAChB,gBAAgB;QAClB;IACF;IACA,YAAY;QACV,OAAO;QACP,UAAU;QACV,YAAY;QACZ,cAAc;QACd,aAAa;YACX,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,iBAAiB;YACjB,SAAS;YACT,gBAAgB;YAChB,aAAa;QACf;QACA,YAAY;YACV,YAAY;YACZ,UAAU;YACV,YAAY;QACd;IACF;IACA,QAAQ;QACN,YAAY;QACZ,iBAAiB;QACjB,eAAe;QACf,YAAY;QACZ,cAAc;YACZ,UAAU;YACV,QAAQ;YACR,WAAW;YACX,WAAW;QACb;QACA,eAAe;YACb;gBACE,IAAI;gBACJ,WAAW;gBACX,MAAM;gBACN,QAAQ;gBACR,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,MAAM;gBACN,QAAQ;gBACR,MAAM;YACR;SACD;QACD,cAAc;YACZ,SAAS;YACT,cAAc;YACd,QAAQ;QACV;IACF;AACF;AAEO,SAAS,iBAAiB,EAAE,QAAQ,EAA2B;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,eAAe;YACjB,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,YAAY;oBAAE,GAAG,eAAe;oBAAE,GAAG,MAAM;gBAAC;YAC9C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC,SAA4B;QAClD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;oBAAE,GAAG,IAAI,CAAC,QAAQ;oBAAE,GAAG,WAAW;gBAAC;YAChD,CAAC;QACD,qBAAqB;IACvB;IAEA,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,uBAAuB;YACvB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAEzD,qBAAqB;YAErB,kCAAkC;YAClC,IAAI,SAAS,UAAU,CAAC,KAAK,KAAK,QAAQ;gBACxC,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACzC,OAAO;gBACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5C;YAEA,QAAQ,IAAI,CAAC,gCAAgC;QAC/C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS;YACX,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBACrC,CAAC;QACH,OAAO;YACL,YAAY;QACd;QACA,qBAAqB;IACvB;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAC/B;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 459, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/next-themes/dist/index.mjs"], "sourcesContent": ["\"use client\";import*as t from\"react\";var M=(e,i,s,u,m,a,l,h)=>{let d=document.documentElement,w=[\"light\",\"dark\"];function p(n){(Array.isArray(e)?e:[e]).forEach(y=>{let k=y===\"class\",S=k&&a?m.map(f=>a[f]||f):m;k?(d.classList.remove(...S),d.classList.add(a&&a[n]?a[n]:n)):d.setAttribute(y,n)}),R(n)}function R(n){h&&w.includes(n)&&(d.style.colorScheme=n)}function c(){return window.matchMedia(\"(prefers-color-scheme: dark)\").matches?\"dark\":\"light\"}if(u)p(u);else try{let n=localStorage.getItem(i)||s,y=l&&n===\"system\"?c():n;p(y)}catch(n){}};var b=[\"light\",\"dark\"],I=\"(prefers-color-scheme: dark)\",O=typeof window==\"undefined\",x=t.createContext(void 0),U={setTheme:e=>{},themes:[]},z=()=>{var e;return(e=t.useContext(x))!=null?e:U},J=e=>t.useContext(x)?t.createElement(t.Fragment,null,e.children):t.createElement(V,{...e}),N=[\"light\",\"dark\"],V=({forcedTheme:e,disableTransitionOnChange:i=!1,enableSystem:s=!0,enableColorScheme:u=!0,storageKey:m=\"theme\",themes:a=N,defaultTheme:l=s?\"system\":\"light\",attribute:h=\"data-theme\",value:d,children:w,nonce:p,scriptProps:R})=>{let[c,n]=t.useState(()=>H(m,l)),[T,y]=t.useState(()=>c===\"system\"?E():c),k=d?Object.values(d):a,S=t.useCallback(o=>{let r=o;if(!r)return;o===\"system\"&&s&&(r=E());let v=d?d[r]:r,C=i?W(p):null,P=document.documentElement,L=g=>{g===\"class\"?(P.classList.remove(...k),v&&P.classList.add(v)):g.startsWith(\"data-\")&&(v?P.setAttribute(g,v):P.removeAttribute(g))};if(Array.isArray(h)?h.forEach(L):L(h),u){let g=b.includes(l)?l:null,D=b.includes(r)?r:g;P.style.colorScheme=D}C==null||C()},[p]),f=t.useCallback(o=>{let r=typeof o==\"function\"?o(c):o;n(r);try{localStorage.setItem(m,r)}catch(v){}},[c]),A=t.useCallback(o=>{let r=E(o);y(r),c===\"system\"&&s&&!e&&S(\"system\")},[c,e]);t.useEffect(()=>{let o=window.matchMedia(I);return o.addListener(A),A(o),()=>o.removeListener(A)},[A]),t.useEffect(()=>{let o=r=>{r.key===m&&(r.newValue?n(r.newValue):f(l))};return window.addEventListener(\"storage\",o),()=>window.removeEventListener(\"storage\",o)},[f]),t.useEffect(()=>{S(e!=null?e:c)},[e,c]);let Q=t.useMemo(()=>({theme:c,setTheme:f,forcedTheme:e,resolvedTheme:c===\"system\"?T:c,themes:s?[...a,\"system\"]:a,systemTheme:s?T:void 0}),[c,f,e,T,s,a]);return t.createElement(x.Provider,{value:Q},t.createElement(_,{forcedTheme:e,storageKey:m,attribute:h,enableSystem:s,enableColorScheme:u,defaultTheme:l,value:d,themes:a,nonce:p,scriptProps:R}),w)},_=t.memo(({forcedTheme:e,storageKey:i,attribute:s,enableSystem:u,enableColorScheme:m,defaultTheme:a,value:l,themes:h,nonce:d,scriptProps:w})=>{let p=JSON.stringify([s,i,a,e,h,l,u,m]).slice(1,-1);return t.createElement(\"script\",{...w,suppressHydrationWarning:!0,nonce:typeof window==\"undefined\"?d:\"\",dangerouslySetInnerHTML:{__html:`(${M.toString()})(${p})`}})}),H=(e,i)=>{if(O)return;let s;try{s=localStorage.getItem(e)||void 0}catch(u){}return s||i},W=e=>{let i=document.createElement(\"style\");return e&&i.setAttribute(\"nonce\",e),i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(i),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(i)},1)}},E=e=>(e||(e=window.matchMedia(I)),e.matches?\"dark\":\"light\");export{J as ThemeProvider,z as useTheme};\n"], "names": [], "mappings": ";;;;AAAa;AAAb;;AAAqC,IAAI,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,SAAS,eAAe,EAAC,IAAE;QAAC;QAAQ;KAAO;IAAC,SAAS,EAAE,CAAC;QAAE,CAAC,MAAM,OAAO,CAAC,KAAG,IAAE;YAAC;SAAE,EAAE,OAAO,CAAC,CAAA;YAAI,IAAI,IAAE,MAAI,SAAQ,IAAE,KAAG,IAAE,EAAE,GAAG,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,IAAE,KAAG;YAAE,IAAE,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,EAAE,SAAS,CAAC,GAAG,CAAC,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,EAAE,IAAE,EAAE,YAAY,CAAC,GAAE;QAAE,IAAG,EAAE;IAAE;IAAC,SAAS,EAAE,CAAC;QAAE,KAAG,EAAE,QAAQ,CAAC,MAAI,CAAC,EAAE,KAAK,CAAC,WAAW,GAAC,CAAC;IAAC;IAAC,SAAS;QAAI,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAC,SAAO;IAAO;IAAC,IAAG,GAAE,EAAE;SAAQ,IAAG;QAAC,IAAI,IAAE,aAAa,OAAO,CAAC,MAAI,GAAE,IAAE,KAAG,MAAI,WAAS,MAAI;QAAE,EAAE;IAAE,EAAC,OAAM,GAAE,CAAC;AAAC;AAAE,IAAI,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,gCAA+B,IAAE,OAAO,UAAQ,aAAY,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,KAAK,IAAG,IAAE;IAAC,UAAS,CAAA,KAAI;IAAE,QAAO,EAAE;AAAA,GAAE,IAAE;IAAK,IAAI;IAAE,OAAM,CAAC,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAY,AAAD,EAAE,EAAE,KAAG,OAAK,IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAA,GAAA,qMAAA,CAAA,aAAY,AAAD,EAAE,KAAG,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,qMAAA,CAAA,WAAU,EAAC,MAAK,EAAE,QAAQ,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,GAAG,CAAC;IAAA,IAAG,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,CAAC,EAAC,aAAY,CAAC,EAAC,2BAA0B,IAAE,CAAC,CAAC,EAAC,cAAa,IAAE,CAAC,CAAC,EAAC,mBAAkB,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,OAAO,EAAC,QAAO,IAAE,CAAC,EAAC,cAAa,IAAE,IAAE,WAAS,OAAO,EAAC,WAAU,IAAE,YAAY,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAU,AAAD,EAAE,IAAI,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAU,AAAD,EAAE,IAAI,MAAI,WAAS,MAAI,IAAG,IAAE,IAAE,OAAO,MAAM,CAAC,KAAG,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE;QAAE,IAAG,CAAC,GAAE;QAAO,MAAI,YAAU,KAAG,CAAC,IAAE,GAAG;QAAE,IAAI,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,GAAE,IAAE,IAAE,EAAE,KAAG,MAAK,IAAE,SAAS,eAAe,EAAC,IAAE,CAAA;YAAI,MAAI,UAAQ,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,KAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,IAAE,EAAE,UAAU,CAAC,YAAU,CAAC,IAAE,EAAE,YAAY,CAAC,GAAE,KAAG,EAAE,eAAe,CAAC,EAAE;QAAC;QAAE,IAAG,MAAM,OAAO,CAAC,KAAG,EAAE,OAAO,CAAC,KAAG,EAAE,IAAG,GAAE;YAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE,MAAK,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE;YAAE,EAAE,KAAK,CAAC,WAAW,GAAC;QAAC;QAAC,KAAG,QAAM;IAAG,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,OAAO,KAAG,aAAW,EAAE,KAAG;QAAE,EAAE;QAAG,IAAG;YAAC,aAAa,OAAO,CAAC,GAAE;QAAE,EAAC,OAAM,GAAE,CAAC;IAAC,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE;QAAG,EAAE,IAAG,MAAI,YAAU,KAAG,CAAC,KAAG,EAAE;IAAS,GAAE;QAAC;QAAE;KAAE;IAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,OAAO,UAAU,CAAC;QAAG,OAAO,EAAE,WAAW,CAAC,IAAG,EAAE,IAAG,IAAI,EAAE,cAAc,CAAC;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,CAAA;YAAI,EAAE,GAAG,KAAG,KAAG,CAAC,EAAE,QAAQ,GAAC,EAAE,EAAE,QAAQ,IAAE,EAAE,EAAE;QAAC;QAAE,OAAO,OAAO,gBAAgB,CAAC,WAAU,IAAG,IAAI,OAAO,mBAAmB,CAAC,WAAU;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,EAAE,KAAG,OAAK,IAAE;IAAE,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC;YAAC,OAAM;YAAE,UAAS;YAAE,aAAY;YAAE,eAAc,MAAI,WAAS,IAAE;YAAE,QAAO,IAAE;mBAAI;gBAAE;aAAS,GAAC;YAAE,aAAY,IAAE,IAAE,KAAK;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,aAAY;QAAE,YAAW;QAAE,WAAU;QAAE,cAAa;QAAE,mBAAkB;QAAE,cAAa;QAAE,OAAM;QAAE,QAAO;QAAE,OAAM;QAAE,aAAY;IAAC,IAAG;AAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,OAAM,AAAD,EAAE,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,WAAU,CAAC,EAAC,cAAa,CAAC,EAAC,mBAAkB,CAAC,EAAC,cAAa,CAAC,EAAC,OAAM,CAAC,EAAC,QAAO,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAI,IAAE,KAAK,SAAS,CAAC;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE,EAAE,KAAK,CAAC,GAAE,CAAC;IAAG,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,UAAS;QAAC,GAAG,CAAC;QAAC,0BAAyB,CAAC;QAAE,OAAM,OAAO,UAAQ,cAAY,IAAE;QAAG,yBAAwB;YAAC,QAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAAA;IAAC;AAAE,IAAG,IAAE,CAAC,GAAE;IAAK,IAAG,GAAE;IAAO,IAAI;IAAE,IAAG;QAAC,IAAE,aAAa,OAAO,CAAC,MAAI,KAAK;IAAC,EAAC,OAAM,GAAE,CAAC;IAAC,OAAO,KAAG;AAAC,GAAE,IAAE,CAAA;IAAI,IAAI,IAAE,SAAS,aAAa,CAAC;IAAS,OAAO,KAAG,EAAE,YAAY,CAAC,SAAQ,IAAG,EAAE,WAAW,CAAC,SAAS,cAAc,CAAC,iLAAgL,SAAS,IAAI,CAAC,WAAW,CAAC,IAAG;QAAK,OAAO,gBAAgB,CAAC,SAAS,IAAI,GAAE,WAAW;YAAK,SAAS,IAAI,CAAC,WAAW,CAAC;QAAE,GAAE;IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAC,KAAG,CAAC,IAAE,OAAO,UAAU,CAAC,EAAE,GAAE,EAAE,OAAO,GAAC,SAAO,OAAO", "ignoreList": [0], "debugId": null}}]}