'use client'

import { useState, useRef } from 'react'
import Image from 'next/image'

import {
  Save, User, Store, Bell, Shield, Palette, Database, Download, Upload,
  MapPin, Clock, Camera, Eye, EyeOff, Key, Smartphone, Globe,
  Mail, Phone, Trash2, Plus, Edit3, Check, AlertTriangle,
  Monitor, Sun, Moon, Palette as PaletteIcon, Type, Layout, Zap,
  Cloud, HardDrive, RefreshCw, Archive
} from 'lucide-react'

import { useSettings, StoreSettings, ProfileSettings, NotificationSettings, SecuritySettings, AppearanceSettings, BackupSettings } from '@/contexts/SettingsContext'

export default function Settings() {
  const [activeTab, setActiveTab] = useState('store')
  const [showPassword, setShowPassword] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { settings, updateSettings, saveSettings, isLoading, hasUnsavedChanges } = useSettings()

  const tabs = [
    { id: 'store', label: 'Store Info', icon: Store },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'backup', label: 'Backup', icon: Database },
  ]

  // Helper functions for updating nested settings
  const updateStoreSettings = (updates: Partial<StoreSettings>) => updateSettings('store', updates)
  const updateProfileSettings = (updates: Partial<ProfileSettings>) => updateSettings('profile', updates)
  const updateNotificationSettings = (updates: Partial<NotificationSettings>) => updateSettings('notifications', updates)
  const updateSecuritySettings = (updates: Partial<SecuritySettings>) => updateSettings('security', updates)
  const updateAppearanceSettings = (updates: Partial<AppearanceSettings>) => updateSettings('appearance', updates)
  const updateBackupSettings = (updates: Partial<BackupSettings>) => updateSettings('backup', updates)

  const handleSave = async () => {
    try {
      await saveSettings()
      alert('Settings saved successfully!')
    } catch (error) {
      console.error('Error saving settings:', error)
      alert('Error saving settings. Please try again.')
    }
  }

  const handleExportData = () => {
    console.warn('Exporting data...')
    alert('Data export started. You will receive an email when ready.')
  }

  const handleImportData = () => {
    console.warn('Importing data...')
    alert('Please select a backup file to import.')
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, type: 'logo' | 'avatar') => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        if (type === 'logo') {
          updateStoreSettings({ 
            branding: { ...settings.store.branding, logo: result } 
          })
        } else if (type === 'avatar') {
          updateProfileSettings({ avatar: result })
        }
      }
      reader.readAsDataURL(file)
    }
  }



  // Professional render functions for all sections
  const renderProfileSettings = () => (
    <div className="space-y-8">
      {/* Personal Information */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <User className="h-5 w-5 mr-2 text-blue-600" />
          Personal Information
        </h3>

        <div className="flex items-start space-x-6 mb-6">
          <div className="flex flex-col items-center space-y-3">
            <div className="w-24 h-24 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center bg-gray-50 dark:bg-slate-700 overflow-hidden">
              {settings.profile.avatar ? (
                <Image
                  src={settings.profile.avatar}
                  alt="Profile Avatar"
                  width={96}
                  height={96}
                  className="w-full h-full object-cover"
                />
              ) : (
                <User className="h-12 w-12 text-gray-400" />
              )}
            </div>
            <div className="flex flex-col space-y-2">
              <button
                onClick={() => {
                  const input = document.createElement('input')
                  input.type = 'file'
                  input.accept = 'image/*'
                  input.onchange = (e) => handleFileUpload(e as Event, 'avatar')
                  input.click()
                }}
                className="btn-outline text-sm px-3 py-1"
              >
                Upload Photo
              </button>
              {settings.profile.avatar && (
                <button
                  onClick={() => updateProfileSettings({ avatar: null })}
                  className="text-red-600 hover:text-red-700 text-sm"
                >
                  Remove
                </button>
              )}
            </div>
          </div>

          <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                First Name *
              </label>
              <input
                type="text"
                value={settings.profile.firstName}
                onChange={(e) => updateProfileSettings({ firstName: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="Enter first name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Last Name *
              </label>
              <input
                type="text"
                value={settings.profile.lastName}
                onChange={(e) => updateProfileSettings({ lastName: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="Enter last name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Email Address *
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <input
                  type="email"
                  value={settings.profile.email}
                  onChange={(e) => updateProfileSettings({ email: e.target.value })}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Phone Number
              </label>
              <div className="relative">
                <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <input
                  type="tel"
                  value={settings.profile.phone}
                  onChange={(e) => updateProfileSettings({ phone: e.target.value })}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                  placeholder="+63 ************"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Role
              </label>
              <select
                value={settings.profile.role}
                onChange={(e) => updateProfileSettings({ role: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
              >
                <option value="Store Owner">Store Owner</option>
                <option value="Manager">Manager</option>
                <option value="Cashier">Cashier</option>
                <option value="Staff">Staff</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Date of Birth
              </label>
              <input
                type="date"
                value={settings.profile.dateOfBirth}
                onChange={(e) => updateProfileSettings({ dateOfBirth: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Bio
            </label>
            <textarea
              value={settings.profile.bio}
              onChange={(e) => updateProfileSettings({ bio: e.target.value })}
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
              placeholder="Tell us about yourself..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Address
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <textarea
                value={settings.profile.address}
                onChange={(e) => updateProfileSettings({ address: e.target.value })}
                rows={2}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="Enter your address"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Emergency Contact */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
          Emergency Contact
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Contact Name
            </label>
            <input
              type="text"
              value={settings.profile.emergencyContact.name}
              onChange={(e) => updateProfileSettings({
                emergencyContact: { ...settings.profile.emergencyContact, name: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
              placeholder="Emergency contact name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Phone Number
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="tel"
                value={settings.profile.emergencyContact.phone}
                onChange={(e) => updateProfileSettings({
                  emergencyContact: { ...settings.profile.emergencyContact, phone: e.target.value }
                })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="+63 ************"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Relationship
            </label>
            <select
              value={settings.profile.emergencyContact.relationship}
              onChange={(e) => updateProfileSettings({
                emergencyContact: { ...settings.profile.emergencyContact, relationship: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            >
              <option value="Family">Family</option>
              <option value="Friend">Friend</option>
              <option value="Colleague">Colleague</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="space-y-8">
      {/* Basic Notification Preferences */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Bell className="h-5 w-5 mr-2 text-blue-600" />
          Alert Preferences
        </h3>
        <div className="space-y-4">
          {Object.entries(settings.notifications).filter(([key]) =>
            !['channels', 'customRules', 'templates'].includes(key)
          ).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
              <div className="flex-1">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {key === 'lowStock' && 'Get notified when products are running low'}
                  {key === 'newDebt' && 'Alert when new customer debt is recorded'}
                  {key === 'paymentReceived' && 'Notification for debt payments'}
                  {key === 'dailyReport' && 'Daily business summary report'}
                  {key === 'weeklyReport' && 'Weekly business analytics report'}
                  {key === 'emailNotifications' && 'Receive notifications via email'}
                  {key === 'smsNotifications' && 'Receive notifications via SMS'}
                  {key === 'pushNotifications' && 'Receive push notifications in browser'}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer ml-4">
                <input
                  type="checkbox"
                  checked={value as boolean}
                  onChange={(e) => updateNotificationSettings({ [key]: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"></div>
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Notification Channels */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Mail className="h-5 w-5 mr-2 text-green-600" />
          Delivery Channels
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Email Address
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="email"
                value={settings.notifications.channels.email}
                onChange={(e) => updateNotificationSettings({
                  channels: { ...settings.notifications.channels, email: e.target.value }
                })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              SMS Number
            </label>
            <div className="relative">
              <Smartphone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="tel"
                value={settings.notifications.channels.sms}
                onChange={(e) => updateNotificationSettings({
                  channels: { ...settings.notifications.channels, sms: e.target.value }
                })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="+63 ************"
              />
            </div>
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Webhook URL (Optional)
            </label>
            <div className="relative">
              <Globe className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="url"
                value={settings.notifications.channels.webhook}
                onChange={(e) => updateNotificationSettings({
                  channels: { ...settings.notifications.channels, webhook: e.target.value }
                })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="https://your-webhook-url.com/notifications"
              />
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Send notifications to external systems via webhook
            </p>
          </div>
        </div>
      </div>

      {/* Custom Notification Rules */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Zap className="h-5 w-5 mr-2 text-yellow-600" />
          Custom Rules
        </h3>
        <div className="space-y-4">
          {settings.notifications.customRules.map((rule) => (
            <div key={rule.id} className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">{rule.name}</h4>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    rule.enabled
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                    {rule.enabled ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="text-blue-600 hover:text-blue-700 p-1">
                    <Edit3 className="h-4 w-4" />
                  </button>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={rule.enabled}
                      onChange={(e) => {
                        const updatedRules = settings.notifications.customRules.map(r =>
                          r.id === rule.id ? { ...r, enabled: e.target.checked } : r
                        )
                        updateNotificationSettings({ customRules: updatedRules })
                      }}
                      className="sr-only peer"
                    />
                    <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"></div>
                  </label>
                </div>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <p><strong>Condition:</strong> {rule.condition}</p>
                <p><strong>Action:</strong> {rule.action}</p>
              </div>
            </div>
          ))}
          <button className="w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-green-500 hover:text-green-600 transition-colors flex items-center justify-center space-x-2">
            <Plus className="h-5 w-5" />
            <span>Add Custom Rule</span>
          </button>
        </div>
      </div>
    </div>
  )

  const renderSecuritySettings = () => (
    <div className="space-y-8">
      {/* Password Management */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Key className="h-5 w-5 mr-2 text-red-600" />
          Password Management
        </h3>

        <div className="grid grid-cols-1 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Current Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={settings.security.currentPassword}
                onChange={(e) => updateSecuritySettings({ currentPassword: e.target.value })}
                className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="Enter current password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
              >
                {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                New Password
              </label>
              <input
                type={showPassword ? 'text' : 'password'}
                value={settings.security.newPassword}
                onChange={(e) => updateSecuritySettings({ newPassword: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="Enter new password"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Confirm New Password
              </label>
              <input
                type={showPassword ? 'text' : 'password'}
                value={settings.security.confirmPassword}
                onChange={(e) => updateSecuritySettings({ confirmPassword: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="Confirm new password"
              />
            </div>
          </div>

          <button className="btn-primary w-fit">
            Update Password
          </button>
        </div>
      </div>

      {/* Two-Factor Authentication */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Smartphone className="h-5 w-5 mr-2 text-green-600" />
          Two-Factor Authentication
        </h3>

        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">Enable 2FA</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Add an extra layer of security to your account
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.security.twoFactorAuth}
              onChange={(e) => updateSecuritySettings({ twoFactorAuth: e.target.checked })}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"></div>
          </label>
        </div>

        {settings.security.twoFactorAuth && (
          <div className="mt-4 p-4 border border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="flex items-center space-x-3 mb-3">
              <Check className="h-5 w-5 text-green-600" />
              <span className="font-medium text-green-800 dark:text-green-300">2FA is enabled</span>
            </div>
            <div className="space-y-3">
              <button className="btn-outline text-sm">
                View Recovery Codes
              </button>
              <button className="btn-outline text-sm text-red-600 border-red-300 hover:bg-red-50">
                Disable 2FA
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Session Management */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Clock className="h-5 w-5 mr-2 text-purple-600" />
          Session Management
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Session Timeout (minutes)
            </label>
            <select
              value={settings.security.sessionTimeout}
              onChange={(e) => updateSecuritySettings({ sessionTimeout: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            >
              <option value="15">15 minutes</option>
              <option value="30">30 minutes</option>
              <option value="60">1 hour</option>
              <option value="120">2 hours</option>
              <option value="480">8 hours</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Password Expiry (days)
            </label>
            <select
              value={settings.security.passwordExpiry}
              onChange={(e) => updateSecuritySettings({ passwordExpiry: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            >
              <option value="30">30 days</option>
              <option value="60">60 days</option>
              <option value="90">90 days</option>
              <option value="180">180 days</option>
              <option value="365">1 year</option>
              <option value="0">Never</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Max Login Attempts
            </label>
            <select
              value={settings.security.loginAttempts}
              onChange={(e) => updateSecuritySettings({ loginAttempts: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            >
              <option value="3">3 attempts</option>
              <option value="5">5 attempts</option>
              <option value="10">10 attempts</option>
              <option value="0">Unlimited</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  )

  const renderAppearanceSettings = () => (
    <div className="space-y-8">
      {/* Theme Selection */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Monitor className="h-5 w-5 mr-2 text-blue-600" />
          Theme Selection
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[
            { id: 'light', name: 'Light', icon: Sun, preview: 'bg-white border-gray-200' },
            { id: 'dark', name: 'Dark', icon: Moon, preview: 'bg-slate-800 border-slate-600' },
            { id: 'auto', name: 'Auto', icon: Monitor, preview: 'bg-gradient-to-r from-white to-slate-800 border-gray-400' }
          ].map((theme) => {
            const Icon = theme.icon
            return (
              <label key={theme.id} className="cursor-pointer">
                <input
                  type="radio"
                  name="theme"
                  value={theme.id}
                  checked={settings.appearance.theme === theme.id}
                  onChange={(e) => updateAppearanceSettings({ theme: e.target.value })}
                  className="sr-only"
                />
                <div className={`p-4 border-2 rounded-lg transition-all ${
                  settings.appearance.theme === theme.id
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'
                }`}>
                  <div className={`w-full h-20 rounded-md mb-3 border ${theme.preview}`}></div>
                  <div className="flex items-center space-x-2">
                    <Icon className="h-4 w-4" />
                    <span className="font-medium">{theme.name}</span>
                  </div>
                </div>
              </label>
            )
          })}
        </div>
      </div>

      {/* Color Scheme Customization */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <PaletteIcon className="h-5 w-5 mr-2 text-pink-600" />
          Color Scheme
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.entries(settings.appearance.colorScheme).map(([key, color]) => (
            <div key={key}>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 capitalize">
                {key} Color
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="color"
                  value={color}
                  onChange={(e) => updateAppearanceSettings({
                    colorScheme: { ...settings.appearance.colorScheme, [key]: e.target.value }
                  })}
                  className="w-12 h-12 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer"
                />
                <input
                  type="text"
                  value={color}
                  onChange={(e) => updateAppearanceSettings({
                    colorScheme: { ...settings.appearance.colorScheme, [key]: e.target.value }
                  })}
                  className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                  placeholder="#000000"
                />
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 flex space-x-3">
          <button className="btn-outline">
            Reset to Default
          </button>
          <button className="btn-outline">
            Preview Changes
          </button>
        </div>
      </div>

      {/* Layout Preferences */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Layout className="h-5 w-5 mr-2 text-purple-600" />
          Layout Preferences
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Sidebar Position
            </label>
            <select
              value={settings.appearance.layout.sidebarPosition}
              onChange={(e) => updateAppearanceSettings({
                layout: { ...settings.appearance.layout, sidebarPosition: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            >
              <option value="left">Left</option>
              <option value="right">Right</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              UI Density
            </label>
            <select
              value={settings.appearance.layout.density}
              onChange={(e) => updateAppearanceSettings({
                layout: { ...settings.appearance.layout, density: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            >
              <option value="compact">Compact</option>
              <option value="comfortable">Comfortable</option>
              <option value="spacious">Spacious</option>
            </select>
          </div>
        </div>

        <div className="mt-6 space-y-4">
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={settings.appearance.layout.showAnimations}
              onChange={(e) => updateAppearanceSettings({
                layout: { ...settings.appearance.layout, showAnimations: e.target.checked }
              })}
              className="rounded border-gray-300 text-green-600 focus:ring-green-500"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">Enable animations</span>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={settings.appearance.layout.compactMode}
              onChange={(e) => updateAppearanceSettings({
                layout: { ...settings.appearance.layout, compactMode: e.target.checked }
              })}
              className="rounded border-gray-300 text-green-600 focus:ring-green-500"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">Compact mode</span>
          </label>
        </div>
      </div>

      {/* Typography Settings */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Type className="h-5 w-5 mr-2 text-green-600" />
          Typography
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Font Family
            </label>
            <select
              value={settings.appearance.typography.fontFamily}
              onChange={(e) => updateAppearanceSettings({
                typography: { ...settings.appearance.typography, fontFamily: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            >
              <option value="Inter">Inter</option>
              <option value="Roboto">Roboto</option>
              <option value="Open Sans">Open Sans</option>
              <option value="Poppins">Poppins</option>
              <option value="Lato">Lato</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Font Size
            </label>
            <select
              value={settings.appearance.typography.fontSize}
              onChange={(e) => updateAppearanceSettings({
                typography: { ...settings.appearance.typography, fontSize: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            >
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
              <option value="extra-large">Extra Large</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Font Weight
            </label>
            <select
              value={settings.appearance.typography.fontWeight}
              onChange={(e) => updateAppearanceSettings({
                typography: { ...settings.appearance.typography, fontWeight: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            >
              <option value="light">Light</option>
              <option value="normal">Normal</option>
              <option value="medium">Medium</option>
              <option value="semibold">Semibold</option>
              <option value="bold">Bold</option>
            </select>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Preview</h4>
          <div
            className="text-gray-700 dark:text-gray-300"
            style={{
              fontFamily: settings.appearance.typography.fontFamily,
              fontSize: settings.appearance.typography.fontSize === 'small' ? '14px' :
                        settings.appearance.typography.fontSize === 'medium' ? '16px' :
                        settings.appearance.typography.fontSize === 'large' ? '18px' : '20px',
              fontWeight: settings.appearance.typography.fontWeight
            }}
          >
            The quick brown fox jumps over the lazy dog. This is how your text will appear with the selected typography settings.
          </div>
        </div>
      </div>
    </div>
  )

  const renderBackupSettings = () => (
    <div className="space-y-8">
      {/* Backup Configuration */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Database className="h-5 w-5 mr-2 text-blue-600" />
          Backup Configuration
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Auto Backup
            </label>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.backup.autoBackup}
                onChange={(e) => updateBackupSettings({ autoBackup: e.target.checked })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"></div>
              <span className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                Enable automatic backups
              </span>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Backup Frequency
            </label>
            <select
              value={settings.backup.backupFrequency}
              onChange={(e) => updateBackupSettings({ backupFrequency: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
              disabled={!settings.backup.autoBackup}
            >
              <option value="hourly">Every Hour</option>
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Retention Period (Days)
            </label>
            <input
              type="number"
              min="1"
              max="365"
              value={settings.backup.retentionDays}
              onChange={(e) => updateBackupSettings({ retentionDays: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Next Backup
            </label>
            <div className="p-3 bg-gray-50 dark:bg-slate-700 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {settings.backup.autoBackup ? 'Tomorrow at 2:00 AM' : 'Manual backup only'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Cloud Storage Integration */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Cloud className="h-5 w-5 mr-2 text-green-600" />
          Cloud Storage
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Storage Provider
            </label>
            <select
              value={settings.backup.cloudStorage.provider}
              onChange={(e) => updateBackupSettings({
                cloudStorage: { ...settings.backup.cloudStorage, provider: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            >
              <option value="local">Local Storage</option>
              <option value="aws">Amazon S3</option>
              <option value="google">Google Cloud</option>
              <option value="azure">Azure Blob</option>
              <option value="dropbox">Dropbox</option>
            </select>
          </div>

          {settings.backup.cloudStorage.provider !== 'local' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Bucket/Container Name
                </label>
                <input
                  type="text"
                  value={settings.backup.cloudStorage.bucket}
                  onChange={(e) => updateBackupSettings({
                    cloudStorage: { ...settings.backup.cloudStorage, bucket: e.target.value }
                  })}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                  placeholder="my-backup-bucket"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Access Key
                </label>
                <input
                  type="text"
                  value={settings.backup.cloudStorage.accessKey}
                  onChange={(e) => updateBackupSettings({
                    cloudStorage: { ...settings.backup.cloudStorage, accessKey: e.target.value }
                  })}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                  placeholder="Your access key"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Secret Key
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={settings.backup.cloudStorage.secretKey}
                    onChange={(e) => updateBackupSettings({
                      cloudStorage: { ...settings.backup.cloudStorage, secretKey: e.target.value }
                    })}
                    className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                    placeholder="Your secret key"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
              </div>
            </>
          )}
        </div>

        {settings.backup.cloudStorage.provider !== 'local' && (
          <div className="mt-4 flex space-x-3">
            <button className="btn-outline">
              Test Connection
            </button>
            <button className="btn-primary">
              Save Configuration
            </button>
          </div>
        )}
      </div>

      {/* Backup History */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Archive className="h-5 w-5 mr-2 text-purple-600" />
          Backup History
        </h3>

        <div className="space-y-3">
          {settings.backup.backupHistory.map((backup) => (
            <div key={backup.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  backup.status === 'completed' ? 'bg-green-500' :
                  backup.status === 'failed' ? 'bg-red-500' : 'bg-yellow-500'
                }`}></div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {backup.type.charAt(0).toUpperCase() + backup.type.slice(1)} Backup
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {new Date(backup.timestamp).toLocaleString()} • {backup.size}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button className="text-blue-600 hover:text-blue-700 p-1" title="Download">
                  <Download className="h-4 w-4" />
                </button>
                <button className="text-green-600 hover:text-green-700 p-1" title="Restore">
                  <RefreshCw className="h-4 w-4" />
                </button>
                <button className="text-red-600 hover:text-red-700 p-1" title="Delete">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-4 flex justify-between items-center">
          <button className="text-sm text-blue-600 hover:text-blue-700">
            View All Backups
          </button>
          <button className="text-sm text-red-600 hover:text-red-700">
            Clear Old Backups
          </button>
        </div>
      </div>

      {/* Manual Backup & Restore */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <HardDrive className="h-5 w-5 mr-2 text-indigo-600" />
          Manual Operations
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={handleExportData}
            className="flex items-center justify-center px-6 py-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors"
          >
            <Download className="h-5 w-5 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900 dark:text-white">Create Backup</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Export all data now</p>
            </div>
          </button>

          <button
            onClick={handleImportData}
            className="flex items-center justify-center px-6 py-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors"
          >
            <Upload className="h-5 w-5 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900 dark:text-white">Restore Data</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Import from backup</p>
            </div>
          </button>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-800 dark:text-yellow-300">Important</h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-400 mt-1">
                Always verify your backups before relying on them. Test restore procedures regularly to ensure data integrity.
              </p>
            </div>
          </div>
        </div>

        <div className="mt-4 p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Last Backup</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {new Date(settings.backup.lastBackup).toLocaleString()}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900 dark:text-white">Status</p>
              <div className="flex items-center">
                <Check className="h-4 w-4 text-green-600 mr-1" />
                <span className="text-xs text-green-600">Completed</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderTabContent = () => {
    switch (activeTab) {
      case 'store':
        return renderStoreSettings()
      case 'profile':
        return renderProfileSettings()
      case 'notifications':
        return renderNotificationSettings()
      case 'security':
        return renderSecuritySettings()
      case 'appearance':
        return renderAppearanceSettings()
      case 'backup':
        return renderBackupSettings()
      default:
        return (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">
              {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} settings coming soon...
            </p>
          </div>
        )
    }
  }

  // Store Settings Component
  const renderStoreSettings = () => (
    <div className="space-y-8">
      {/* Basic Store Information */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Store className="h-5 w-5 mr-2 text-green-600" />
          Basic Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Store Name *
            </label>
            <input
              type="text"
              value={settings.store.name}
              onChange={(e) => updateStoreSettings({ name: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
              placeholder="Enter store name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Website
            </label>
            <div className="relative">
              <Globe className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="url"
                value={settings.store.website}
                onChange={(e) => updateStoreSettings({ website: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="https://yourstore.com"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Phone Number *
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="tel"
                value={settings.store.phone}
                onChange={(e) => updateStoreSettings({ phone: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="+63 ************"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Email Address *
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="email"
                value={settings.store.email}
                onChange={(e) => updateStoreSettings({ email: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
                placeholder="<EMAIL>"
              />
            </div>
          </div>
        </div>
        
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Address *
          </label>
          <div className="relative">
            <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
            <textarea
              value={settings.store.address}
              onChange={(e) => updateStoreSettings({ address: e.target.value })}
              rows={3}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
              placeholder="Enter complete store address"
            />
          </div>
        </div>
      </div>

      {/* Business Hours & Operating Days */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Clock className="h-5 w-5 mr-2 text-purple-600" />
          Business Hours & Operating Days
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Opening Time
            </label>
            <input
              type="time"
              value={settings.store.businessHours.open}
              onChange={(e) => updateStoreSettings({ 
                businessHours: { ...settings.store.businessHours, open: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Closing Time
            </label>
            <input
              type="time"
              value={settings.store.businessHours.close}
              onChange={(e) => updateStoreSettings({ 
                businessHours: { ...settings.store.businessHours, close: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Operating Days
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
            {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (
              <label key={day} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.store.operatingDays.includes(day)}
                  onChange={(e) => {
                    const updatedDays = e.target.checked
                      ? [...settings.store.operatingDays, day]
                      : settings.store.operatingDays.filter(d => d !== day)
                    updateStoreSettings({ operatingDays: updatedDays })
                  }}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300 capitalize">
                  {day.slice(0, 3)}
                </span>
              </label>
            ))}
          </div>
        </div>
      </div>

      {/* Store Branding */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <PaletteIcon className="h-5 w-5 mr-2 text-pink-600" />
          Store Branding
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Store Logo
            </label>
            <div className="flex items-center space-x-4">
              <div className="w-20 h-20 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center bg-gray-50 dark:bg-slate-700">
                {settings.store.branding.logo ? (
                  <Image
                    src={settings.store.branding.logo}
                    alt="Store Logo"
                    width={80}
                    height={80}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <Camera className="h-8 w-8 text-gray-400" />
                )}
              </div>
              <div className="flex flex-col space-y-2">
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="btn-outline text-sm px-4 py-2"
                >
                  Upload Logo
                </button>
                {settings.store.branding.logo && (
                  <button
                    onClick={() => updateStoreSettings({
                      branding: { ...settings.store.branding, logo: null }
                    })}
                    className="text-red-600 hover:text-red-700 text-sm"
                  >
                    Remove
                  </button>
                )}
              </div>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={(e) => handleFileUpload(e, 'logo')}
              className="hidden"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Store Slogan
            </label>
            <input
              type="text"
              value={settings.store.branding.slogan}
              onChange={(e) => updateStoreSettings({
                branding: { ...settings.store.branding, slogan: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"
              placeholder="Your Neighborhood Store"
            />
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h2>
        <div className="flex items-center space-x-3">
          {hasUnsavedChanges && (
            <span className="text-sm text-yellow-600 dark:text-yellow-400 flex items-center">
              <AlertTriangle className="h-4 w-4 mr-1" />
              Unsaved changes
            </span>
          )}
          <button
            onClick={handleSave}
            disabled={isLoading || !hasUnsavedChanges}
            className="btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {isLoading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>

      <div className="card">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-green-500 text-green-600 dark:text-green-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        <div className="p-6">
          {renderTabContent()}
        </div>
      </div>
    </div>
  )
}
