'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'

import { Plus, Edit, Trash2, Search, Package } from 'lucide-react'
import { useTheme } from 'next-themes'

import ProductModal from './ProductModal'
import { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'

interface ProductsSectionProps {
  onStatsUpdate: () => void
}

export default function ProductsSection({ onStatsUpdate }: ProductsSectionProps) {
  const { resolvedTheme } = useTheme()
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)

  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products')
      const data = await response.json()
      setProducts(data.products || [])
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this product?')) return

    try {
      const response = await fetch(`/api/products/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setProducts(products.filter(p => p.id !== id))
        onStatsUpdate()
      }
    } catch (error) {
      console.error('Error deleting product:', error)
    }
  }

  const handleEdit = (product: Product) => {
    setEditingProduct(product)
    setIsModalOpen(true)
  }

  const handleModalClose = () => {
    setIsModalOpen(false)
    setEditingProduct(null)
    fetchProducts()
    onStatsUpdate()
  }

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === '' || product.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          <div className="relative">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
              }}
            />
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
            }}
          >
            <option value="">All Categories</option>
            {PRODUCT_CATEGORIES.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>
        <button
          onClick={() => setIsModalOpen(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add to Product List
        </button>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredProducts.map((product) => (
          <div
            key={product.id}
            className="rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02]"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
              border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
            }}
          >
            <div
              className="aspect-square flex items-center justify-center transition-colors duration-300"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'
              }}
            >
              {product.image_url ? (
                <Image
                  src={product.image_url}
                  alt={product.name}
                  width={80}
                  height={80}
                  className="w-full h-full object-cover"
                />
              ) : (
                <Package
                  className="h-16 w-16 transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                  }}
                />
              )}
            </div>
            <div className="p-4">
              <h3
                className="font-semibold mb-1 transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                {product.name}
              </h3>
              <p
                className="text-sm mb-2 transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                }}
              >
                {product.category}
              </p>
              <div className="flex justify-between items-center mb-2">
                <span className="text-lg font-bold text-green-600">₱{product.price}</span>
                <span
                  className="text-sm transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                  }}
                >
                  {product.net_weight}
                </span>
              </div>
              <div className="flex justify-between items-center mb-4">
                <span
                  className={`text-sm ${product.stock_quantity < 10 ? 'text-red-600' : ''}`}
                  style={{
                    color: product.stock_quantity >= 10
                      ? (resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280')
                      : '#dc2626'
                  }}
                >
                  Stock: {product.stock_quantity}
                </span>
                {product.stock_quantity < 10 && (
                  <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Low Stock</span>
                )}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleEdit(product)}
                  className="flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </button>
                <button
                  onClick={() => handleDelete(product.id)}
                  className="flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredProducts.length === 0 && (
        <div className="text-center py-12">
          <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products in list</h3>
          <p className="text-gray-600">
            {searchTerm || selectedCategory
              ? 'Try adjusting your search or filter criteria'
              : 'Get started by adding your first product to the list'}
          </p>
        </div>
      )}

      {/* Product Modal */}
      <ProductModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        product={editingProduct}
      />
    </div>
  )
}
