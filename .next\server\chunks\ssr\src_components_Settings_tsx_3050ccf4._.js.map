{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/Settings.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport Image from 'next/image'\n\nimport {\n  Save, User, Store, Bell, Shield, Palette, Database, Download, Upload,\n  MapPin, Clock, Camera, Eye, EyeOff, Key, Smartphone, Globe,\n  Mail, Phone, Trash2, Plus, Edit3, Check, AlertTriangle,\n  Monitor, Sun, Moon, Palette as PaletteIcon, Type, Layout, Zap,\n  Cloud, HardDrive, RefreshCw, Archive\n} from 'lucide-react'\n\nimport { useSettings, StoreSettings, ProfileSettings, NotificationSettings, SecuritySettings, AppearanceSettings, BackupSettings } from '@/contexts/SettingsContext'\n\nexport default function Settings() {\n  const [activeTab, setActiveTab] = useState('store')\n  const [showPassword, setShowPassword] = useState(false)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n  const { settings, updateSettings, saveSettings, isLoading, hasUnsavedChanges } = useSettings()\n\n  const tabs = [\n    { id: 'store', label: 'Store Info', icon: Store },\n    { id: 'profile', label: 'Profile', icon: User },\n    { id: 'notifications', label: 'Notifications', icon: Bell },\n    { id: 'security', label: 'Security', icon: Shield },\n    { id: 'appearance', label: 'Appearance', icon: Palette },\n    { id: 'backup', label: 'Backup', icon: Database },\n  ]\n\n  // Helper functions for updating nested settings\n  const updateStoreSettings = (updates: Partial<StoreSettings>) => updateSettings('store', updates)\n  const updateProfileSettings = (updates: Partial<ProfileSettings>) => updateSettings('profile', updates)\n  const updateNotificationSettings = (updates: Partial<NotificationSettings>) => updateSettings('notifications', updates)\n  const updateSecuritySettings = (updates: Partial<SecuritySettings>) => updateSettings('security', updates)\n  const updateAppearanceSettings = (updates: Partial<AppearanceSettings>) => updateSettings('appearance', updates)\n  const updateBackupSettings = (updates: Partial<BackupSettings>) => updateSettings('backup', updates)\n\n  const handleSave = async () => {\n    try {\n      await saveSettings()\n      alert('Settings saved successfully!')\n    } catch (error) {\n      console.error('Error saving settings:', error)\n      alert('Error saving settings. Please try again.')\n    }\n  }\n\n  const handleExportData = () => {\n    console.warn('Exporting data...')\n    alert('Data export started. You will receive an email when ready.')\n  }\n\n  const handleImportData = () => {\n    console.warn('Importing data...')\n    alert('Please select a backup file to import.')\n  }\n\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, type: 'logo' | 'avatar') => {\n    const file = event.target.files?.[0]\n    if (file) {\n      const reader = new FileReader()\n      reader.onload = (e) => {\n        const result = e.target?.result as string\n        if (type === 'logo') {\n          updateStoreSettings({ \n            branding: { ...settings.store.branding, logo: result } \n          })\n        } else if (type === 'avatar') {\n          updateProfileSettings({ avatar: result })\n        }\n      }\n      reader.readAsDataURL(file)\n    }\n  }\n\n\n\n  // Professional render functions for all sections\n  const renderProfileSettings = () => (\n    <div className=\"space-y-8\">\n      {/* Personal Information */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <User className=\"h-5 w-5 mr-2 text-blue-600\" />\n          Personal Information\n        </h3>\n\n        <div className=\"flex items-start space-x-6 mb-6\">\n          <div className=\"flex flex-col items-center space-y-3\">\n            <div className=\"w-24 h-24 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center bg-gray-50 dark:bg-slate-700 overflow-hidden\">\n              {settings.profile.avatar ? (\n                <Image\n                  src={settings.profile.avatar}\n                  alt=\"Profile Avatar\"\n                  width={96}\n                  height={96}\n                  className=\"w-full h-full object-cover\"\n                />\n              ) : (\n                <User className=\"h-12 w-12 text-gray-400\" />\n              )}\n            </div>\n            <div className=\"flex flex-col space-y-2\">\n              <button\n                onClick={() => {\n                  const input = document.createElement('input')\n                  input.type = 'file'\n                  input.accept = 'image/*'\n                  input.onchange = (e) => handleFileUpload(e as Event, 'avatar')\n                  input.click()\n                }}\n                className=\"btn-outline text-sm px-3 py-1\"\n              >\n                Upload Photo\n              </button>\n              {settings.profile.avatar && (\n                <button\n                  onClick={() => updateProfileSettings({ avatar: null })}\n                  className=\"text-red-600 hover:text-red-700 text-sm\"\n                >\n                  Remove\n                </button>\n              )}\n            </div>\n          </div>\n\n          <div className=\"flex-1 grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                First Name *\n              </label>\n              <input\n                type=\"text\"\n                value={settings.profile.firstName}\n                onChange={(e) => updateProfileSettings({ firstName: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Enter first name\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Last Name *\n              </label>\n              <input\n                type=\"text\"\n                value={settings.profile.lastName}\n                onChange={(e) => updateProfileSettings({ lastName: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Enter last name\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Email Address *\n              </label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n                <input\n                  type=\"email\"\n                  value={settings.profile.email}\n                  onChange={(e) => updateProfileSettings({ email: e.target.value })}\n                  className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Phone Number\n              </label>\n              <div className=\"relative\">\n                <Phone className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n                <input\n                  type=\"tel\"\n                  value={settings.profile.phone}\n                  onChange={(e) => updateProfileSettings({ phone: e.target.value })}\n                  className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                  placeholder=\"+63 ************\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Role\n              </label>\n              <select\n                value={settings.profile.role}\n                onChange={(e) => updateProfileSettings({ role: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              >\n                <option value=\"Store Owner\">Store Owner</option>\n                <option value=\"Manager\">Manager</option>\n                <option value=\"Cashier\">Cashier</option>\n                <option value=\"Staff\">Staff</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Date of Birth\n              </label>\n              <input\n                type=\"date\"\n                value={settings.profile.dateOfBirth}\n                onChange={(e) => updateProfileSettings({ dateOfBirth: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Bio\n            </label>\n            <textarea\n              value={settings.profile.bio}\n              onChange={(e) => updateProfileSettings({ bio: e.target.value })}\n              rows={3}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              placeholder=\"Tell us about yourself...\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Address\n            </label>\n            <div className=\"relative\">\n              <MapPin className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <textarea\n                value={settings.profile.address}\n                onChange={(e) => updateProfileSettings({ address: e.target.value })}\n                rows={2}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Enter your address\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Emergency Contact */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <AlertTriangle className=\"h-5 w-5 mr-2 text-red-600\" />\n          Emergency Contact\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Contact Name\n            </label>\n            <input\n              type=\"text\"\n              value={settings.profile.emergencyContact.name}\n              onChange={(e) => updateProfileSettings({\n                emergencyContact: { ...settings.profile.emergencyContact, name: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              placeholder=\"Emergency contact name\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Phone Number\n            </label>\n            <div className=\"relative\">\n              <Phone className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"tel\"\n                value={settings.profile.emergencyContact.phone}\n                onChange={(e) => updateProfileSettings({\n                  emergencyContact: { ...settings.profile.emergencyContact, phone: e.target.value }\n                })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"+63 ************\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Relationship\n            </label>\n            <select\n              value={settings.profile.emergencyContact.relationship}\n              onChange={(e) => updateProfileSettings({\n                emergencyContact: { ...settings.profile.emergencyContact, relationship: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"Family\">Family</option>\n              <option value=\"Friend\">Friend</option>\n              <option value=\"Colleague\">Colleague</option>\n              <option value=\"Other\">Other</option>\n            </select>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderNotificationSettings = () => (\n    <div className=\"space-y-8\">\n      {/* Basic Notification Preferences */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Bell className=\"h-5 w-5 mr-2 text-blue-600\" />\n          Alert Preferences\n        </h3>\n        <div className=\"space-y-4\">\n          {Object.entries(settings.notifications).filter(([key]) =>\n            !['channels', 'customRules', 'templates'].includes(key)\n          ).map(([key, value]) => (\n            <div key={key} className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n              <div className=\"flex-1\">\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}\n                </label>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                  {key === 'lowStock' && 'Get notified when products are running low'}\n                  {key === 'newDebt' && 'Alert when new customer debt is recorded'}\n                  {key === 'paymentReceived' && 'Notification for debt payments'}\n                  {key === 'dailyReport' && 'Daily business summary report'}\n                  {key === 'weeklyReport' && 'Weekly business analytics report'}\n                  {key === 'emailNotifications' && 'Receive notifications via email'}\n                  {key === 'smsNotifications' && 'Receive notifications via SMS'}\n                  {key === 'pushNotifications' && 'Receive push notifications in browser'}\n                </p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer ml-4\">\n                <input\n                  type=\"checkbox\"\n                  checked={value as boolean}\n                  onChange={(e) => updateNotificationSettings({ [key]: e.target.checked })}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600\"></div>\n              </label>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Notification Channels */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Mail className=\"h-5 w-5 mr-2 text-green-600\" />\n          Delivery Channels\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Email Address\n            </label>\n            <div className=\"relative\">\n              <Mail className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"email\"\n                value={settings.notifications.channels.email}\n                onChange={(e) => updateNotificationSettings({\n                  channels: { ...settings.notifications.channels, email: e.target.value }\n                })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              SMS Number\n            </label>\n            <div className=\"relative\">\n              <Smartphone className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"tel\"\n                value={settings.notifications.channels.sms}\n                onChange={(e) => updateNotificationSettings({\n                  channels: { ...settings.notifications.channels, sms: e.target.value }\n                })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"+63 ************\"\n              />\n            </div>\n          </div>\n\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Webhook URL (Optional)\n            </label>\n            <div className=\"relative\">\n              <Globe className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"url\"\n                value={settings.notifications.channels.webhook}\n                onChange={(e) => updateNotificationSettings({\n                  channels: { ...settings.notifications.channels, webhook: e.target.value }\n                })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"https://your-webhook-url.com/notifications\"\n              />\n            </div>\n            <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n              Send notifications to external systems via webhook\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Custom Notification Rules */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Zap className=\"h-5 w-5 mr-2 text-yellow-600\" />\n          Custom Rules\n        </h3>\n        <div className=\"space-y-4\">\n          {settings.notifications.customRules.map((rule) => (\n            <div key={rule.id} className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <div className=\"flex items-center space-x-3\">\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">{rule.name}</h4>\n                  <span className={`px-2 py-1 text-xs rounded-full ${\n                    rule.enabled\n                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n                  }`}>\n                    {rule.enabled ? 'Active' : 'Inactive'}\n                  </span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <button className=\"text-blue-600 hover:text-blue-700 p-1\">\n                    <Edit3 className=\"h-4 w-4\" />\n                  </button>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={rule.enabled}\n                      onChange={(e) => {\n                        const updatedRules = settings.notifications.customRules.map(r =>\n                          r.id === rule.id ? { ...r, enabled: e.target.checked } : r\n                        )\n                        updateNotificationSettings({ customRules: updatedRules })\n                      }}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-green-600\"></div>\n                  </label>\n                </div>\n              </div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                <p><strong>Condition:</strong> {rule.condition}</p>\n                <p><strong>Action:</strong> {rule.action}</p>\n              </div>\n            </div>\n          ))}\n          <button className=\"w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-green-500 hover:text-green-600 transition-colors flex items-center justify-center space-x-2\">\n            <Plus className=\"h-5 w-5\" />\n            <span>Add Custom Rule</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderSecuritySettings = () => (\n    <div className=\"space-y-8\">\n      {/* Password Management */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Key className=\"h-5 w-5 mr-2 text-red-600\" />\n          Password Management\n        </h3>\n\n        <div className=\"grid grid-cols-1 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Current Password\n            </label>\n            <div className=\"relative\">\n              <input\n                type={showPassword ? 'text' : 'password'}\n                value={settings.security.currentPassword}\n                onChange={(e) => updateSecuritySettings({ currentPassword: e.target.value })}\n                className=\"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Enter current password\"\n              />\n              <button\n                type=\"button\"\n                onClick={() => setShowPassword(!showPassword)}\n                className=\"absolute right-3 top-3 text-gray-400 hover:text-gray-600\"\n              >\n                {showPassword ? <EyeOff className=\"h-5 w-5\" /> : <Eye className=\"h-5 w-5\" />}\n              </button>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                New Password\n              </label>\n              <input\n                type={showPassword ? 'text' : 'password'}\n                value={settings.security.newPassword}\n                onChange={(e) => updateSecuritySettings({ newPassword: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Enter new password\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Confirm New Password\n              </label>\n              <input\n                type={showPassword ? 'text' : 'password'}\n                value={settings.security.confirmPassword}\n                onChange={(e) => updateSecuritySettings({ confirmPassword: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"Confirm new password\"\n              />\n            </div>\n          </div>\n\n          <button className=\"btn-primary w-fit\">\n            Update Password\n          </button>\n        </div>\n      </div>\n\n      {/* Two-Factor Authentication */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Smartphone className=\"h-5 w-5 mr-2 text-green-600\" />\n          Two-Factor Authentication\n        </h3>\n\n        <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n          <div>\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">Enable 2FA</h4>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n              Add an extra layer of security to your account\n            </p>\n          </div>\n          <label className=\"relative inline-flex items-center cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              checked={settings.security.twoFactorAuth}\n              onChange={(e) => updateSecuritySettings({ twoFactorAuth: e.target.checked })}\n              className=\"sr-only peer\"\n            />\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600\"></div>\n          </label>\n        </div>\n\n        {settings.security.twoFactorAuth && (\n          <div className=\"mt-4 p-4 border border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <Check className=\"h-5 w-5 text-green-600\" />\n              <span className=\"font-medium text-green-800 dark:text-green-300\">2FA is enabled</span>\n            </div>\n            <div className=\"space-y-3\">\n              <button className=\"btn-outline text-sm\">\n                View Recovery Codes\n              </button>\n              <button className=\"btn-outline text-sm text-red-600 border-red-300 hover:bg-red-50\">\n                Disable 2FA\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Session Management */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Clock className=\"h-5 w-5 mr-2 text-purple-600\" />\n          Session Management\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Session Timeout (minutes)\n            </label>\n            <select\n              value={settings.security.sessionTimeout}\n              onChange={(e) => updateSecuritySettings({ sessionTimeout: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"15\">15 minutes</option>\n              <option value=\"30\">30 minutes</option>\n              <option value=\"60\">1 hour</option>\n              <option value=\"120\">2 hours</option>\n              <option value=\"480\">8 hours</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Password Expiry (days)\n            </label>\n            <select\n              value={settings.security.passwordExpiry}\n              onChange={(e) => updateSecuritySettings({ passwordExpiry: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"30\">30 days</option>\n              <option value=\"60\">60 days</option>\n              <option value=\"90\">90 days</option>\n              <option value=\"180\">180 days</option>\n              <option value=\"365\">1 year</option>\n              <option value=\"0\">Never</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Max Login Attempts\n            </label>\n            <select\n              value={settings.security.loginAttempts}\n              onChange={(e) => updateSecuritySettings({ loginAttempts: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"3\">3 attempts</option>\n              <option value=\"5\">5 attempts</option>\n              <option value=\"10\">10 attempts</option>\n              <option value=\"0\">Unlimited</option>\n            </select>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderAppearanceSettings = () => (\n    <div className=\"space-y-8\">\n      {/* Theme Selection */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Monitor className=\"h-5 w-5 mr-2 text-blue-600\" />\n          Theme Selection\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {[\n            { id: 'light', name: 'Light', icon: Sun, preview: 'bg-white border-gray-200' },\n            { id: 'dark', name: 'Dark', icon: Moon, preview: 'bg-slate-800 border-slate-600' },\n            { id: 'auto', name: 'Auto', icon: Monitor, preview: 'bg-gradient-to-r from-white to-slate-800 border-gray-400' }\n          ].map((theme) => {\n            const Icon = theme.icon\n            return (\n              <label key={theme.id} className=\"cursor-pointer\">\n                <input\n                  type=\"radio\"\n                  name=\"theme\"\n                  value={theme.id}\n                  checked={settings.appearance.theme === theme.id}\n                  onChange={(e) => updateAppearanceSettings({ theme: e.target.value })}\n                  className=\"sr-only\"\n                />\n                <div className={`p-4 border-2 rounded-lg transition-all ${\n                  settings.appearance.theme === theme.id\n                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'\n                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                }`}>\n                  <div className={`w-full h-20 rounded-md mb-3 border ${theme.preview}`}></div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Icon className=\"h-4 w-4\" />\n                    <span className=\"font-medium\">{theme.name}</span>\n                  </div>\n                </div>\n              </label>\n            )\n          })}\n        </div>\n      </div>\n\n      {/* Color Scheme Customization */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <PaletteIcon className=\"h-5 w-5 mr-2 text-pink-600\" />\n          Color Scheme\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {Object.entries(settings.appearance.colorScheme).map(([key, color]) => (\n            <div key={key}>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 capitalize\">\n                {key} Color\n              </label>\n              <div className=\"flex items-center space-x-3\">\n                <input\n                  type=\"color\"\n                  value={color}\n                  onChange={(e) => updateAppearanceSettings({\n                    colorScheme: { ...settings.appearance.colorScheme, [key]: e.target.value }\n                  })}\n                  className=\"w-12 h-12 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer\"\n                />\n                <input\n                  type=\"text\"\n                  value={color}\n                  onChange={(e) => updateAppearanceSettings({\n                    colorScheme: { ...settings.appearance.colorScheme, [key]: e.target.value }\n                  })}\n                  className=\"flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                  placeholder=\"#000000\"\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"mt-6 flex space-x-3\">\n          <button className=\"btn-outline\">\n            Reset to Default\n          </button>\n          <button className=\"btn-outline\">\n            Preview Changes\n          </button>\n        </div>\n      </div>\n\n      {/* Layout Preferences */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Layout className=\"h-5 w-5 mr-2 text-purple-600\" />\n          Layout Preferences\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Sidebar Position\n            </label>\n            <select\n              value={settings.appearance.layout.sidebarPosition}\n              onChange={(e) => updateAppearanceSettings({\n                layout: { ...settings.appearance.layout, sidebarPosition: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"left\">Left</option>\n              <option value=\"right\">Right</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              UI Density\n            </label>\n            <select\n              value={settings.appearance.layout.density}\n              onChange={(e) => updateAppearanceSettings({\n                layout: { ...settings.appearance.layout, density: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"compact\">Compact</option>\n              <option value=\"comfortable\">Comfortable</option>\n              <option value=\"spacious\">Spacious</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"mt-6 space-y-4\">\n          <label className=\"flex items-center space-x-3 cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              checked={settings.appearance.layout.showAnimations}\n              onChange={(e) => updateAppearanceSettings({\n                layout: { ...settings.appearance.layout, showAnimations: e.target.checked }\n              })}\n              className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n            />\n            <span className=\"text-sm text-gray-700 dark:text-gray-300\">Enable animations</span>\n          </label>\n\n          <label className=\"flex items-center space-x-3 cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              checked={settings.appearance.layout.compactMode}\n              onChange={(e) => updateAppearanceSettings({\n                layout: { ...settings.appearance.layout, compactMode: e.target.checked }\n              })}\n              className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n            />\n            <span className=\"text-sm text-gray-700 dark:text-gray-300\">Compact mode</span>\n          </label>\n        </div>\n      </div>\n\n      {/* Typography Settings */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Type className=\"h-5 w-5 mr-2 text-green-600\" />\n          Typography\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Font Family\n            </label>\n            <select\n              value={settings.appearance.typography.fontFamily}\n              onChange={(e) => updateAppearanceSettings({\n                typography: { ...settings.appearance.typography, fontFamily: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"Inter\">Inter</option>\n              <option value=\"Roboto\">Roboto</option>\n              <option value=\"Open Sans\">Open Sans</option>\n              <option value=\"Poppins\">Poppins</option>\n              <option value=\"Lato\">Lato</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Font Size\n            </label>\n            <select\n              value={settings.appearance.typography.fontSize}\n              onChange={(e) => updateAppearanceSettings({\n                typography: { ...settings.appearance.typography, fontSize: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"small\">Small</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"large\">Large</option>\n              <option value=\"extra-large\">Extra Large</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Font Weight\n            </label>\n            <select\n              value={settings.appearance.typography.fontWeight}\n              onChange={(e) => updateAppearanceSettings({\n                typography: { ...settings.appearance.typography, fontWeight: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"light\">Light</option>\n              <option value=\"normal\">Normal</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"semibold\">Semibold</option>\n              <option value=\"bold\">Bold</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n          <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Preview</h4>\n          <div\n            className=\"text-gray-700 dark:text-gray-300\"\n            style={{\n              fontFamily: settings.appearance.typography.fontFamily,\n              fontSize: settings.appearance.typography.fontSize === 'small' ? '14px' :\n                        settings.appearance.typography.fontSize === 'medium' ? '16px' :\n                        settings.appearance.typography.fontSize === 'large' ? '18px' : '20px',\n              fontWeight: settings.appearance.typography.fontWeight\n            }}\n          >\n            The quick brown fox jumps over the lazy dog. This is how your text will appear with the selected typography settings.\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderBackupSettings = () => (\n    <div className=\"space-y-8\">\n      {/* Backup Configuration */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Database className=\"h-5 w-5 mr-2 text-blue-600\" />\n          Backup Configuration\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Auto Backup\n            </label>\n            <label className=\"relative inline-flex items-center cursor-pointer\">\n              <input\n                type=\"checkbox\"\n                checked={settings.backup.autoBackup}\n                onChange={(e) => updateBackupSettings({ autoBackup: e.target.checked })}\n                className=\"sr-only peer\"\n              />\n              <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600\"></div>\n              <span className=\"ml-3 text-sm text-gray-700 dark:text-gray-300\">\n                Enable automatic backups\n              </span>\n            </label>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Backup Frequency\n            </label>\n            <select\n              value={settings.backup.backupFrequency}\n              onChange={(e) => updateBackupSettings({ backupFrequency: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              disabled={!settings.backup.autoBackup}\n            >\n              <option value=\"hourly\">Every Hour</option>\n              <option value=\"daily\">Daily</option>\n              <option value=\"weekly\">Weekly</option>\n              <option value=\"monthly\">Monthly</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Retention Period (Days)\n            </label>\n            <input\n              type=\"number\"\n              min=\"1\"\n              max=\"365\"\n              value={settings.backup.retentionDays}\n              onChange={(e) => updateBackupSettings({ retentionDays: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Next Backup\n            </label>\n            <div className=\"p-3 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {settings.backup.autoBackup ? 'Tomorrow at 2:00 AM' : 'Manual backup only'}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Cloud Storage Integration */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Cloud className=\"h-5 w-5 mr-2 text-green-600\" />\n          Cloud Storage\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Storage Provider\n            </label>\n            <select\n              value={settings.backup.cloudStorage.provider}\n              onChange={(e) => updateBackupSettings({\n                cloudStorage: { ...settings.backup.cloudStorage, provider: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            >\n              <option value=\"local\">Local Storage</option>\n              <option value=\"aws\">Amazon S3</option>\n              <option value=\"google\">Google Cloud</option>\n              <option value=\"azure\">Azure Blob</option>\n              <option value=\"dropbox\">Dropbox</option>\n            </select>\n          </div>\n\n          {settings.backup.cloudStorage.provider !== 'local' && (\n            <>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Bucket/Container Name\n                </label>\n                <input\n                  type=\"text\"\n                  value={settings.backup.cloudStorage.bucket}\n                  onChange={(e) => updateBackupSettings({\n                    cloudStorage: { ...settings.backup.cloudStorage, bucket: e.target.value }\n                  })}\n                  className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                  placeholder=\"my-backup-bucket\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Access Key\n                </label>\n                <input\n                  type=\"text\"\n                  value={settings.backup.cloudStorage.accessKey}\n                  onChange={(e) => updateBackupSettings({\n                    cloudStorage: { ...settings.backup.cloudStorage, accessKey: e.target.value }\n                  })}\n                  className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                  placeholder=\"Your access key\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Secret Key\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type={showPassword ? 'text' : 'password'}\n                    value={settings.backup.cloudStorage.secretKey}\n                    onChange={(e) => updateBackupSettings({\n                      cloudStorage: { ...settings.backup.cloudStorage, secretKey: e.target.value }\n                    })}\n                    className=\"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                    placeholder=\"Your secret key\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    className=\"absolute right-3 top-3 text-gray-400 hover:text-gray-600\"\n                  >\n                    {showPassword ? <EyeOff className=\"h-5 w-5\" /> : <Eye className=\"h-5 w-5\" />}\n                  </button>\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {settings.backup.cloudStorage.provider !== 'local' && (\n          <div className=\"mt-4 flex space-x-3\">\n            <button className=\"btn-outline\">\n              Test Connection\n            </button>\n            <button className=\"btn-primary\">\n              Save Configuration\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Backup History */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Archive className=\"h-5 w-5 mr-2 text-purple-600\" />\n          Backup History\n        </h3>\n\n        <div className=\"space-y-3\">\n          {settings.backup.backupHistory.map((backup) => (\n            <div key={backup.id} className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n              <div className=\"flex items-center space-x-3\">\n                <div className={`w-3 h-3 rounded-full ${\n                  backup.status === 'completed' ? 'bg-green-500' :\n                  backup.status === 'failed' ? 'bg-red-500' : 'bg-yellow-500'\n                }`}></div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {backup.type.charAt(0).toUpperCase() + backup.type.slice(1)} Backup\n                  </p>\n                  <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                    {new Date(backup.timestamp).toLocaleString()} • {backup.size}\n                  </p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <button className=\"text-blue-600 hover:text-blue-700 p-1\" title=\"Download\">\n                  <Download className=\"h-4 w-4\" />\n                </button>\n                <button className=\"text-green-600 hover:text-green-700 p-1\" title=\"Restore\">\n                  <RefreshCw className=\"h-4 w-4\" />\n                </button>\n                <button className=\"text-red-600 hover:text-red-700 p-1\" title=\"Delete\">\n                  <Trash2 className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"mt-4 flex justify-between items-center\">\n          <button className=\"text-sm text-blue-600 hover:text-blue-700\">\n            View All Backups\n          </button>\n          <button className=\"text-sm text-red-600 hover:text-red-700\">\n            Clear Old Backups\n          </button>\n        </div>\n      </div>\n\n      {/* Manual Backup & Restore */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <HardDrive className=\"h-5 w-5 mr-2 text-indigo-600\" />\n          Manual Operations\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <button\n            onClick={handleExportData}\n            className=\"flex items-center justify-center px-6 py-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors\"\n          >\n            <Download className=\"h-5 w-5 mr-3\" />\n            <div className=\"text-left\">\n              <p className=\"font-medium text-gray-900 dark:text-white\">Create Backup</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Export all data now</p>\n            </div>\n          </button>\n\n          <button\n            onClick={handleImportData}\n            className=\"flex items-center justify-center px-6 py-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors\"\n          >\n            <Upload className=\"h-5 w-5 mr-3\" />\n            <div className=\"text-left\">\n              <p className=\"font-medium text-gray-900 dark:text-white\">Restore Data</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Import from backup</p>\n            </div>\n          </button>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg\">\n          <div className=\"flex items-start space-x-3\">\n            <AlertTriangle className=\"h-5 w-5 text-yellow-600 mt-0.5\" />\n            <div>\n              <h4 className=\"font-medium text-yellow-800 dark:text-yellow-300\">Important</h4>\n              <p className=\"text-sm text-yellow-700 dark:text-yellow-400 mt-1\">\n                Always verify your backups before relying on them. Test restore procedures regularly to ensure data integrity.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-4 p-4 bg-gray-50 dark:bg-slate-700 rounded-lg\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-900 dark:text-white\">Last Backup</p>\n              <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                {new Date(settings.backup.lastBackup).toLocaleString()}\n              </p>\n            </div>\n            <div className=\"text-right\">\n              <p className=\"text-sm font-medium text-gray-900 dark:text-white\">Status</p>\n              <div className=\"flex items-center\">\n                <Check className=\"h-4 w-4 text-green-600 mr-1\" />\n                <span className=\"text-xs text-green-600\">Completed</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'store':\n        return renderStoreSettings()\n      case 'profile':\n        return renderProfileSettings()\n      case 'notifications':\n        return renderNotificationSettings()\n      case 'security':\n        return renderSecuritySettings()\n      case 'appearance':\n        return renderAppearanceSettings()\n      case 'backup':\n        return renderBackupSettings()\n      default:\n        return (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} settings coming soon...\n            </p>\n          </div>\n        )\n    }\n  }\n\n  // Store Settings Component\n  const renderStoreSettings = () => (\n    <div className=\"space-y-8\">\n      {/* Basic Store Information */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Store className=\"h-5 w-5 mr-2 text-green-600\" />\n          Basic Information\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Store Name *\n            </label>\n            <input\n              type=\"text\"\n              value={settings.store.name}\n              onChange={(e) => updateStoreSettings({ name: e.target.value })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              placeholder=\"Enter store name\"\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Website\n            </label>\n            <div className=\"relative\">\n              <Globe className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"url\"\n                value={settings.store.website}\n                onChange={(e) => updateStoreSettings({ website: e.target.value })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"https://yourstore.com\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Phone Number *\n            </label>\n            <div className=\"relative\">\n              <Phone className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"tel\"\n                value={settings.store.phone}\n                onChange={(e) => updateStoreSettings({ phone: e.target.value })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"+63 ************\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Email Address *\n            </label>\n            <div className=\"relative\">\n              <Mail className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <input\n                type=\"email\"\n                value={settings.store.email}\n                onChange={(e) => updateStoreSettings({ email: e.target.value })}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"mt-6\">\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Address *\n          </label>\n          <div className=\"relative\">\n            <MapPin className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n            <textarea\n              value={settings.store.address}\n              onChange={(e) => updateStoreSettings({ address: e.target.value })}\n              rows={3}\n              className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              placeholder=\"Enter complete store address\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Business Hours & Operating Days */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <Clock className=\"h-5 w-5 mr-2 text-purple-600\" />\n          Business Hours & Operating Days\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Opening Time\n            </label>\n            <input\n              type=\"time\"\n              value={settings.store.businessHours.open}\n              onChange={(e) => updateStoreSettings({ \n                businessHours: { ...settings.store.businessHours, open: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Closing Time\n            </label>\n            <input\n              type=\"time\"\n              value={settings.store.businessHours.close}\n              onChange={(e) => updateStoreSettings({ \n                businessHours: { ...settings.store.businessHours, close: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n            />\n          </div>\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            Operating Days\n          </label>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3\">\n            {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (\n              <label key={day} className=\"flex items-center space-x-2 cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.store.operatingDays.includes(day)}\n                  onChange={(e) => {\n                    const updatedDays = e.target.checked\n                      ? [...settings.store.operatingDays, day]\n                      : settings.store.operatingDays.filter(d => d !== day)\n                    updateStoreSettings({ operatingDays: updatedDays })\n                  }}\n                  className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300 capitalize\">\n                  {day.slice(0, 3)}\n                </span>\n              </label>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Store Branding */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <PaletteIcon className=\"h-5 w-5 mr-2 text-pink-600\" />\n          Store Branding\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Store Logo\n            </label>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"w-20 h-20 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center bg-gray-50 dark:bg-slate-700\">\n                {settings.store.branding.logo ? (\n                  <Image\n                    src={settings.store.branding.logo}\n                    alt=\"Store Logo\"\n                    width={80}\n                    height={80}\n                    className=\"w-full h-full object-cover rounded-lg\"\n                  />\n                ) : (\n                  <Camera className=\"h-8 w-8 text-gray-400\" />\n                )}\n              </div>\n              <div className=\"flex flex-col space-y-2\">\n                <button\n                  onClick={() => fileInputRef.current?.click()}\n                  className=\"btn-outline text-sm px-4 py-2\"\n                >\n                  Upload Logo\n                </button>\n                {settings.store.branding.logo && (\n                  <button\n                    onClick={() => updateStoreSettings({\n                      branding: { ...settings.store.branding, logo: null }\n                    })}\n                    className=\"text-red-600 hover:text-red-700 text-sm\"\n                  >\n                    Remove\n                  </button>\n                )}\n              </div>\n            </div>\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={(e) => handleFileUpload(e, 'logo')}\n              className=\"hidden\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Store Slogan\n            </label>\n            <input\n              type=\"text\"\n              value={settings.store.branding.slogan}\n              onChange={(e) => updateStoreSettings({\n                branding: { ...settings.store.branding, slogan: e.target.value }\n              })}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all\"\n              placeholder=\"Your Neighborhood Store\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Settings</h2>\n        <div className=\"flex items-center space-x-3\">\n          {hasUnsavedChanges && (\n            <span className=\"text-sm text-yellow-600 dark:text-yellow-400 flex items-center\">\n              <AlertTriangle className=\"h-4 w-4 mr-1\" />\n              Unsaved changes\n            </span>\n          )}\n          <button\n            onClick={handleSave}\n            disabled={isLoading || !hasUnsavedChanges}\n            className=\"btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isLoading ? (\n              <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n            ) : (\n              <Save className=\"h-4 w-4 mr-2\" />\n            )}\n            {isLoading ? 'Saving...' : 'Save Changes'}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"card\">\n        <div className=\"border-b border-gray-200 dark:border-gray-700\">\n          <nav className=\"flex space-x-8 px-6\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === tab.id\n                      ? 'border-green-500 text-green-600 dark:text-green-400'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                  }`}\n                >\n                  <Icon className=\"h-5 w-5 mr-2\" />\n                  {tab.label}\n                </button>\n              )\n            })}\n          </nav>\n        </div>\n\n        <div className=\"p-6\">\n          {renderTabContent()}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAbA;;;;;;AAee,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,YAAY,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAE3F,MAAM,OAAO;QACX;YAAE,IAAI;YAAS,OAAO;YAAc,MAAM,oMAAA,CAAA,QAAK;QAAC;QAChD;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC9C;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC1D;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,sMAAA,CAAA,SAAM;QAAC;QAClD;YAAE,IAAI;YAAc,OAAO;YAAc,MAAM,wMAAA,CAAA,UAAO;QAAC;QACvD;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACjD;IAED,gDAAgD;IAChD,MAAM,sBAAsB,CAAC,UAAoC,eAAe,SAAS;IACzF,MAAM,wBAAwB,CAAC,UAAsC,eAAe,WAAW;IAC/F,MAAM,6BAA6B,CAAC,UAA2C,eAAe,iBAAiB;IAC/G,MAAM,yBAAyB,CAAC,UAAuC,eAAe,YAAY;IAClG,MAAM,2BAA2B,CAAC,UAAyC,eAAe,cAAc;IACxG,MAAM,uBAAuB,CAAC,UAAqC,eAAe,UAAU;IAE5F,MAAM,aAAa;QACjB,IAAI;YACF,MAAM;YACN,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB;QACvB,QAAQ,IAAI,CAAC;QACb,MAAM;IACR;IAEA,MAAM,mBAAmB;QACvB,QAAQ,IAAI,CAAC;QACb,MAAM;IACR;IAEA,MAAM,mBAAmB,CAAC,OAA4C;QACpE,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,IAAI,SAAS,QAAQ;oBACnB,oBAAoB;wBAClB,UAAU;4BAAE,GAAG,SAAS,KAAK,CAAC,QAAQ;4BAAE,MAAM;wBAAO;oBACvD;gBACF,OAAO,IAAI,SAAS,UAAU;oBAC5B,sBAAsB;wBAAE,QAAQ;oBAAO;gBACzC;YACF;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAIA,iDAAiD;IACjD,MAAM,wBAAwB,kBAC5B,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,CAAC,MAAM,iBACtB,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,SAAS,OAAO,CAAC,MAAM;gDAC5B,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;qEAGZ,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;wDACP,MAAM,QAAQ,SAAS,aAAa,CAAC;wDACrC,MAAM,IAAI,GAAG;wDACb,MAAM,MAAM,GAAG;wDACf,MAAM,QAAQ,GAAG,CAAC,IAAM,iBAAiB,GAAY;wDACrD,MAAM,KAAK;oDACb;oDACA,WAAU;8DACX;;;;;;gDAGA,SAAS,OAAO,CAAC,MAAM,kBACtB,8OAAC;oDACC,SAAS,IAAM,sBAAsB;4DAAE,QAAQ;wDAAK;oDACpD,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO,CAAC,SAAS;oDACjC,UAAU,CAAC,IAAM,sBAAsB;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACnE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO,CAAC,QAAQ;oDAChC,UAAU,CAAC,IAAM,sBAAsB;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAClE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO,CAAC,KAAK;4DAC7B,UAAU,CAAC,IAAM,sBAAsB;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC;4DAC/D,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO,CAAC,KAAK;4DAC7B,UAAU,CAAC,IAAM,sBAAsB;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC;4DAC/D,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,OAAO,SAAS,OAAO,CAAC,IAAI;oDAC5B,UAAU,CAAC,IAAM,sBAAsB;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC9D,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;sDAI1B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO,CAAC,WAAW;oDACnC,UAAU,CAAC,IAAM,sBAAsB;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACrE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,OAAO,CAAC,GAAG;4CAC3B,UAAU,CAAC,IAAM,sBAAsB;oDAAE,KAAK,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC7D,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,OAAO,SAAS,OAAO,CAAC,OAAO;oDAC/B,UAAU,CAAC,IAAM,sBAAsB;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjE,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,OAAO,CAAC,gBAAgB,CAAC,IAAI;4CAC7C,UAAU,CAAC,IAAM,sBAAsB;oDACrC,kBAAkB;wDAAE,GAAG,SAAS,OAAO,CAAC,gBAAgB;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACjF;4CACA,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO,CAAC,gBAAgB,CAAC,KAAK;oDAC9C,UAAU,CAAC,IAAM,sBAAsB;4DACrC,kBAAkB;gEAAE,GAAG,SAAS,OAAO,CAAC,gBAAgB;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClF;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,OAAO,CAAC,gBAAgB,CAAC,YAAY;4CACrD,UAAU,CAAC,IAAM,sBAAsB;oDACrC,kBAAkB;wDAAE,GAAG,SAAS,OAAO,CAAC,gBAAgB;wDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACzF;4CACA,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQlC,MAAM,6BAA6B,kBACjC,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAGjD,8OAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,SAAS,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,GACnD,CAAC;oCAAC;oCAAY;oCAAe;iCAAY,CAAC,QAAQ,CAAC,MACnD,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACjB,8OAAC;oCAAc,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DACd,IAAI,OAAO,CAAC,YAAY,OAAO,OAAO,CAAC,MAAM,CAAA,MAAO,IAAI,WAAW;;;;;;8DAEtE,8OAAC;oDAAE,WAAU;;wDACV,QAAQ,cAAc;wDACtB,QAAQ,aAAa;wDACrB,QAAQ,qBAAqB;wDAC7B,QAAQ,iBAAiB;wDACzB,QAAQ,kBAAkB;wDAC1B,QAAQ,wBAAwB;wDAChC,QAAQ,sBAAsB;wDAC9B,QAAQ,uBAAuB;;;;;;;;;;;;;sDAGpC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,IAAM,2BAA2B;4DAAE,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACtE,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAvBT;;;;;;;;;;;;;;;;8BA+BhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;sCAGlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,aAAa,CAAC,QAAQ,CAAC,KAAK;oDAC5C,UAAU,CAAC,IAAM,2BAA2B;4DAC1C,UAAU;gEAAE,GAAG,SAAS,aAAa,CAAC,QAAQ;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACxE;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,aAAa,CAAC,QAAQ,CAAC,GAAG;oDAC1C,UAAU,CAAC,IAAM,2BAA2B;4DAC1C,UAAU;gEAAE,GAAG,SAAS,aAAa,CAAC,QAAQ;gEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACtE;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,aAAa,CAAC,QAAQ,CAAC,OAAO;oDAC9C,UAAU,CAAC,IAAM,2BAA2B;4DAC1C,UAAU;gEAAE,GAAG,SAAS,aAAa,CAAC,QAAQ;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC1E;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAGhB,8OAAC;4CAAE,WAAU;sDAAgD;;;;;;;;;;;;;;;;;;;;;;;;8BAQnE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAGlD,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,qBACvC,8OAAC;wCAAkB,WAAU;;0DAC3B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA6C,KAAK,IAAI;;;;;;0EACpE,8OAAC;gEAAK,WAAW,CAAC,+BAA+B,EAC/C,KAAK,OAAO,GACR,sEACA,iEACJ;0EACC,KAAK,OAAO,GAAG,WAAW;;;;;;;;;;;;kEAG/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;0EAChB,cAAA,8OAAC,0MAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,8OAAC;gEAAM,WAAU;;kFACf,8OAAC;wEACC,MAAK;wEACL,SAAS,KAAK,OAAO;wEACrB,UAAU,CAAC;4EACT,MAAM,eAAe,SAAS,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA,IAC1D,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG;oFAAE,GAAG,CAAC;oFAAE,SAAS,EAAE,MAAM,CAAC,OAAO;gFAAC,IAAI;4EAE3D,2BAA2B;gFAAE,aAAa;4EAAa;wEACzD;wEACA,WAAU;;;;;;kFAEZ,8OAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAmB;4DAAE,KAAK,SAAS;;;;;;;kEAC9C,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAgB;4DAAE,KAAK,MAAM;;;;;;;;;;;;;;uCAlClC,KAAK,EAAE;;;;;8CAsCnB,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOhB,MAAM,yBAAyB,kBAC7B,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAI/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAM,eAAe,SAAS;oDAC9B,OAAO,SAAS,QAAQ,CAAC,eAAe;oDACxC,UAAU,CAAC,IAAM,uBAAuB;4DAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC1E,WAAU;oDACV,aAAY;;;;;;8DAEd,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;8DAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAAe,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKtE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,MAAM,eAAe,SAAS;oDAC9B,OAAO,SAAS,QAAQ,CAAC,WAAW;oDACpC,UAAU,CAAC,IAAM,uBAAuB;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACtE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,MAAM,eAAe,SAAS;oDAC9B,OAAO,SAAS,QAAQ,CAAC,eAAe;oDACxC,UAAU,CAAC,IAAM,uBAAuB;4DAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC1E,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAO,WAAU;8CAAoB;;;;;;;;;;;;;;;;;;8BAO1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,8MAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;sCAIxD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;8CAI1D,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,SAAS,QAAQ,CAAC,aAAa;4CACxC,UAAU,CAAC,IAAM,uBAAuB;oDAAE,eAAe,EAAE,MAAM,CAAC,OAAO;gDAAC;4CAC1E,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;wBAIlB,SAAS,QAAQ,CAAC,aAAa,kBAC9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAiD;;;;;;;;;;;;8CAEnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAAsB;;;;;;sDAGxC,8OAAC;4CAAO,WAAU;sDAAkE;;;;;;;;;;;;;;;;;;;;;;;;8BAS5F,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,QAAQ,CAAC,cAAc;4CACvC,UAAU,CAAC,IAAM,uBAAuB;oDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACzE,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,8OAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,8OAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;;8CAIxB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,QAAQ,CAAC,cAAc;4CACvC,UAAU,CAAC,IAAM,uBAAuB;oDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACzE,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,8OAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,8OAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAI;;;;;;;;;;;;;;;;;;8CAItB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,QAAQ,CAAC,aAAa;4CACtC,UAAU,CAAC,IAAM,uBAAuB;oDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,8OAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,8OAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,8OAAC;oDAAO,OAAM;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ9B,MAAM,2BAA2B,kBAC/B,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAS,MAAM;oCAAS,MAAM,gMAAA,CAAA,MAAG;oCAAE,SAAS;gCAA2B;gCAC7E;oCAAE,IAAI;oCAAQ,MAAM;oCAAQ,MAAM,kMAAA,CAAA,OAAI;oCAAE,SAAS;gCAAgC;gCACjF;oCAAE,IAAI;oCAAQ,MAAM;oCAAQ,MAAM,wMAAA,CAAA,UAAO;oCAAE,SAAS;gCAA2D;6BAChH,CAAC,GAAG,CAAC,CAAC;gCACL,MAAM,OAAO,MAAM,IAAI;gCACvB,qBACE,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,MAAM,EAAE;4CACf,SAAS,SAAS,UAAU,CAAC,KAAK,KAAK,MAAM,EAAE;4CAC/C,UAAU,CAAC,IAAM,yBAAyB;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAClE,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAW,CAAC,uCAAuC,EACtD,SAAS,UAAU,CAAC,KAAK,KAAK,MAAM,EAAE,GAClC,sDACA,8DACJ;;8DACA,8OAAC;oDAAI,WAAW,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;;;;;;8DACrE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAe,MAAM,IAAI;;;;;;;;;;;;;;;;;;;mCAjBnC,MAAM,EAAE;;;;;4BAsBxB;;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,wMAAA,CAAA,UAAW;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIxD,8OAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,SAAS,UAAU,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAChE,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;;gDACd;gDAAI;;;;;;;sDAEP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,yBAAyB;4DACxC,aAAa;gEAAE,GAAG,SAAS,UAAU,CAAC,WAAW;gEAAE,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC3E;oDACA,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,yBAAyB;4DACxC,aAAa;gEAAE,GAAG,SAAS,UAAU,CAAC,WAAW;gEAAE,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC3E;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;;mCApBR;;;;;;;;;;sCA2Bd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAAc;;;;;;8CAGhC,8OAAC;oCAAO,WAAU;8CAAc;;;;;;;;;;;;;;;;;;8BAOpC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,qNAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAIrD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,UAAU,CAAC,MAAM,CAAC,eAAe;4CACjD,UAAU,CAAC,IAAM,yBAAyB;oDACxC,QAAQ;wDAAE,GAAG,SAAS,UAAU,CAAC,MAAM;wDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC3E;4CACA,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;8CAI1B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,UAAU,CAAC,MAAM,CAAC,OAAO;4CACzC,UAAU,CAAC,IAAM,yBAAyB;oDACxC,QAAQ;wDAAE,GAAG,SAAS,UAAU,CAAC,MAAM;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACnE;4CACA,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,8OAAC;oDAAO,OAAM;8DAAW;;;;;;;;;;;;;;;;;;;;;;;;sCAK/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,SAAS,UAAU,CAAC,MAAM,CAAC,cAAc;4CAClD,UAAU,CAAC,IAAM,yBAAyB;oDACxC,QAAQ;wDAAE,GAAG,SAAS,UAAU,CAAC,MAAM;wDAAE,gBAAgB,EAAE,MAAM,CAAC,OAAO;oDAAC;gDAC5E;4CACA,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;8CAG7D,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,SAAS,UAAU,CAAC,MAAM,CAAC,WAAW;4CAC/C,UAAU,CAAC,IAAM,yBAAyB;oDACxC,QAAQ;wDAAE,GAAG,SAAS,UAAU,CAAC,MAAM;wDAAE,aAAa,EAAE,MAAM,CAAC,OAAO;oDAAC;gDACzE;4CACA,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;;8BAMjE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;sCAIlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,UAAU,CAAC,UAAU,CAAC,UAAU;4CAChD,UAAU,CAAC,IAAM,yBAAyB;oDACxC,YAAY;wDAAE,GAAG,SAAS,UAAU,CAAC,UAAU;wDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC9E;4CACA,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;8CAIzB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,UAAU,CAAC,UAAU,CAAC,QAAQ;4CAC9C,UAAU,CAAC,IAAM,yBAAyB;oDACxC,YAAY;wDAAE,GAAG,SAAS,UAAU,CAAC,UAAU;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC5E;4CACA,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAc;;;;;;;;;;;;;;;;;;8CAIhC,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,UAAU,CAAC,UAAU,CAAC,UAAU;4CAChD,UAAU,CAAC,IAAM,yBAAyB;oDACxC,YAAY;wDAAE,GAAG,SAAS,UAAU,CAAC,UAAU;wDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC9E;4CACA,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAK3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAC/D,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,YAAY,SAAS,UAAU,CAAC,UAAU,CAAC,UAAU;wCACrD,UAAU,SAAS,UAAU,CAAC,UAAU,CAAC,QAAQ,KAAK,UAAU,SACtD,SAAS,UAAU,CAAC,UAAU,CAAC,QAAQ,KAAK,WAAW,SACvD,SAAS,UAAU,CAAC,UAAU,CAAC,QAAQ,KAAK,UAAU,SAAS;wCACzE,YAAY,SAAS,UAAU,CAAC,UAAU,CAAC,UAAU;oCACvD;8CACD;;;;;;;;;;;;;;;;;;;;;;;;IAQT,MAAM,uBAAuB,kBAC3B,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIrD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,MAAM,CAAC,UAAU;oDACnC,UAAU,CAAC,IAAM,qBAAqB;4DAAE,YAAY,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACrE,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;8CAMpE,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,MAAM,CAAC,eAAe;4CACtC,UAAU,CAAC,IAAM,qBAAqB;oDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,WAAU;4CACV,UAAU,CAAC,SAAS,MAAM,CAAC,UAAU;;8DAErC,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;8CAI5B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO,SAAS,MAAM,CAAC,aAAa;4CACpC,UAAU,CAAC,IAAM,qBAAqB;oDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACtE,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,CAAC,UAAU,GAAG,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;sCAInD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,OAAO,SAAS,MAAM,CAAC,YAAY,CAAC,QAAQ;4CAC5C,UAAU,CAAC,IAAM,qBAAqB;oDACpC,cAAc;wDAAE,GAAG,SAAS,MAAM,CAAC,YAAY;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC5E;4CACA,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;gCAI3B,SAAS,MAAM,CAAC,YAAY,CAAC,QAAQ,KAAK,yBACzC;;sDACE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,MAAM,CAAC,YAAY,CAAC,MAAM;oDAC1C,UAAU,CAAC,IAAM,qBAAqB;4DACpC,cAAc;gEAAE,GAAG,SAAS,MAAM,CAAC,YAAY;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC1E;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,MAAM,CAAC,YAAY,CAAC,SAAS;oDAC7C,UAAU,CAAC,IAAM,qBAAqB;4DACpC,cAAc;gEAAE,GAAG,SAAS,MAAM,CAAC,YAAY;gEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC7E;oDACA,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAM,eAAe,SAAS;4DAC9B,OAAO,SAAS,MAAM,CAAC,YAAY,CAAC,SAAS;4DAC7C,UAAU,CAAC,IAAM,qBAAqB;oEACpC,cAAc;wEAAE,GAAG,SAAS,MAAM,CAAC,YAAY;wEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAC7E;4DACA,WAAU;4DACV,aAAY;;;;;;sEAEd,8OAAC;4DACC,MAAK;4DACL,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;sEAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAAe,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQ3E,SAAS,MAAM,CAAC,YAAY,CAAC,QAAQ,KAAK,yBACzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAAc;;;;;;8CAGhC,8OAAC;oCAAO,WAAU;8CAAc;;;;;;;;;;;;;;;;;;8BAQtC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAItD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,uBAClC,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,qBAAqB,EACpC,OAAO,MAAM,KAAK,cAAc,iBAChC,OAAO,MAAM,KAAK,WAAW,eAAe,iBAC5C;;;;;;8DACF,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;;gEACV,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC;gEAAG;;;;;;;sEAE9D,8OAAC;4DAAE,WAAU;;gEACV,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc;gEAAG;gEAAI,OAAO,IAAI;;;;;;;;;;;;;;;;;;;sDAIlE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;oDAAwC,OAAM;8DAC9D,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAO,WAAU;oDAA0C,OAAM;8DAChE,cAAA,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;oDAAO,WAAU;oDAAsC,OAAM;8DAC5D,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;mCAvBd,OAAO,EAAE;;;;;;;;;;sCA8BvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAA4C;;;;;;8CAG9D,8OAAC;oCAAO,WAAU;8CAA0C;;;;;;;;;;;;;;;;;;8BAOhE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAIxD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAA4C;;;;;;8DACzD,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;8CAI5D,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAA4C;;;;;;8DACzD,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;sCAK9D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;;;;;;;;;;;;;;;;;;sCAOvE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,SAAS,MAAM,CAAC,UAAU,EAAE,cAAc;;;;;;;;;;;;kDAGxD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;0DACjE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASvD,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BACV,UAAU,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,KAAK,CAAC;4BAAG;;;;;;;;;;;;QAIlE;IACF;IAEA,2BAA2B;IAC3B,MAAM,sBAAsB,kBAC1B,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;sCAGnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK,CAAC,IAAI;4CAC1B,UAAU,CAAC,IAAM,oBAAoB;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC5D,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK,CAAC,OAAO;oDAC7B,UAAU,CAAC,IAAM,oBAAoB;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC/D,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK,CAAC,KAAK;oDAC3B,UAAU,CAAC,IAAM,oBAAoB;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC7D,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK,CAAC,KAAK;oDAC3B,UAAU,CAAC,IAAM,oBAAoB;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC7D,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAMpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,OAAO,SAAS,KAAK,CAAC,OAAO;4CAC7B,UAAU,CAAC,IAAM,oBAAoB;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC/D,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiC;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK,CAAC,aAAa,CAAC,IAAI;4CACxC,UAAU,CAAC,IAAM,oBAAoB;oDACnC,eAAe;wDAAE,GAAG,SAAS,KAAK,CAAC,aAAa;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACzE;4CACA,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK,CAAC,aAAa,CAAC,KAAK;4CACzC,UAAU,CAAC,IAAM,oBAAoB;oDACnC,eAAe;wDAAE,GAAG,SAAS,KAAK,CAAC,aAAa;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC1E;4CACA,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAU;wCAAW;wCAAa;wCAAY;wCAAU;wCAAY;qCAAS,CAAC,GAAG,CAAC,CAAC,oBACnF,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC;oDAC/C,UAAU,CAAC;wDACT,MAAM,cAAc,EAAE,MAAM,CAAC,OAAO,GAChC;+DAAI,SAAS,KAAK,CAAC,aAAa;4DAAE;yDAAI,GACtC,SAAS,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;wDACnD,oBAAoB;4DAAE,eAAe;wDAAY;oDACnD;oDACA,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DACb,IAAI,KAAK,CAAC,GAAG;;;;;;;2CAbN;;;;;;;;;;;;;;;;;;;;;;8BAsBpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,wMAAA,CAAA,UAAW;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAIxD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,SAAS,KAAK,CAAC,QAAQ,CAAC,IAAI,iBAC3B,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,SAAS,KAAK,CAAC,QAAQ,CAAC,IAAI;wDACjC,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;6EAGZ,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAGtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS,IAAM,aAAa,OAAO,EAAE;4DACrC,WAAU;sEACX;;;;;;wDAGA,SAAS,KAAK,CAAC,QAAQ,CAAC,IAAI,kBAC3B,8OAAC;4DACC,SAAS,IAAM,oBAAoB;oEACjC,UAAU;wEAAE,GAAG,SAAS,KAAK,CAAC,QAAQ;wEAAE,MAAM;oEAAK;gEACrD;4DACA,WAAU;sEACX;;;;;;;;;;;;;;;;;;sDAMP,8OAAC;4CACC,KAAK;4CACL,MAAK;4CACL,QAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,GAAG;4CACrC,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK,CAAC,QAAQ,CAAC,MAAM;4CACrC,UAAU,CAAC,IAAM,oBAAoB;oDACnC,UAAU;wDAAE,GAAG,SAAS,KAAK,CAAC,QAAQ;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACjE;4CACA,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQxB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCACjE,8OAAC;wBAAI,WAAU;;4BACZ,mCACC,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAI9C,8OAAC;gCACC,SAAS;gCACT,UAAU,aAAa,CAAC;gCACxB,WAAU;;oCAET,0BACC,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAEjB,YAAY,cAAc;;;;;;;;;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC;gCACT,MAAM,OAAO,IAAI,IAAI;gCACrB,qBACE,8OAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,6EAA6E,EACvF,cAAc,IAAI,EAAE,GAChB,wDACA,0HACJ;;sDAEF,8OAAC;4CAAK,WAAU;;;;;;wCACf,IAAI,KAAK;;mCATL,IAAI,EAAE;;;;;4BAYjB;;;;;;;;;;;kCAIJ,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}