[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\ai\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\speech-to-text\\route.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AIAssistant.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AISupport.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts": "39"}, {"size": 5708, "mtime": 1752515097450, "results": "40", "hashOfConfig": "41"}, {"size": 3720, "mtime": 1752513751220, "results": "42", "hashOfConfig": "41"}, {"size": 2768, "mtime": 1752232447112, "results": "43", "hashOfConfig": "41"}, {"size": 2674, "mtime": 1752216983535, "results": "44", "hashOfConfig": "41"}, {"size": 2623, "mtime": 1752213773252, "results": "45", "hashOfConfig": "41"}, {"size": 2412, "mtime": 1752216948323, "results": "46", "hashOfConfig": "41"}, {"size": 6060, "mtime": 1752667359628, "results": "47", "hashOfConfig": "41"}, {"size": 1415, "mtime": 1751939500686, "results": "48", "hashOfConfig": "41"}, {"size": 11603, "mtime": 1752190925916, "results": "49", "hashOfConfig": "41"}, {"size": 1495, "mtime": 1752281960816, "results": "50", "hashOfConfig": "41"}, {"size": 8374, "mtime": 1752190941247, "results": "51", "hashOfConfig": "41"}, {"size": 753, "mtime": 1751948571543, "results": "52", "hashOfConfig": "41"}, {"size": 10478, "mtime": 1752501745892, "results": "53", "hashOfConfig": "41"}, {"size": 42089, "mtime": 1752667385880, "results": "54", "hashOfConfig": "41"}, {"size": 13127, "mtime": 1752561102360, "results": "55", "hashOfConfig": "41"}, {"size": 56392, "mtime": 1752667532295, "results": "56", "hashOfConfig": "41"}, {"size": 32560, "mtime": 1752667568264, "results": "57", "hashOfConfig": "41"}, {"size": 30994, "mtime": 1752510024594, "results": "58", "hashOfConfig": "41"}, {"size": 7440, "mtime": 1752324845264, "results": "59", "hashOfConfig": "41"}, {"size": 7823, "mtime": 1752061478940, "results": "60", "hashOfConfig": "41"}, {"size": 63256, "mtime": 1752667581277, "results": "61", "hashOfConfig": "41"}, {"size": 61481, "mtime": 1752667483151, "results": "62", "hashOfConfig": "41"}, {"size": 1308, "mtime": 1752515037632, "results": "63", "hashOfConfig": "41"}, {"size": 1188, "mtime": 1751950590317, "results": "64", "hashOfConfig": "41"}, {"size": 8850, "mtime": 1751938233472, "results": "65", "hashOfConfig": "41"}, {"size": 9147, "mtime": 1752061416183, "results": "66", "hashOfConfig": "41"}, {"size": 1905, "mtime": 1751950118275, "results": "67", "hashOfConfig": "41"}, {"size": 67117, "mtime": 1752667594733, "results": "68", "hashOfConfig": "41"}, {"size": 18126, "mtime": 1752514964304, "results": "69", "hashOfConfig": "41"}, {"size": 344, "mtime": 1752212829480, "results": "70", "hashOfConfig": "41"}, {"size": 3296, "mtime": 1752212374913, "results": "71", "hashOfConfig": "41"}, {"size": 2521, "mtime": 1751950064561, "results": "72", "hashOfConfig": "41"}, {"size": 10471, "mtime": 1752281679844, "results": "73", "hashOfConfig": "41"}, {"size": 5120, "mtime": 1752212844330, "results": "74", "hashOfConfig": "41"}, {"size": 1025, "mtime": 1752217088143, "results": "75", "hashOfConfig": "41"}, {"size": 4937, "mtime": 1752513719322, "results": "76", "hashOfConfig": "41"}, {"size": 1266, "mtime": 1752212686019, "results": "77", "hashOfConfig": "41"}, {"size": 2234, "mtime": 1752217075185, "results": "78", "hashOfConfig": "41"}, {"size": 5694, "mtime": 1752212408642, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10tv68i", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 7, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 1, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\ai\\route.ts", ["197", "198", "199"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts", ["200", "201"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts", ["202"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts", ["203", "204"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts", ["205"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\speech-to-text\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts", ["206"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx", ["207"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx", ["208", "209", "210"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx", ["211"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx", ["212", "213", "214"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AIAssistant.tsx", ["215", "216", "217", "218", "219", "220", "221", "222", "223"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AISupport.tsx", ["224", "225", "226", "227"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx", ["228", "229", "230", "231", "232", "233", "234", "235", "236", "237", "238", "239", "240"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx", ["241", "242", "243", "244", "245", "246", "247"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx", ["248", "249"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx", ["250", "251", "252"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx", ["253", "254", "255", "256", "257"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx", ["258", "259"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx", ["260", "261", "262", "263", "264", "265", "266", "267", "268", "269", "270", "271", "272"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx", ["273"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx", ["274"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts", ["275", "276", "277", "278", "279", "280"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts", ["281"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts", ["282", "283"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts", ["284"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts", ["285", "286", "287", "288", "289"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts", ["290", "291", "292", "293", "294"], [], {"ruleId": "295", "severity": 1, "message": "296", "line": 2, "column": 1, "nodeType": "297", "endLine": 2, "endColumn": 59, "fix": "298"}, {"ruleId": "295", "severity": 1, "message": "299", "line": 2, "column": 1, "nodeType": "297", "endLine": 2, "endColumn": 59, "fix": "300"}, {"ruleId": "301", "severity": 1, "message": "302", "line": 100, "column": 12, "nodeType": null, "messageId": "303", "endLine": 100, "endColumn": 17}, {"ruleId": "295", "severity": 1, "message": "296", "line": 1, "column": 1, "nodeType": "297", "endLine": 1, "endColumn": 56, "fix": "304"}, {"ruleId": "295", "severity": 1, "message": "305", "line": 3, "column": 1, "nodeType": "297", "endLine": 9, "endColumn": 25, "fix": "306"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 1, "column": 1, "nodeType": "297", "endLine": 1, "endColumn": 56, "fix": "307"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 1, "column": 1, "nodeType": "297", "endLine": 1, "endColumn": 42, "fix": "308"}, {"ruleId": "295", "severity": 1, "message": "305", "line": 3, "column": 1, "nodeType": "297", "endLine": 12, "endColumn": 25, "fix": "309"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 1, "column": 1, "nodeType": "297", "endLine": 1, "endColumn": 56, "fix": "310"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 1, "column": 1, "nodeType": "297", "endLine": 1, "endColumn": 56, "fix": "311"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 2, "column": 1, "nodeType": "297", "endLine": 2, "endColumn": 51, "fix": "312"}, {"ruleId": "295", "severity": 1, "message": "313", "line": 3, "column": 1, "nodeType": "297", "endLine": 3, "endColumn": 44, "fix": "314"}, {"ruleId": "295", "severity": 1, "message": "315", "line": 4, "column": 1, "nodeType": "297", "endLine": 4, "endColumn": 44, "fix": "316"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 7, "column": 1, "nodeType": "297", "endLine": 7, "endColumn": 29, "fix": "317"}, {"ruleId": "295", "severity": 1, "message": "318", "line": 4, "column": 1, "nodeType": "297", "endLine": 4, "endColumn": 44, "fix": "319"}, {"ruleId": "295", "severity": 1, "message": "320", "line": 3, "column": 1, "nodeType": "297", "endLine": 3, "endColumn": 44, "fix": "321"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 6, "column": 1, "nodeType": "297", "endLine": 6, "endColumn": 39, "fix": "322"}, {"ruleId": "323", "severity": 1, "message": "324", "line": 58, "column": 5, "nodeType": "325", "messageId": "326", "endLine": 58, "endColumn": 16, "suggestions": "327"}, {"ruleId": "295", "severity": 1, "message": "328", "line": 4, "column": 1, "nodeType": "297", "endLine": 4, "endColumn": 39, "fix": "329"}, {"ruleId": "295", "severity": 1, "message": "330", "line": 5, "column": 1, "nodeType": "297", "endLine": 19, "endColumn": 22, "fix": "331"}, {"ruleId": "295", "severity": 1, "message": "332", "line": 20, "column": 1, "nodeType": "297", "endLine": 20, "endColumn": 56, "fix": "333"}, {"ruleId": "301", "severity": 1, "message": "302", "line": 148, "column": 14, "nodeType": null, "messageId": "303", "endLine": 148, "endColumn": 19}, {"ruleId": "323", "severity": 1, "message": "324", "line": 257, "column": 15, "nodeType": "325", "messageId": "326", "endLine": 257, "endColumn": 26, "suggestions": "334"}, {"ruleId": "323", "severity": 1, "message": "324", "line": 259, "column": 15, "nodeType": "325", "messageId": "326", "endLine": 259, "endColumn": 26, "suggestions": "335"}, {"ruleId": "323", "severity": 1, "message": "324", "line": 261, "column": 15, "nodeType": "325", "messageId": "326", "endLine": 261, "endColumn": 26, "suggestions": "336"}, {"ruleId": "323", "severity": 1, "message": "324", "line": 266, "column": 15, "nodeType": "325", "messageId": "326", "endLine": 266, "endColumn": 26, "suggestions": "337"}, {"ruleId": "323", "severity": 1, "message": "324", "line": 270, "column": 13, "nodeType": "325", "messageId": "326", "endLine": 270, "endColumn": 24, "suggestions": "338"}, {"ruleId": "295", "severity": 1, "message": "328", "line": 4, "column": 1, "nodeType": "297", "endLine": 4, "endColumn": 39, "fix": "339"}, {"ruleId": "295", "severity": 1, "message": "330", "line": 5, "column": 1, "nodeType": "297", "endLine": 18, "endColumn": 22, "fix": "340"}, {"ruleId": "295", "severity": 1, "message": "332", "line": 19, "column": 1, "nodeType": "297", "endLine": 19, "endColumn": 39, "fix": "341"}, {"ruleId": "301", "severity": 1, "message": "302", "line": 130, "column": 14, "nodeType": null, "messageId": "303", "endLine": 130, "endColumn": 19}, {"ruleId": "295", "severity": 1, "message": "342", "line": 3, "column": 1, "nodeType": "297", "endLine": 3, "endColumn": 66, "fix": "343"}, {"ruleId": "295", "severity": 1, "message": "344", "line": 5, "column": 1, "nodeType": "297", "endLine": 5, "endColumn": 39, "fix": "345"}, {"ruleId": "346", "severity": 1, "message": "347", "line": 203, "column": 6, "nodeType": "348", "endLine": 203, "endColumn": 13, "suggestions": "349"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 241, "column": 27, "nodeType": "352", "messageId": "353", "endLine": 241, "endColumn": 30, "suggestions": "354"}, {"ruleId": "346", "severity": 1, "message": "355", "line": 391, "column": 7, "nodeType": "348", "endLine": 391, "endColumn": 62, "suggestions": "356"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 413, "column": 27, "nodeType": "352", "messageId": "353", "endLine": 413, "endColumn": 30, "suggestions": "357"}, {"ruleId": "346", "severity": 1, "message": "358", "line": 529, "column": 7, "nodeType": "348", "endLine": 529, "endColumn": 42, "suggestions": "359"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 551, "column": 27, "nodeType": "352", "messageId": "353", "endLine": 551, "endColumn": 30, "suggestions": "360"}, {"ruleId": "346", "severity": 1, "message": "358", "line": 631, "column": 7, "nodeType": "348", "endLine": 631, "endColumn": 46, "suggestions": "361"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 665, "column": 29, "nodeType": "352", "messageId": "353", "endLine": 665, "endColumn": 32, "suggestions": "362"}, {"ruleId": "346", "severity": 1, "message": "358", "line": 732, "column": 6, "nodeType": "348", "endLine": 732, "endColumn": 21, "suggestions": "363"}, {"ruleId": "346", "severity": 1, "message": "364", "line": 823, "column": 7, "nodeType": "348", "endLine": 823, "endColumn": 71, "suggestions": "365"}, {"ruleId": "346", "severity": 1, "message": "366", "line": 900, "column": 6, "nodeType": "348", "endLine": 900, "endColumn": 40, "suggestions": "367"}, {"ruleId": "295", "severity": 1, "message": "330", "line": 4, "column": 1, "nodeType": "297", "endLine": 4, "endColumn": 75, "fix": "368"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 5, "column": 1, "nodeType": "297", "endLine": 5, "endColumn": 39, "fix": "369"}, {"ruleId": "295", "severity": 1, "message": "328", "line": 5, "column": 1, "nodeType": "297", "endLine": 5, "endColumn": 39, "fix": "370"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 6, "column": 1, "nodeType": "297", "endLine": 6, "endColumn": 36, "fix": "371"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 7, "column": 1, "nodeType": "297", "endLine": 7, "endColumn": 46, "fix": "372"}, {"ruleId": "295", "severity": 1, "message": "373", "line": 7, "column": 1, "nodeType": "297", "endLine": 7, "endColumn": 46, "fix": "374"}, {"ruleId": "295", "severity": 1, "message": "375", "line": 8, "column": 1, "nodeType": "297", "endLine": 8, "endColumn": 34, "fix": "376"}, {"ruleId": "295", "severity": 1, "message": "330", "line": 4, "column": 1, "nodeType": "297", "endLine": 10, "endColumn": 22, "fix": "377"}, {"ruleId": "378", "severity": 2, "message": "379", "line": 161, "column": 9, "nodeType": "380", "messageId": "381", "endLine": 161, "endColumn": 17, "fix": "382"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 4, "column": 1, "nodeType": "297", "endLine": 4, "endColumn": 50, "fix": "383"}, {"ruleId": "295", "severity": 1, "message": "330", "line": 4, "column": 1, "nodeType": "297", "endLine": 4, "endColumn": 50, "fix": "384"}, {"ruleId": "385", "severity": 1, "message": "386", "line": 151, "column": 19, "nodeType": "387", "endLine": 155, "endColumn": 21}, {"ruleId": "295", "severity": 1, "message": "320", "line": 3, "column": 1, "nodeType": "297", "endLine": 3, "endColumn": 44, "fix": "388"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 5, "column": 1, "nodeType": "297", "endLine": 5, "endColumn": 39, "fix": "389"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 6, "column": 1, "nodeType": "297", "endLine": 6, "endColumn": 42, "fix": "390"}, {"ruleId": "295", "severity": 1, "message": "391", "line": 6, "column": 1, "nodeType": "297", "endLine": 6, "endColumn": 42, "fix": "392"}, {"ruleId": "385", "severity": 1, "message": "386", "line": 149, "column": 17, "nodeType": "387", "endLine": 153, "endColumn": 19}, {"ruleId": "295", "severity": 1, "message": "296", "line": 4, "column": 1, "nodeType": "297", "endLine": 4, "endColumn": 44, "fix": "393"}, {"ruleId": "295", "severity": 1, "message": "318", "line": 4, "column": 1, "nodeType": "297", "endLine": 4, "endColumn": 44, "fix": "394"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 4, "column": 1, "nodeType": "297", "endLine": 10, "endColumn": 22, "fix": "395"}, {"ruleId": "295", "severity": 1, "message": "330", "line": 4, "column": 1, "nodeType": "297", "endLine": 10, "endColumn": 22, "fix": "396"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 29, "column": 41, "nodeType": "352", "messageId": "353", "endLine": 29, "endColumn": 44, "suggestions": "397"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 30, "column": 43, "nodeType": "352", "messageId": "353", "endLine": 30, "endColumn": 46, "suggestions": "398"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 31, "column": 48, "nodeType": "352", "messageId": "353", "endLine": 31, "endColumn": 51, "suggestions": "399"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 32, "column": 44, "nodeType": "352", "messageId": "353", "endLine": 32, "endColumn": 47, "suggestions": "400"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 33, "column": 46, "nodeType": "352", "messageId": "353", "endLine": 33, "endColumn": 49, "suggestions": "401"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 34, "column": 42, "nodeType": "352", "messageId": "353", "endLine": 34, "endColumn": 45, "suggestions": "402"}, {"ruleId": "323", "severity": 1, "message": "324", "line": 47, "column": 5, "nodeType": "325", "messageId": "326", "endLine": 47, "endColumn": 16, "suggestions": "403"}, {"ruleId": "323", "severity": 1, "message": "324", "line": 52, "column": 5, "nodeType": "325", "messageId": "326", "endLine": 52, "endColumn": 16, "suggestions": "404"}, {"ruleId": "385", "severity": 1, "message": "386", "line": 90, "column": 17, "nodeType": "387", "endLine": 94, "endColumn": 19}, {"ruleId": "350", "severity": 1, "message": "351", "line": 105, "column": 65, "nodeType": "352", "messageId": "353", "endLine": 105, "endColumn": 68, "suggestions": "405"}, {"ruleId": "385", "severity": 1, "message": "386", "line": 1360, "column": 19, "nodeType": "387", "endLine": 1364, "endColumn": 21}, {"ruleId": "295", "severity": 1, "message": "320", "line": 3, "column": 1, "nodeType": "297", "endLine": 3, "endColumn": 44, "fix": "406"}, {"ruleId": "323", "severity": 1, "message": "324", "line": 410, "column": 7, "nodeType": "325", "messageId": "326", "endLine": 410, "endColumn": 18, "suggestions": "407"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 3, "column": 1, "nodeType": "297", "endLine": 3, "endColumn": 43, "fix": "408"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 99, "column": 44, "nodeType": "352", "messageId": "353", "endLine": 99, "endColumn": 47, "suggestions": "409"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 124, "column": 21, "nodeType": "352", "messageId": "353", "endLine": 124, "endColumn": 24, "suggestions": "410"}, {"ruleId": "301", "severity": 1, "message": "302", "line": 129, "column": 12, "nodeType": null, "messageId": "303", "endLine": 129, "endColumn": 17}, {"ruleId": "350", "severity": 1, "message": "351", "line": 134, "column": 65, "nodeType": "352", "messageId": "353", "endLine": 134, "endColumn": 68, "suggestions": "411"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 151, "column": 44, "nodeType": "352", "messageId": "353", "endLine": 151, "endColumn": 47, "suggestions": "412"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 1, "column": 1, "nodeType": "297", "endLine": 1, "endColumn": 46, "fix": "413"}, {"ruleId": "323", "severity": 1, "message": "324", "line": 154, "column": 7, "nodeType": "325", "messageId": "326", "endLine": 154, "endColumn": 18, "suggestions": "414"}, {"ruleId": "323", "severity": 1, "message": "324", "line": 155, "column": 7, "nodeType": "325", "messageId": "326", "endLine": 155, "endColumn": 18, "suggestions": "415"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 1, "column": 1, "nodeType": "297", "endLine": 1, "endColumn": 53, "fix": "416"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 29, "column": 32, "nodeType": "352", "messageId": "353", "endLine": 29, "endColumn": 35, "suggestions": "417"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 49, "column": 29, "nodeType": "352", "messageId": "353", "endLine": 49, "endColumn": 32, "suggestions": "418"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 58, "column": 34, "nodeType": "352", "messageId": "353", "endLine": 58, "endColumn": 37, "suggestions": "419"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 96, "column": 30, "nodeType": "352", "messageId": "353", "endLine": 96, "endColumn": 33, "suggestions": "420"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 97, "column": 35, "nodeType": "352", "messageId": "353", "endLine": 97, "endColumn": 38, "suggestions": "421"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 3, "column": 1, "nodeType": "297", "endLine": 3, "endColumn": 65, "fix": "422"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 82, "column": 47, "nodeType": "352", "messageId": "353", "endLine": 82, "endColumn": 50, "suggestions": "423"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 91, "column": 47, "nodeType": "352", "messageId": "353", "endLine": 91, "endColumn": 50, "suggestions": "424"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 191, "column": 46, "nodeType": "352", "messageId": "353", "endLine": 191, "endColumn": 49, "suggestions": "425"}, {"ruleId": "350", "severity": 1, "message": "351", "line": 191, "column": 56, "nodeType": "352", "messageId": "353", "endLine": 191, "endColumn": 59, "suggestions": "426"}, "import/order", "There should be at least one empty line between import groups", "ImportDeclaration", {"range": "427", "text": "428"}, "`@google/generative-ai` import should occur before import of `next/server`", {"range": "429", "text": "430"}, "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", {"range": "431", "text": "428"}, "`@/lib/api-utils` import should occur before import of `@/lib/supabase`", {"range": "432", "text": "433"}, {"range": "434", "text": "428"}, {"range": "435", "text": "428"}, {"range": "436", "text": "437"}, {"range": "438", "text": "428"}, {"range": "439", "text": "428"}, {"range": "440", "text": "428"}, "`react` import should occur after import of `next/link`", {"range": "441", "text": "442"}, "`next/navigation` import should occur after import of `next/link`", {"range": "443", "text": "444"}, {"range": "445", "text": "428"}, "`next/navigation` import should occur before import of `react`", {"range": "446", "text": "447"}, "`react` import should occur after import of `next-themes`", {"range": "448", "text": "449"}, {"range": "450", "text": "428"}, "no-console", "Unexpected console statement. Only these console methods are allowed: warn, error.", "MemberExpression", "limited", ["451"], "`next-themes` import should occur before import of `react`", {"range": "452", "text": "453"}, "`lucide-react` import should occur before import of `react`", {"range": "454", "text": "455"}, "`framer-motion` import should occur before import of `react`", {"range": "456", "text": "457"}, ["458"], ["459"], ["460"], ["461"], ["462"], {"range": "463", "text": "453"}, {"range": "464", "text": "465"}, {"range": "466", "text": "467"}, "`react` import should occur after import of `lucide-react`", {"range": "468", "text": "469"}, "`next-themes` import should occur after import of `lucide-react`", {"range": "470", "text": "471"}, "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'selectedCategory'. Either include it or remove the dependency array.", "ArrayExpression", ["472"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["473", "474"], "React Hook useMemo has missing dependencies: 'filters.showTrends' and 'getChartTheme'. Either include them or remove the dependency array.", ["475"], ["476", "477"], "React Hook useMemo has a missing dependency: 'getChartTheme'. Either include it or remove the dependency array.", ["478"], ["479", "480"], ["481"], ["482", "483"], ["484"], "React Hook useMemo has missing dependencies: 'chartData.performanceMetrics.efficiency' and 'getChartTheme'. Either include them or remove the dependency array. Mutable values like 'chartData.performanceMetrics.efficiency.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["485"], "React Hook useMemo has an unnecessary dependency: 'stats.totalDebtAmount'. Either exclude it or remove the dependency array.", ["486"], {"range": "487", "text": "488"}, {"range": "489", "text": "428"}, {"range": "490", "text": "491"}, {"range": "492", "text": "428"}, {"range": "493", "text": "428"}, "`@/lib/supabase` import should occur before import of `./DebtModal`", {"range": "494", "text": "495"}, "`date-fns` import should occur before import of `react`", {"range": "496", "text": "497"}, {"range": "498", "text": "499"}, "prefer-const", "'filtered' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "500", "text": "501"}, {"range": "502", "text": "428"}, {"range": "503", "text": "504"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", {"range": "505", "text": "506"}, {"range": "507", "text": "428"}, {"range": "508", "text": "428"}, "`./ProductModal` import should occur after import of `@/lib/supabase`", {"range": "509", "text": "510"}, {"range": "511", "text": "428"}, {"range": "512", "text": "447"}, {"range": "513", "text": "428"}, {"range": "514", "text": "515"}, ["516", "517"], ["518", "519"], ["520", "521"], ["522", "523"], ["524", "525"], ["526", "527"], ["528"], ["529"], ["530", "531"], {"range": "532", "text": "533"}, ["534"], {"range": "535", "text": "428"}, ["536", "537"], ["538", "539"], ["540", "541"], ["542", "543"], {"range": "544", "text": "428"}, ["545"], ["546"], {"range": "547", "text": "428"}, ["548", "549"], ["550", "551"], ["552", "553"], ["554", "555"], ["556", "557"], {"range": "558", "text": "428"}, ["559", "560"], ["561", "562"], ["563", "564"], ["565", "566"], [114, 114], "\n", [0, 115], "import { GoogleGenerativeAI } from '@google/generative-ai'\nimport { NextRequest, NextResponse } from 'next/server'\n", [55, 55], [56, 248], "import {\n  successResponse,\n  withError<PERSON>andler,\n  handleDatabaseError,\n  parsePaginationParams,\n  handleCorsPreflightRequest\n} from '@/lib/api-utils'\nimport { supabase } from '@/lib/supabase'\n", [55, 55], [41, 41], [42, 300], "import {\n  successResponse,\n  errorResponse,\n  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  validateRequestBody,\n  validateRequiredFields,\n  handleDatabaseError,\n  parsePaginationParams,\n  handleCorsPreflightRequest\n} from '@/lib/api-utils'\nimport { supabase } from '@/lib/supabase'\n", [55, 55], [55, 55], [88, 88], [14, 244], "import { useRouter } from 'next/navigation'\nimport { motion } from 'framer-motion'\nimport { Eye, EyeOff, Lock, Mail, ArrowRight, Store } from 'lucide-react'\nimport Link from 'next/link'\nimport { useState, useEffect } from 'react'\n", [58, 244], "import { motion } from 'framer-motion'\nimport { Eye, EyeOff, Lock, Mail, ArrowRight, Store } from 'lucide-react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\n", [243, 243], [14, 92], "import { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n", [14, 218], "import { Search, Home, Package, Users, Image, Moon, Sun, LogOut, User } from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [217, 217], {"fix": "567", "messageId": "568", "data": "569", "desc": "570"}, [14, 105], "import { useTheme } from 'next-themes'\nimport { useState, useRef, useEffect } from 'react'\n", [14, 259], "import {\n  Send,\n  <PERSON>t,\n  User,\n  Loader2,\n  <PERSON><PERSON><PERSON>,\n  X,\n  Minimize2,\n  Maximize2,\n  Mic,\n  Square,\n  RotateCcw,\n  Copy,\n  Check\n} from 'lucide-react'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\n", [14, 315], "import { motion, AnimatePresence } from 'framer-motion'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\nimport {\n  Send,\n  Bot,\n  User,\n  Loader2,\n  Sparkles,\n  X,\n  Minimize2,\n  Maximize2,\n  Mic,\n  Square,\n  RotateCcw,\n  Copy,\n  Check\n} from 'lucide-react'\n", {"fix": "571", "messageId": "568", "data": "572", "desc": "570"}, {"fix": "573", "messageId": "568", "data": "574", "desc": "570"}, {"fix": "575", "messageId": "568", "data": "576", "desc": "570"}, {"fix": "577", "messageId": "568", "data": "578", "desc": "570"}, {"fix": "579", "messageId": "568", "data": "580", "desc": "570"}, [14, 105], [14, 264], "import {\n  Bot,\n  User,\n  Send,\n  Loader2,\n  TrendingUp,\n  Package,\n  CreditCard,\n  BarChart3,\n  Lightbulb,\n  HelpCircle,\n  Zap,\n  Brain\n} from 'lucide-react'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\n", [14, 303], "import { motion } from 'framer-motion'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\nimport {\n  Bot,\n  User,\n  Send,\n  Loader2,\n  TrendingUp,\n  Package,\n  CreditCard,\n  BarChart3,\n  Lightbulb,\n  HelpCircle,\n  Zap,\n  Brain\n} from 'lucide-react'\n", [14, 398], "import ReactECharts from 'echarts-for-react'\nimport { useTheme } from 'next-themes'\nimport {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\nimport { useEffect, useState, useMemo, useCallback } from 'react'\n", [125, 398], "import {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\n", {"desc": "581", "fix": "582"}, {"messageId": "583", "fix": "584", "desc": "585"}, {"messageId": "586", "fix": "587", "desc": "588"}, {"desc": "589", "fix": "590"}, {"messageId": "583", "fix": "591", "desc": "585"}, {"messageId": "586", "fix": "592", "desc": "588"}, {"desc": "593", "fix": "594"}, {"messageId": "583", "fix": "595", "desc": "585"}, {"messageId": "586", "fix": "596", "desc": "588"}, {"desc": "597", "fix": "598"}, {"messageId": "583", "fix": "599", "desc": "585"}, {"messageId": "586", "fix": "600", "desc": "588"}, {"desc": "601", "fix": "602"}, {"desc": "603", "fix": "604"}, {"desc": "605", "fix": "606"}, [14, 133], "import { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n", [171, 171], [14, 172], "import { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\n", [207, 207], [253, 253], [172, 254], "import { CustomerDebt } from '@/lib/supabase'\nimport DebtModal from './DebtModal'\n", [14, 288], "import { format } from 'date-fns'\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport DebtModal from './DebtModal'\nimport { CustomerDebt } from '@/lib/supabase'\n", [14, 372], "import {\n  Upload, Heart, Share2, Download, Trash2, Plus, Image as ImageIcon,\n  Search, Filter, Grid, List, Calendar, Eye, Star, Users, Camera,\n  Play, Pause, ChevronLeft, ChevronRight, X,\n  FolderOpen, Tag, Clock, Activity,\n  Settings, MoreVertical, Edit, RefreshCw, SortAsc, SortDesc\n} from 'lucide-react'\nimport { useState, useMemo, useRef } from 'react'\n", [4832, 5306], "const filtered = photos.filter(photo => {\n      const matchesSearch = searchTerm === '' ||\n        photo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        photo.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        photo.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))\n\n      const matchesCategory = selectedCategory === 'all' || photo.category === selectedCategory\n\n      return matchesSearch && matchesCategory\n    })", [107, 107], [14, 108], "import { X, Upload, Package } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n", [14, 164], "import { Plus, Edit, Trash2, Search, Package } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [163, 163], [205, 205], [164, 267], "import { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\nimport ProductModal from './ProductModal'\n", [91, 91], [14, 92], [381, 381], [14, 382], "import {\n  Save, User, Store, Bell, Shield, Palette, Database, Download, Upload,\n  MapPin, Clock, Camera, Eye, EyeOff, Key, Smartphone, Globe,\n  Mail, Phone, Trash2, Plus, Edit3, Check, AlertTriangle,\n  Monitor, Sun, Moon, Palette as PaletteIcon, Type, Layout, Zap,\n  Cloud, HardDrive, RefreshCw, Archive\n} from 'lucide-react'\nimport { useState, useRef } from 'react'\n", {"messageId": "583", "fix": "607", "desc": "585"}, {"messageId": "586", "fix": "608", "desc": "588"}, {"messageId": "583", "fix": "609", "desc": "585"}, {"messageId": "586", "fix": "610", "desc": "588"}, {"messageId": "583", "fix": "611", "desc": "585"}, {"messageId": "586", "fix": "612", "desc": "588"}, {"messageId": "583", "fix": "613", "desc": "585"}, {"messageId": "586", "fix": "614", "desc": "588"}, {"messageId": "583", "fix": "615", "desc": "585"}, {"messageId": "586", "fix": "616", "desc": "588"}, {"messageId": "583", "fix": "617", "desc": "585"}, {"messageId": "586", "fix": "618", "desc": "588"}, {"fix": "619", "messageId": "568", "data": "620", "desc": "570"}, {"fix": "621", "messageId": "568", "data": "622", "desc": "570"}, {"messageId": "583", "fix": "623", "desc": "585"}, {"messageId": "586", "fix": "624", "desc": "588"}, [14, 213], "import {\n  BarChart3,\n  History,\n  Calendar,\n  Settings,\n  ChevronLeft,\n  ChevronRight,\n  Bot\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", {"fix": "625", "messageId": "568", "data": "626", "desc": "570"}, [114, 114], {"messageId": "583", "fix": "627", "desc": "585"}, {"messageId": "586", "fix": "628", "desc": "588"}, {"messageId": "583", "fix": "629", "desc": "585"}, {"messageId": "586", "fix": "630", "desc": "588"}, {"messageId": "583", "fix": "631", "desc": "585"}, {"messageId": "586", "fix": "632", "desc": "588"}, {"messageId": "583", "fix": "633", "desc": "585"}, {"messageId": "586", "fix": "634", "desc": "588"}, [45, 45], {"fix": "635", "messageId": "568", "data": "636", "desc": "570"}, {"fix": "637", "messageId": "568", "data": "638", "desc": "570"}, [52, 52], {"messageId": "583", "fix": "639", "desc": "585"}, {"messageId": "586", "fix": "640", "desc": "588"}, {"messageId": "583", "fix": "641", "desc": "585"}, {"messageId": "586", "fix": "642", "desc": "588"}, {"messageId": "583", "fix": "643", "desc": "585"}, {"messageId": "586", "fix": "644", "desc": "588"}, {"messageId": "583", "fix": "645", "desc": "585"}, {"messageId": "586", "fix": "646", "desc": "588"}, {"messageId": "583", "fix": "647", "desc": "585"}, {"messageId": "586", "fix": "648", "desc": "588"}, [93, 93], {"messageId": "583", "fix": "649", "desc": "585"}, {"messageId": "586", "fix": "650", "desc": "588"}, {"messageId": "583", "fix": "651", "desc": "585"}, {"messageId": "586", "fix": "652", "desc": "588"}, {"messageId": "583", "fix": "653", "desc": "585"}, {"messageId": "586", "fix": "654", "desc": "588"}, {"messageId": "583", "fix": "655", "desc": "585"}, {"messageId": "586", "fix": "656", "desc": "588"}, {"range": "657", "text": "658"}, "removeConsole", {"propertyName": "659"}, "Remove the console.log().", {"range": "660", "text": "658"}, {"propertyName": "659"}, {"range": "661", "text": "658"}, {"propertyName": "659"}, {"range": "662", "text": "658"}, {"propertyName": "659"}, {"range": "663", "text": "658"}, {"propertyName": "659"}, {"range": "664", "text": "658"}, {"propertyName": "659"}, "Update the dependencies array to be: [selectedCategory, stats]", {"range": "665", "text": "666"}, "suggestUnknown", {"range": "667", "text": "668"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "669", "text": "670"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [chartData.salesData, filters.chartType, filters.showTrends, getChartTheme, resolvedTheme]", {"range": "671", "text": "672"}, {"range": "673", "text": "668"}, {"range": "674", "text": "670"}, "Update the dependencies array to be: [chartData.debtData, getChartTheme, resolvedTheme]", {"range": "675", "text": "676"}, {"range": "677", "text": "668"}, {"range": "678", "text": "670"}, "Update the dependencies array to be: [chartData.categoryData, getChartTheme, resolvedTheme]", {"range": "679", "text": "680"}, {"range": "681", "text": "668"}, {"range": "682", "text": "670"}, "Update the dependencies array to be: [getChartTheme, resolvedTheme]", {"range": "683", "text": "684"}, "Update the dependencies array to be: [chartData.performanceMetrics.efficiency, getChartTheme, resolvedTheme]", {"range": "685", "text": "686"}, "Update the dependencies array to be: [chartData]", {"range": "687", "text": "688"}, {"range": "689", "text": "668"}, {"range": "690", "text": "670"}, {"range": "691", "text": "668"}, {"range": "692", "text": "670"}, {"range": "693", "text": "668"}, {"range": "694", "text": "670"}, {"range": "695", "text": "668"}, {"range": "696", "text": "670"}, {"range": "697", "text": "668"}, {"range": "698", "text": "670"}, {"range": "699", "text": "668"}, {"range": "700", "text": "670"}, {"range": "701", "text": "658"}, {"propertyName": "659"}, {"range": "702", "text": "658"}, {"propertyName": "659"}, {"range": "703", "text": "668"}, {"range": "704", "text": "670"}, {"range": "705", "text": "658"}, {"propertyName": "659"}, {"range": "706", "text": "668"}, {"range": "707", "text": "670"}, {"range": "708", "text": "668"}, {"range": "709", "text": "670"}, {"range": "710", "text": "668"}, {"range": "711", "text": "670"}, {"range": "712", "text": "668"}, {"range": "713", "text": "670"}, {"range": "714", "text": "658"}, {"propertyName": "659"}, {"range": "715", "text": "658"}, {"propertyName": "659"}, {"range": "716", "text": "668"}, {"range": "717", "text": "670"}, {"range": "718", "text": "668"}, {"range": "719", "text": "670"}, {"range": "720", "text": "668"}, {"range": "721", "text": "670"}, {"range": "722", "text": "668"}, {"range": "723", "text": "670"}, {"range": "724", "text": "668"}, {"range": "725", "text": "670"}, {"range": "726", "text": "668"}, {"range": "727", "text": "670"}, {"range": "728", "text": "668"}, {"range": "729", "text": "670"}, {"range": "730", "text": "668"}, {"range": "731", "text": "670"}, {"range": "732", "text": "668"}, {"range": "733", "text": "670"}, [1430, 1472], "", "log", [6925, 7016], [7102, 7187], [7273, 7363], [7558, 7634], [7708, 7789], [6482, 6489], "[selected<PERSON>ategory, stats]", [7642, 7645], "unknown", [7642, 7645], "never", [11678, 11733], "[chartData.salesData, filters.chartType, filters.showTrends, getChartTheme, resolvedTheme]", [12390, 12393], [12390, 12393], [15425, 15460], "[chartData.debtData, getChartTheme, resolvedTheme]", [16120, 16123], [16120, 16123], [18522, 18561], "[chartData.categoryData, getChartTheme, resolvedTheme]", [19638, 19641], [19638, 19641], [21439, 21454], "[getChartTheme, resolvedTheme]", [23570, 23634], "[chartData.performanceMetrics.efficiency, getChartTheme, resolvedTheme]", [27272, 27306], "[chartData]", [1201, 1204], [1201, 1204], [1284, 1287], [1284, 1287], [1374, 1377], [1374, 1377], [1466, 1469], [1466, 1469], [1555, 1558], [1555, 1558], [1642, 1645], [1642, 1645], [1982, 2014], [2131, 2163], [4172, 4175], [4172, 4175], [9559, 9612], [2131, 2134], [2131, 2134], [2784, 2787], [2784, 2787], [3046, 3049], [3046, 3049], [3475, 3478], [3475, 3478], [4201, 4262], [4269, 4475], [633, 636], [633, 636], [1044, 1047], [1044, 1047], [1211, 1214], [1211, 1214], [1967, 1970], [1967, 1970], [2027, 2030], [2027, 2030], [2410, 2413], [2410, 2413], [2612, 2615], [2612, 2615], [5337, 5340], [5337, 5340], [5347, 5350], [5347, 5350]]