[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\ai\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\speech-to-text\\route.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AIAssistant.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AISupport.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts": "39"}, {"size": 5708, "mtime": 1752515097450, "results": "40", "hashOfConfig": "41"}, {"size": 3713, "mtime": 1752667904512, "results": "42", "hashOfConfig": "41"}, {"size": 2769, "mtime": 1752667738579, "results": "43", "hashOfConfig": "41"}, {"size": 2675, "mtime": 1752667748235, "results": "44", "hashOfConfig": "41"}, {"size": 2624, "mtime": 1752667766642, "results": "45", "hashOfConfig": "41"}, {"size": 2413, "mtime": 1752667776200, "results": "46", "hashOfConfig": "41"}, {"size": 6060, "mtime": 1752667359628, "results": "47", "hashOfConfig": "41"}, {"size": 1416, "mtime": 1752667790738, "results": "48", "hashOfConfig": "41"}, {"size": 11603, "mtime": 1752190925916, "results": "49", "hashOfConfig": "41"}, {"size": 1496, "mtime": 1752667805767, "results": "50", "hashOfConfig": "41"}, {"size": 8375, "mtime": 1752667821109, "results": "51", "hashOfConfig": "41"}, {"size": 753, "mtime": 1752667835295, "results": "52", "hashOfConfig": "41"}, {"size": 10478, "mtime": 1752501745892, "results": "53", "hashOfConfig": "41"}, {"size": 42086, "mtime": 1752667892061, "results": "54", "hashOfConfig": "41"}, {"size": 13127, "mtime": 1752561102360, "results": "55", "hashOfConfig": "41"}, {"size": 56392, "mtime": 1752667532295, "results": "56", "hashOfConfig": "41"}, {"size": 32560, "mtime": 1752667568264, "results": "57", "hashOfConfig": "41"}, {"size": 30994, "mtime": 1752510024594, "results": "58", "hashOfConfig": "41"}, {"size": 7440, "mtime": 1752324845264, "results": "59", "hashOfConfig": "41"}, {"size": 7823, "mtime": 1752061478940, "results": "60", "hashOfConfig": "41"}, {"size": 63258, "mtime": 1752667935683, "results": "61", "hashOfConfig": "41"}, {"size": 61481, "mtime": 1752667483151, "results": "62", "hashOfConfig": "41"}, {"size": 1308, "mtime": 1752515037632, "results": "63", "hashOfConfig": "41"}, {"size": 1188, "mtime": 1751950590317, "results": "64", "hashOfConfig": "41"}, {"size": 8850, "mtime": 1751938233472, "results": "65", "hashOfConfig": "41"}, {"size": 9147, "mtime": 1752061416183, "results": "66", "hashOfConfig": "41"}, {"size": 1905, "mtime": 1751950118275, "results": "67", "hashOfConfig": "41"}, {"size": 67119, "mtime": 1752667953326, "results": "68", "hashOfConfig": "41"}, {"size": 18126, "mtime": 1752514964304, "results": "69", "hashOfConfig": "41"}, {"size": 344, "mtime": 1752212829480, "results": "70", "hashOfConfig": "41"}, {"size": 3296, "mtime": 1752212374913, "results": "71", "hashOfConfig": "41"}, {"size": 2521, "mtime": 1751950064561, "results": "72", "hashOfConfig": "41"}, {"size": 10471, "mtime": 1752281679844, "results": "73", "hashOfConfig": "41"}, {"size": 5120, "mtime": 1752212844330, "results": "74", "hashOfConfig": "41"}, {"size": 1025, "mtime": 1752217088143, "results": "75", "hashOfConfig": "41"}, {"size": 4937, "mtime": 1752513719322, "results": "76", "hashOfConfig": "41"}, {"size": 1266, "mtime": 1752212686019, "results": "77", "hashOfConfig": "41"}, {"size": 2234, "mtime": 1752217075185, "results": "78", "hashOfConfig": "41"}, {"size": 5694, "mtime": 1752212408642, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10tv68i", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 7, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\ai\\route.ts", ["197", "198"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\speech-to-text\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx", ["199", "200"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx", ["201", "202", "203"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AIAssistant.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AISupport.tsx", ["204", "205", "206", "207"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx", ["208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx", ["221", "222", "223", "224", "225", "226", "227"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx", ["228", "229", "230"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx", ["231", "232", "233", "234", "235"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx", ["236", "237"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx", ["238", "239", "240", "241", "242", "243", "244", "245", "246", "247", "248"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx", ["249"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx", ["250"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts", ["251", "252", "253", "254", "255", "256"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts", ["257"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts", ["258", "259"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts", ["260"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts", ["261", "262", "263", "264", "265"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts", ["266", "267", "268", "269", "270"], [], {"ruleId": "271", "severity": 1, "message": "272", "line": 1, "column": 1, "nodeType": "273", "endLine": 1, "endColumn": 59, "fix": "274"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 3, "column": 1, "nodeType": "273", "endLine": 3, "endColumn": 56, "fix": "276"}, {"ruleId": "271", "severity": 1, "message": "277", "line": 6, "column": 1, "nodeType": "273", "endLine": 6, "endColumn": 39, "fix": "278"}, {"ruleId": "271", "severity": 1, "message": "279", "line": 7, "column": 1, "nodeType": "273", "endLine": 7, "endColumn": 74, "fix": "280"}, {"ruleId": "271", "severity": 1, "message": "281", "line": 3, "column": 1, "nodeType": "273", "endLine": 3, "endColumn": 44, "fix": "282"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 6, "column": 1, "nodeType": "273", "endLine": 6, "endColumn": 39, "fix": "283"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 58, "column": 5, "nodeType": "286", "messageId": "287", "endLine": 58, "endColumn": 16, "suggestions": "288"}, {"ruleId": "271", "severity": 1, "message": "289", "line": 4, "column": 1, "nodeType": "273", "endLine": 4, "endColumn": 39, "fix": "290"}, {"ruleId": "271", "severity": 1, "message": "291", "line": 5, "column": 1, "nodeType": "273", "endLine": 18, "endColumn": 22, "fix": "292"}, {"ruleId": "271", "severity": 1, "message": "293", "line": 19, "column": 1, "nodeType": "273", "endLine": 19, "endColumn": 39, "fix": "294"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 130, "column": 14, "nodeType": null, "messageId": "297", "endLine": 130, "endColumn": 19}, {"ruleId": "271", "severity": 1, "message": "298", "line": 3, "column": 1, "nodeType": "273", "endLine": 3, "endColumn": 66, "fix": "299"}, {"ruleId": "271", "severity": 1, "message": "300", "line": 5, "column": 1, "nodeType": "273", "endLine": 5, "endColumn": 39, "fix": "301"}, {"ruleId": "302", "severity": 1, "message": "303", "line": 203, "column": 6, "nodeType": "304", "endLine": 203, "endColumn": 13, "suggestions": "305"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 241, "column": 27, "nodeType": "308", "messageId": "309", "endLine": 241, "endColumn": 30, "suggestions": "310"}, {"ruleId": "302", "severity": 1, "message": "311", "line": 391, "column": 7, "nodeType": "304", "endLine": 391, "endColumn": 62, "suggestions": "312"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 413, "column": 27, "nodeType": "308", "messageId": "309", "endLine": 413, "endColumn": 30, "suggestions": "313"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 529, "column": 7, "nodeType": "304", "endLine": 529, "endColumn": 42, "suggestions": "315"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 551, "column": 27, "nodeType": "308", "messageId": "309", "endLine": 551, "endColumn": 30, "suggestions": "316"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 631, "column": 7, "nodeType": "304", "endLine": 631, "endColumn": 46, "suggestions": "317"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 665, "column": 29, "nodeType": "308", "messageId": "309", "endLine": 665, "endColumn": 32, "suggestions": "318"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 732, "column": 6, "nodeType": "304", "endLine": 732, "endColumn": 21, "suggestions": "319"}, {"ruleId": "302", "severity": 1, "message": "320", "line": 823, "column": 7, "nodeType": "304", "endLine": 823, "endColumn": 71, "suggestions": "321"}, {"ruleId": "302", "severity": 1, "message": "322", "line": 900, "column": 6, "nodeType": "304", "endLine": 900, "endColumn": 40, "suggestions": "323"}, {"ruleId": "271", "severity": 1, "message": "291", "line": 4, "column": 1, "nodeType": "273", "endLine": 4, "endColumn": 75, "fix": "324"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 5, "column": 1, "nodeType": "273", "endLine": 5, "endColumn": 39, "fix": "325"}, {"ruleId": "271", "severity": 1, "message": "289", "line": 5, "column": 1, "nodeType": "273", "endLine": 5, "endColumn": 39, "fix": "326"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 6, "column": 1, "nodeType": "273", "endLine": 6, "endColumn": 36, "fix": "327"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 7, "column": 1, "nodeType": "273", "endLine": 7, "endColumn": 46, "fix": "328"}, {"ruleId": "271", "severity": 1, "message": "329", "line": 7, "column": 1, "nodeType": "273", "endLine": 7, "endColumn": 46, "fix": "330"}, {"ruleId": "271", "severity": 1, "message": "331", "line": 8, "column": 1, "nodeType": "273", "endLine": 8, "endColumn": 34, "fix": "332"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 4, "column": 1, "nodeType": "273", "endLine": 4, "endColumn": 50, "fix": "333"}, {"ruleId": "271", "severity": 1, "message": "291", "line": 4, "column": 1, "nodeType": "273", "endLine": 4, "endColumn": 50, "fix": "334"}, {"ruleId": "335", "severity": 1, "message": "336", "line": 151, "column": 19, "nodeType": "337", "endLine": 155, "endColumn": 21}, {"ruleId": "271", "severity": 1, "message": "281", "line": 3, "column": 1, "nodeType": "273", "endLine": 3, "endColumn": 44, "fix": "338"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 5, "column": 1, "nodeType": "273", "endLine": 5, "endColumn": 39, "fix": "339"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 6, "column": 1, "nodeType": "273", "endLine": 6, "endColumn": 42, "fix": "340"}, {"ruleId": "271", "severity": 1, "message": "341", "line": 6, "column": 1, "nodeType": "273", "endLine": 6, "endColumn": 42, "fix": "342"}, {"ruleId": "335", "severity": 1, "message": "336", "line": 149, "column": 17, "nodeType": "337", "endLine": 153, "endColumn": 19}, {"ruleId": "271", "severity": 1, "message": "275", "line": 4, "column": 1, "nodeType": "273", "endLine": 4, "endColumn": 44, "fix": "343"}, {"ruleId": "271", "severity": 1, "message": "344", "line": 4, "column": 1, "nodeType": "273", "endLine": 4, "endColumn": 44, "fix": "345"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 4, "column": 1, "nodeType": "273", "endLine": 10, "endColumn": 22, "fix": "346"}, {"ruleId": "271", "severity": 1, "message": "291", "line": 4, "column": 1, "nodeType": "273", "endLine": 10, "endColumn": 22, "fix": "347"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 29, "column": 41, "nodeType": "308", "messageId": "309", "endLine": 29, "endColumn": 44, "suggestions": "348"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 30, "column": 43, "nodeType": "308", "messageId": "309", "endLine": 30, "endColumn": 46, "suggestions": "349"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 31, "column": 48, "nodeType": "308", "messageId": "309", "endLine": 31, "endColumn": 51, "suggestions": "350"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 32, "column": 44, "nodeType": "308", "messageId": "309", "endLine": 32, "endColumn": 47, "suggestions": "351"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 33, "column": 46, "nodeType": "308", "messageId": "309", "endLine": 33, "endColumn": 49, "suggestions": "352"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 34, "column": 42, "nodeType": "308", "messageId": "309", "endLine": 34, "endColumn": 45, "suggestions": "353"}, {"ruleId": "335", "severity": 1, "message": "336", "line": 90, "column": 17, "nodeType": "337", "endLine": 94, "endColumn": 19}, {"ruleId": "306", "severity": 1, "message": "307", "line": 105, "column": 65, "nodeType": "308", "messageId": "309", "endLine": 105, "endColumn": 68, "suggestions": "354"}, {"ruleId": "335", "severity": 1, "message": "336", "line": 1360, "column": 19, "nodeType": "337", "endLine": 1364, "endColumn": 21}, {"ruleId": "271", "severity": 1, "message": "281", "line": 3, "column": 1, "nodeType": "273", "endLine": 3, "endColumn": 44, "fix": "355"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 410, "column": 7, "nodeType": "286", "messageId": "287", "endLine": 410, "endColumn": 18, "suggestions": "356"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 3, "column": 1, "nodeType": "273", "endLine": 3, "endColumn": 43, "fix": "357"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 99, "column": 44, "nodeType": "308", "messageId": "309", "endLine": 99, "endColumn": 47, "suggestions": "358"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 124, "column": 21, "nodeType": "308", "messageId": "309", "endLine": 124, "endColumn": 24, "suggestions": "359"}, {"ruleId": "295", "severity": 1, "message": "296", "line": 129, "column": 12, "nodeType": null, "messageId": "297", "endLine": 129, "endColumn": 17}, {"ruleId": "306", "severity": 1, "message": "307", "line": 134, "column": 65, "nodeType": "308", "messageId": "309", "endLine": 134, "endColumn": 68, "suggestions": "360"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 151, "column": 44, "nodeType": "308", "messageId": "309", "endLine": 151, "endColumn": 47, "suggestions": "361"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 1, "column": 1, "nodeType": "273", "endLine": 1, "endColumn": 46, "fix": "362"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 154, "column": 7, "nodeType": "286", "messageId": "287", "endLine": 154, "endColumn": 18, "suggestions": "363"}, {"ruleId": "284", "severity": 1, "message": "285", "line": 155, "column": 7, "nodeType": "286", "messageId": "287", "endLine": 155, "endColumn": 18, "suggestions": "364"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 1, "column": 1, "nodeType": "273", "endLine": 1, "endColumn": 53, "fix": "365"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 29, "column": 32, "nodeType": "308", "messageId": "309", "endLine": 29, "endColumn": 35, "suggestions": "366"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 49, "column": 29, "nodeType": "308", "messageId": "309", "endLine": 49, "endColumn": 32, "suggestions": "367"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 58, "column": 34, "nodeType": "308", "messageId": "309", "endLine": 58, "endColumn": 37, "suggestions": "368"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 96, "column": 30, "nodeType": "308", "messageId": "309", "endLine": 96, "endColumn": 33, "suggestions": "369"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 97, "column": 35, "nodeType": "308", "messageId": "309", "endLine": 97, "endColumn": 38, "suggestions": "370"}, {"ruleId": "271", "severity": 1, "message": "275", "line": 3, "column": 1, "nodeType": "273", "endLine": 3, "endColumn": 65, "fix": "371"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 82, "column": 47, "nodeType": "308", "messageId": "309", "endLine": 82, "endColumn": 50, "suggestions": "372"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 91, "column": 47, "nodeType": "308", "messageId": "309", "endLine": 91, "endColumn": 50, "suggestions": "373"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 191, "column": 46, "nodeType": "308", "messageId": "309", "endLine": 191, "endColumn": 49, "suggestions": "374"}, {"ruleId": "306", "severity": 1, "message": "307", "line": 191, "column": 56, "nodeType": "308", "messageId": "309", "endLine": 191, "endColumn": 59, "suggestions": "375"}, "import/order", "There should be no empty line within import group", "ImportDeclaration", {"range": "376", "text": "377"}, "There should be at least one empty line between import groups", {"range": "378", "text": "379"}, "`framer-motion` import should occur before import of `next/link`", {"range": "380", "text": "381"}, "`lucide-react` import should occur before import of `next/link`", {"range": "382", "text": "383"}, "`react` import should occur after import of `next-themes`", {"range": "384", "text": "385"}, {"range": "386", "text": "379"}, "no-console", "Unexpected console statement. Only these console methods are allowed: warn, error.", "MemberExpression", "limited", ["387"], "`next-themes` import should occur before import of `react`", {"range": "388", "text": "389"}, "`lucide-react` import should occur before import of `react`", {"range": "390", "text": "391"}, "`framer-motion` import should occur before import of `react`", {"range": "392", "text": "393"}, "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "`react` import should occur after import of `lucide-react`", {"range": "394", "text": "395"}, "`next-themes` import should occur after import of `lucide-react`", {"range": "396", "text": "397"}, "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'selectedCategory'. Either include it or remove the dependency array.", "ArrayExpression", ["398"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["399", "400"], "React Hook useMemo has missing dependencies: 'filters.showTrends' and 'getChartTheme'. Either include them or remove the dependency array.", ["401"], ["402", "403"], "React Hook useMemo has a missing dependency: 'getChartTheme'. Either include it or remove the dependency array.", ["404"], ["405", "406"], ["407"], ["408", "409"], ["410"], "React Hook useMemo has missing dependencies: 'chartData.performanceMetrics.efficiency' and 'getChartTheme'. Either include them or remove the dependency array. Mutable values like 'chartData.performanceMetrics.efficiency.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["411"], "React Hook useMemo has an unnecessary dependency: 'stats.totalDebtAmount'. Either exclude it or remove the dependency array.", ["412"], {"range": "413", "text": "414"}, {"range": "415", "text": "379"}, {"range": "416", "text": "417"}, {"range": "418", "text": "379"}, {"range": "419", "text": "379"}, "`@/lib/supabase` import should occur before import of `./DebtModal`", {"range": "420", "text": "421"}, "`date-fns` import should occur before import of `react`", {"range": "422", "text": "423"}, {"range": "424", "text": "379"}, {"range": "425", "text": "426"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", {"range": "427", "text": "428"}, {"range": "429", "text": "379"}, {"range": "430", "text": "379"}, "`./ProductModal` import should occur after import of `@/lib/supabase`", {"range": "431", "text": "432"}, {"range": "433", "text": "379"}, "`next/navigation` import should occur before import of `react`", {"range": "434", "text": "435"}, {"range": "436", "text": "379"}, {"range": "437", "text": "438"}, ["439", "440"], ["441", "442"], ["443", "444"], ["445", "446"], ["447", "448"], ["449", "450"], ["451", "452"], {"range": "453", "text": "454"}, ["455"], {"range": "456", "text": "379"}, ["457", "458"], ["459", "460"], ["461", "462"], ["463", "464"], {"range": "465", "text": "379"}, ["466"], ["467"], {"range": "468", "text": "379"}, ["469", "470"], ["471", "472"], ["473", "474"], ["475", "476"], ["477", "478"], {"range": "479", "text": "379"}, ["480", "481"], ["482", "483"], ["484", "485"], ["486", "487"], [59, 60], "", [115, 115], "\n", [14, 170], "import { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState, useEffect } from 'react'\n", [14, 244], "import { Eye, EyeOff, Lock, Mail, ArrowRight, Store } from 'lucide-react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\n", [14, 218], "import { Search, Home, Package, Users, Image, Moon, Sun, LogOut, User } from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [217, 217], {"fix": "488", "messageId": "489", "data": "490", "desc": "491"}, [14, 105], "import { useTheme } from 'next-themes'\nimport { useState, useRef, useEffect } from 'react'\n", [14, 264], "import {\n  Bot,\n  User,\n  Send,\n  Loader2,\n  TrendingUp,\n  Package,\n  CreditCard,\n  BarChart3,\n  Lightbulb,\n  HelpCircle,\n  Zap,\n  Brain\n} from 'lucide-react'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\n", [14, 303], "import { motion } from 'framer-motion'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\nimport {\n  Bot,\n  User,\n  Send,\n  Loader2,\n  TrendingUp,\n  Package,\n  CreditCard,\n  BarChart3,\n  Lightbulb,\n  HelpCircle,\n  Zap,\n  Brain\n} from 'lucide-react'\n", [14, 398], "import ReactECharts from 'echarts-for-react'\nimport { useTheme } from 'next-themes'\nimport {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\nimport { useEffect, useState, useMemo, useCallback } from 'react'\n", [125, 398], "import {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\n", {"desc": "492", "fix": "493"}, {"messageId": "494", "fix": "495", "desc": "496"}, {"messageId": "497", "fix": "498", "desc": "499"}, {"desc": "500", "fix": "501"}, {"messageId": "494", "fix": "502", "desc": "496"}, {"messageId": "497", "fix": "503", "desc": "499"}, {"desc": "504", "fix": "505"}, {"messageId": "494", "fix": "506", "desc": "496"}, {"messageId": "497", "fix": "507", "desc": "499"}, {"desc": "508", "fix": "509"}, {"messageId": "494", "fix": "510", "desc": "496"}, {"messageId": "497", "fix": "511", "desc": "499"}, {"desc": "512", "fix": "513"}, {"desc": "514", "fix": "515"}, {"desc": "516", "fix": "517"}, [14, 133], "import { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n", [171, 171], [14, 172], "import { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\n", [207, 207], [253, 253], [172, 254], "import { CustomerDebt } from '@/lib/supabase'\nimport DebtModal from './DebtModal'\n", [14, 288], "import { format } from 'date-fns'\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport DebtModal from './DebtModal'\nimport { CustomerDebt } from '@/lib/supabase'\n", [107, 107], [14, 108], "import { X, Upload, Package } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n", [14, 164], "import { Plus, Edit, Trash2, Search, Package } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [163, 163], [205, 205], [164, 267], "import { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\nimport ProductModal from './ProductModal'\n", [91, 91], [14, 92], "import { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n", [381, 381], [14, 382], "import {\n  Save, User, Store, Bell, Shield, Palette, Database, Download, Upload,\n  MapPin, Clock, Camera, Eye, EyeOff, Key, Smartphone, Globe,\n  Mail, Phone, Trash2, Plus, Edit3, Check, AlertTriangle,\n  Monitor, Sun, Moon, Palette as PaletteIcon, Type, Layout, Zap,\n  Cloud, HardDrive, RefreshCw, Archive\n} from 'lucide-react'\nimport { useState, useRef } from 'react'\n", {"messageId": "494", "fix": "518", "desc": "496"}, {"messageId": "497", "fix": "519", "desc": "499"}, {"messageId": "494", "fix": "520", "desc": "496"}, {"messageId": "497", "fix": "521", "desc": "499"}, {"messageId": "494", "fix": "522", "desc": "496"}, {"messageId": "497", "fix": "523", "desc": "499"}, {"messageId": "494", "fix": "524", "desc": "496"}, {"messageId": "497", "fix": "525", "desc": "499"}, {"messageId": "494", "fix": "526", "desc": "496"}, {"messageId": "497", "fix": "527", "desc": "499"}, {"messageId": "494", "fix": "528", "desc": "496"}, {"messageId": "497", "fix": "529", "desc": "499"}, {"messageId": "494", "fix": "530", "desc": "496"}, {"messageId": "497", "fix": "531", "desc": "499"}, [14, 213], "import {\n  BarChart3,\n  History,\n  Calendar,\n  Settings,\n  ChevronLeft,\n  ChevronRight,\n  Bot\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", {"fix": "532", "messageId": "489", "data": "533", "desc": "491"}, [114, 114], {"messageId": "494", "fix": "534", "desc": "496"}, {"messageId": "497", "fix": "535", "desc": "499"}, {"messageId": "494", "fix": "536", "desc": "496"}, {"messageId": "497", "fix": "537", "desc": "499"}, {"messageId": "494", "fix": "538", "desc": "496"}, {"messageId": "497", "fix": "539", "desc": "499"}, {"messageId": "494", "fix": "540", "desc": "496"}, {"messageId": "497", "fix": "541", "desc": "499"}, [45, 45], {"fix": "542", "messageId": "489", "data": "543", "desc": "491"}, {"fix": "544", "messageId": "489", "data": "545", "desc": "491"}, [52, 52], {"messageId": "494", "fix": "546", "desc": "496"}, {"messageId": "497", "fix": "547", "desc": "499"}, {"messageId": "494", "fix": "548", "desc": "496"}, {"messageId": "497", "fix": "549", "desc": "499"}, {"messageId": "494", "fix": "550", "desc": "496"}, {"messageId": "497", "fix": "551", "desc": "499"}, {"messageId": "494", "fix": "552", "desc": "496"}, {"messageId": "497", "fix": "553", "desc": "499"}, {"messageId": "494", "fix": "554", "desc": "496"}, {"messageId": "497", "fix": "555", "desc": "499"}, [93, 93], {"messageId": "494", "fix": "556", "desc": "496"}, {"messageId": "497", "fix": "557", "desc": "499"}, {"messageId": "494", "fix": "558", "desc": "496"}, {"messageId": "497", "fix": "559", "desc": "499"}, {"messageId": "494", "fix": "560", "desc": "496"}, {"messageId": "497", "fix": "561", "desc": "499"}, {"messageId": "494", "fix": "562", "desc": "496"}, {"messageId": "497", "fix": "563", "desc": "499"}, {"range": "564", "text": "377"}, "removeConsole", {"propertyName": "565"}, "Remove the console.log().", "Update the dependencies array to be: [selectedCategory, stats]", {"range": "566", "text": "567"}, "suggestUnknown", {"range": "568", "text": "569"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "570", "text": "571"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [chartData.salesData, filters.chartType, filters.showTrends, getChartTheme, resolvedTheme]", {"range": "572", "text": "573"}, {"range": "574", "text": "569"}, {"range": "575", "text": "571"}, "Update the dependencies array to be: [chartData.debtData, getChartTheme, resolvedTheme]", {"range": "576", "text": "577"}, {"range": "578", "text": "569"}, {"range": "579", "text": "571"}, "Update the dependencies array to be: [chartData.categoryData, getChartTheme, resolvedTheme]", {"range": "580", "text": "581"}, {"range": "582", "text": "569"}, {"range": "583", "text": "571"}, "Update the dependencies array to be: [getChartTheme, resolvedTheme]", {"range": "584", "text": "585"}, "Update the dependencies array to be: [chartData.performanceMetrics.efficiency, getChartTheme, resolvedTheme]", {"range": "586", "text": "587"}, "Update the dependencies array to be: [chartData]", {"range": "588", "text": "589"}, {"range": "590", "text": "569"}, {"range": "591", "text": "571"}, {"range": "592", "text": "569"}, {"range": "593", "text": "571"}, {"range": "594", "text": "569"}, {"range": "595", "text": "571"}, {"range": "596", "text": "569"}, {"range": "597", "text": "571"}, {"range": "598", "text": "569"}, {"range": "599", "text": "571"}, {"range": "600", "text": "569"}, {"range": "601", "text": "571"}, {"range": "602", "text": "569"}, {"range": "603", "text": "571"}, {"range": "604", "text": "377"}, {"propertyName": "565"}, {"range": "605", "text": "569"}, {"range": "606", "text": "571"}, {"range": "607", "text": "569"}, {"range": "608", "text": "571"}, {"range": "609", "text": "569"}, {"range": "610", "text": "571"}, {"range": "611", "text": "569"}, {"range": "612", "text": "571"}, {"range": "613", "text": "377"}, {"propertyName": "565"}, {"range": "614", "text": "377"}, {"propertyName": "565"}, {"range": "615", "text": "569"}, {"range": "616", "text": "571"}, {"range": "617", "text": "569"}, {"range": "618", "text": "571"}, {"range": "619", "text": "569"}, {"range": "620", "text": "571"}, {"range": "621", "text": "569"}, {"range": "622", "text": "571"}, {"range": "623", "text": "569"}, {"range": "624", "text": "571"}, {"range": "625", "text": "569"}, {"range": "626", "text": "571"}, {"range": "627", "text": "569"}, {"range": "628", "text": "571"}, {"range": "629", "text": "569"}, {"range": "630", "text": "571"}, {"range": "631", "text": "569"}, {"range": "632", "text": "571"}, [1430, 1472], "log", [6482, 6489], "[selected<PERSON>ategory, stats]", [7642, 7645], "unknown", [7642, 7645], "never", [11678, 11733], "[chartData.salesData, filters.chartType, filters.showTrends, getChartTheme, resolvedTheme]", [12390, 12393], [12390, 12393], [15425, 15460], "[chartData.debtData, getChartTheme, resolvedTheme]", [16120, 16123], [16120, 16123], [18522, 18561], "[chartData.categoryData, getChartTheme, resolvedTheme]", [19638, 19641], [19638, 19641], [21439, 21454], "[getChartTheme, resolvedTheme]", [23570, 23634], "[chartData.performanceMetrics.efficiency, getChartTheme, resolvedTheme]", [27272, 27306], "[chartData]", [1201, 1204], [1201, 1204], [1284, 1287], [1284, 1287], [1374, 1377], [1374, 1377], [1466, 1469], [1466, 1469], [1555, 1558], [1555, 1558], [1642, 1645], [1642, 1645], [4174, 4177], [4174, 4177], [9559, 9612], [2131, 2134], [2131, 2134], [2784, 2787], [2784, 2787], [3046, 3049], [3046, 3049], [3475, 3478], [3475, 3478], [4201, 4262], [4269, 4475], [633, 636], [633, 636], [1044, 1047], [1044, 1047], [1211, 1214], [1211, 1214], [1967, 1970], [1967, 1970], [2027, 2030], [2027, 2030], [2410, 2413], [2410, 2413], [2612, 2615], [2612, 2615], [5337, 5340], [5337, 5340], [5347, 5350], [5347, 5350]]