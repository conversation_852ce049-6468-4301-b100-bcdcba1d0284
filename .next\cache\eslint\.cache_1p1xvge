[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\ai\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\speech-to-text\\route.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AIAssistant.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AISupport.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts": "39"}, {"size": 5708, "mtime": 1752515097450, "results": "40", "hashOfConfig": "41"}, {"size": 3720, "mtime": 1752513751220, "results": "42", "hashOfConfig": "41"}, {"size": 2768, "mtime": 1752232447112, "results": "43", "hashOfConfig": "41"}, {"size": 2674, "mtime": 1752216983535, "results": "44", "hashOfConfig": "41"}, {"size": 2623, "mtime": 1752213773252, "results": "45", "hashOfConfig": "41"}, {"size": 2412, "mtime": 1752216948323, "results": "46", "hashOfConfig": "41"}, {"size": 5824, "mtime": 1752661907690, "results": "47", "hashOfConfig": "41"}, {"size": 1415, "mtime": 1751939500686, "results": "48", "hashOfConfig": "41"}, {"size": 11603, "mtime": 1752190925916, "results": "49", "hashOfConfig": "41"}, {"size": 1495, "mtime": 1752281960816, "results": "50", "hashOfConfig": "41"}, {"size": 8374, "mtime": 1752190941247, "results": "51", "hashOfConfig": "41"}, {"size": 753, "mtime": 1751948571543, "results": "52", "hashOfConfig": "41"}, {"size": 10478, "mtime": 1752501745892, "results": "53", "hashOfConfig": "41"}, {"size": 42099, "mtime": 1752665379759, "results": "54", "hashOfConfig": "41"}, {"size": 13127, "mtime": 1752561102360, "results": "55", "hashOfConfig": "41"}, {"size": 56399, "mtime": 1752656506992, "results": "56", "hashOfConfig": "41"}, {"size": 32178, "mtime": 1752493944830, "results": "57", "hashOfConfig": "41"}, {"size": 30994, "mtime": 1752510024594, "results": "58", "hashOfConfig": "41"}, {"size": 7440, "mtime": 1752324845264, "results": "59", "hashOfConfig": "41"}, {"size": 7823, "mtime": 1752061478940, "results": "60", "hashOfConfig": "41"}, {"size": 62947, "mtime": 1752493709589, "results": "61", "hashOfConfig": "41"}, {"size": 61537, "mtime": 1752412733132, "results": "62", "hashOfConfig": "41"}, {"size": 1308, "mtime": 1752515037632, "results": "63", "hashOfConfig": "41"}, {"size": 1188, "mtime": 1751950590317, "results": "64", "hashOfConfig": "41"}, {"size": 8850, "mtime": 1751938233472, "results": "65", "hashOfConfig": "41"}, {"size": 9147, "mtime": 1752061416183, "results": "66", "hashOfConfig": "41"}, {"size": 1905, "mtime": 1751950118275, "results": "67", "hashOfConfig": "41"}, {"size": 67658, "mtime": 1752510027657, "results": "68", "hashOfConfig": "41"}, {"size": 18126, "mtime": 1752514964304, "results": "69", "hashOfConfig": "41"}, {"size": 344, "mtime": 1752212829480, "results": "70", "hashOfConfig": "41"}, {"size": 3296, "mtime": 1752212374913, "results": "71", "hashOfConfig": "41"}, {"size": 2521, "mtime": 1751950064561, "results": "72", "hashOfConfig": "41"}, {"size": 10471, "mtime": 1752281679844, "results": "73", "hashOfConfig": "41"}, {"size": 5120, "mtime": 1752212844330, "results": "74", "hashOfConfig": "41"}, {"size": 1025, "mtime": 1752217088143, "results": "75", "hashOfConfig": "41"}, {"size": 4937, "mtime": 1752513719322, "results": "76", "hashOfConfig": "41"}, {"size": 1266, "mtime": 1752212686019, "results": "77", "hashOfConfig": "41"}, {"size": 2234, "mtime": 1752217075185, "results": "78", "hashOfConfig": "41"}, {"size": 5694, "mtime": 1752212408642, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10tv68i", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 7, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 1, "fixableWarningCount": 1, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\ai\\route.ts", ["197", "198", "199"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts", ["200", "201"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts", ["202"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts", ["203", "204"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts", ["205"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\speech-to-text\\route.ts", ["206", "207", "208"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts", ["209"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx", ["210"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx", ["211", "212", "213"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx", ["214"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx", ["215", "216", "217"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AIAssistant.tsx", ["218", "219", "220", "221", "222", "223", "224", "225", "226", "227"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AISupport.tsx", ["228", "229", "230", "231"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx", ["232", "233", "234", "235", "236", "237", "238", "239", "240", "241", "242", "243", "244", "245"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx", ["246", "247", "248", "249", "250", "251", "252"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx", ["253", "254", "255", "256", "257", "258", "259", "260", "261", "262"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx", ["263", "264", "265", "266", "267", "268"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx", ["269", "270", "271"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx", ["272", "273", "274", "275", "276"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx", ["277", "278"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx", ["279", "280", "281", "282", "283", "284", "285", "286", "287", "288", "289", "290", "291", "292", "293", "294", "295", "296", "297", "298", "299", "300", "301", "302", "303", "304"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx", ["305"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx", ["306"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts", ["307", "308", "309", "310", "311", "312"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts", ["313"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts", ["314", "315"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts", ["316"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts", ["317", "318", "319", "320", "321"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts", ["322", "323", "324", "325", "326"], [], {"ruleId": "327", "severity": 1, "message": "328", "line": 2, "column": 1, "nodeType": "329", "endLine": 2, "endColumn": 59, "fix": "330"}, {"ruleId": "327", "severity": 1, "message": "331", "line": 2, "column": 1, "nodeType": "329", "endLine": 2, "endColumn": 59, "fix": "332"}, {"ruleId": "333", "severity": 1, "message": "334", "line": 100, "column": 12, "nodeType": null, "messageId": "335", "endLine": 100, "endColumn": 17}, {"ruleId": "327", "severity": 1, "message": "328", "line": 1, "column": 1, "nodeType": "329", "endLine": 1, "endColumn": 56, "fix": "336"}, {"ruleId": "327", "severity": 1, "message": "337", "line": 3, "column": 1, "nodeType": "329", "endLine": 9, "endColumn": 25, "fix": "338"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 1, "column": 1, "nodeType": "329", "endLine": 1, "endColumn": 56, "fix": "339"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 1, "column": 1, "nodeType": "329", "endLine": 1, "endColumn": 42, "fix": "340"}, {"ruleId": "327", "severity": 1, "message": "337", "line": 3, "column": 1, "nodeType": "329", "endLine": 12, "endColumn": 25, "fix": "341"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 1, "column": 1, "nodeType": "329", "endLine": 1, "endColumn": 56, "fix": "342"}, {"ruleId": "333", "severity": 1, "message": "343", "line": 29, "column": 44, "nodeType": null, "messageId": "335", "endLine": 29, "endColumn": 54}, {"ruleId": "333", "severity": 1, "message": "344", "line": 84, "column": 3, "nodeType": null, "messageId": "335", "endLine": 84, "endColumn": 11}, {"ruleId": "333", "severity": 1, "message": "345", "line": 85, "column": 3, "nodeType": null, "messageId": "335", "endLine": 85, "endColumn": 9}, {"ruleId": "327", "severity": 1, "message": "328", "line": 1, "column": 1, "nodeType": "329", "endLine": 1, "endColumn": 56, "fix": "346"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 2, "column": 1, "nodeType": "329", "endLine": 2, "endColumn": 51, "fix": "347"}, {"ruleId": "327", "severity": 1, "message": "348", "line": 3, "column": 1, "nodeType": "329", "endLine": 3, "endColumn": 44, "fix": "349"}, {"ruleId": "327", "severity": 1, "message": "350", "line": 4, "column": 1, "nodeType": "329", "endLine": 4, "endColumn": 44, "fix": "351"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 7, "column": 1, "nodeType": "329", "endLine": 7, "endColumn": 29, "fix": "352"}, {"ruleId": "327", "severity": 1, "message": "353", "line": 4, "column": 1, "nodeType": "329", "endLine": 4, "endColumn": 44, "fix": "354"}, {"ruleId": "327", "severity": 1, "message": "355", "line": 3, "column": 1, "nodeType": "329", "endLine": 3, "endColumn": 44, "fix": "356"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 6, "column": 1, "nodeType": "329", "endLine": 6, "endColumn": 39, "fix": "357"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 58, "column": 5, "nodeType": "360", "messageId": "361", "endLine": 58, "endColumn": 16, "suggestions": "362"}, {"ruleId": "327", "severity": 1, "message": "363", "line": 4, "column": 1, "nodeType": "329", "endLine": 4, "endColumn": 39, "fix": "364"}, {"ruleId": "327", "severity": 1, "message": "365", "line": 5, "column": 1, "nodeType": "329", "endLine": 20, "endColumn": 22, "fix": "366"}, {"ruleId": "333", "severity": 1, "message": "367", "line": 15, "column": 3, "nodeType": null, "messageId": "335", "endLine": 15, "endColumn": 9}, {"ruleId": "327", "severity": 1, "message": "368", "line": 21, "column": 1, "nodeType": "329", "endLine": 21, "endColumn": 56, "fix": "369"}, {"ruleId": "333", "severity": 1, "message": "334", "line": 149, "column": 14, "nodeType": null, "messageId": "335", "endLine": 149, "endColumn": 19}, {"ruleId": "358", "severity": 1, "message": "359", "line": 258, "column": 15, "nodeType": "360", "messageId": "361", "endLine": 258, "endColumn": 26, "suggestions": "370"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 260, "column": 15, "nodeType": "360", "messageId": "361", "endLine": 260, "endColumn": 26, "suggestions": "371"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 262, "column": 15, "nodeType": "360", "messageId": "361", "endLine": 262, "endColumn": 26, "suggestions": "372"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 267, "column": 15, "nodeType": "360", "messageId": "361", "endLine": 267, "endColumn": 26, "suggestions": "373"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 271, "column": 13, "nodeType": "360", "messageId": "361", "endLine": 271, "endColumn": 24, "suggestions": "374"}, {"ruleId": "327", "severity": 1, "message": "363", "line": 4, "column": 1, "nodeType": "329", "endLine": 4, "endColumn": 39, "fix": "375"}, {"ruleId": "327", "severity": 1, "message": "365", "line": 5, "column": 1, "nodeType": "329", "endLine": 18, "endColumn": 22, "fix": "376"}, {"ruleId": "327", "severity": 1, "message": "368", "line": 19, "column": 1, "nodeType": "329", "endLine": 19, "endColumn": 39, "fix": "377"}, {"ruleId": "333", "severity": 1, "message": "334", "line": 130, "column": 14, "nodeType": null, "messageId": "335", "endLine": 130, "endColumn": 19}, {"ruleId": "327", "severity": 1, "message": "378", "line": 3, "column": 1, "nodeType": "329", "endLine": 3, "endColumn": 66, "fix": "379"}, {"ruleId": "327", "severity": 1, "message": "380", "line": 5, "column": 1, "nodeType": "329", "endLine": 5, "endColumn": 39, "fix": "381"}, {"ruleId": "333", "severity": 1, "message": "382", "line": 103, "column": 51, "nodeType": null, "messageId": "335", "endLine": 103, "endColumn": 56}, {"ruleId": "383", "severity": 1, "message": "384", "line": 203, "column": 6, "nodeType": "385", "endLine": 203, "endColumn": 13, "suggestions": "386"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 241, "column": 27, "nodeType": "389", "messageId": "390", "endLine": 241, "endColumn": 30, "suggestions": "391"}, {"ruleId": "383", "severity": 1, "message": "392", "line": 391, "column": 7, "nodeType": "385", "endLine": 391, "endColumn": 62, "suggestions": "393"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 413, "column": 27, "nodeType": "389", "messageId": "390", "endLine": 413, "endColumn": 30, "suggestions": "394"}, {"ruleId": "383", "severity": 1, "message": "395", "line": 529, "column": 7, "nodeType": "385", "endLine": 529, "endColumn": 42, "suggestions": "396"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 551, "column": 27, "nodeType": "389", "messageId": "390", "endLine": 551, "endColumn": 30, "suggestions": "397"}, {"ruleId": "383", "severity": 1, "message": "395", "line": 631, "column": 7, "nodeType": "385", "endLine": 631, "endColumn": 46, "suggestions": "398"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 665, "column": 29, "nodeType": "389", "messageId": "390", "endLine": 665, "endColumn": 32, "suggestions": "399"}, {"ruleId": "383", "severity": 1, "message": "395", "line": 732, "column": 6, "nodeType": "385", "endLine": 732, "endColumn": 21, "suggestions": "400"}, {"ruleId": "383", "severity": 1, "message": "401", "line": 823, "column": 7, "nodeType": "385", "endLine": 823, "endColumn": 71, "suggestions": "402"}, {"ruleId": "383", "severity": 1, "message": "403", "line": 900, "column": 6, "nodeType": "385", "endLine": 900, "endColumn": 40, "suggestions": "404"}, {"ruleId": "327", "severity": 1, "message": "365", "line": 4, "column": 1, "nodeType": "329", "endLine": 4, "endColumn": 75, "fix": "405"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 5, "column": 1, "nodeType": "329", "endLine": 5, "endColumn": 39, "fix": "406"}, {"ruleId": "327", "severity": 1, "message": "363", "line": 5, "column": 1, "nodeType": "329", "endLine": 5, "endColumn": 39, "fix": "407"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 6, "column": 1, "nodeType": "329", "endLine": 6, "endColumn": 36, "fix": "408"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 7, "column": 1, "nodeType": "329", "endLine": 7, "endColumn": 46, "fix": "409"}, {"ruleId": "327", "severity": 1, "message": "410", "line": 7, "column": 1, "nodeType": "329", "endLine": 7, "endColumn": 46, "fix": "411"}, {"ruleId": "327", "severity": 1, "message": "412", "line": 8, "column": 1, "nodeType": "329", "endLine": 8, "endColumn": 34, "fix": "413"}, {"ruleId": "327", "severity": 1, "message": "365", "line": 4, "column": 1, "nodeType": "329", "endLine": 10, "endColumn": 22, "fix": "414"}, {"ruleId": "333", "severity": 1, "message": "415", "line": 7, "column": 16, "nodeType": null, "messageId": "335", "endLine": 7, "endColumn": 24}, {"ruleId": "333", "severity": 1, "message": "416", "line": 7, "column": 26, "nodeType": null, "messageId": "335", "endLine": 7, "endColumn": 37}, {"ruleId": "333", "severity": 1, "message": "417", "line": 7, "column": 39, "nodeType": null, "messageId": "335", "endLine": 7, "endColumn": 48}, {"ruleId": "333", "severity": 1, "message": "418", "line": 8, "column": 27, "nodeType": null, "messageId": "335", "endLine": 8, "endColumn": 37}, {"ruleId": "333", "severity": 1, "message": "419", "line": 8, "column": 39, "nodeType": null, "messageId": "335", "endLine": 8, "endColumn": 48}, {"ruleId": "333", "severity": 1, "message": "420", "line": 8, "column": 60, "nodeType": null, "messageId": "335", "endLine": 8, "endColumn": 63}, {"ruleId": "333", "severity": 1, "message": "421", "line": 9, "column": 33, "nodeType": null, "messageId": "335", "endLine": 9, "endColumn": 37}, {"ruleId": "333", "severity": 1, "message": "422", "line": 9, "column": 39, "nodeType": null, "messageId": "335", "endLine": 9, "endColumn": 46}, {"ruleId": "423", "severity": 2, "message": "424", "line": 161, "column": 9, "nodeType": "425", "messageId": "426", "endLine": 161, "endColumn": 17, "fix": "427"}, {"ruleId": "333", "severity": 1, "message": "428", "line": 9, "column": 3, "nodeType": null, "messageId": "335", "endLine": 9, "endColumn": 9}, {"ruleId": "333", "severity": 1, "message": "429", "line": 9, "column": 11, "nodeType": null, "messageId": "335", "endLine": 9, "endColumn": 19}, {"ruleId": "333", "severity": 1, "message": "430", "line": 9, "column": 21, "nodeType": null, "messageId": "335", "endLine": 9, "endColumn": 26}, {"ruleId": "333", "severity": 1, "message": "431", "line": 9, "column": 28, "nodeType": null, "messageId": "335", "endLine": 9, "endColumn": 38}, {"ruleId": "333", "severity": 1, "message": "432", "line": 9, "column": 40, "nodeType": null, "messageId": "335", "endLine": 9, "endColumn": 47}, {"ruleId": "333", "severity": 1, "message": "433", "line": 10, "column": 18, "nodeType": null, "messageId": "335", "endLine": 10, "endColumn": 24}, {"ruleId": "327", "severity": 1, "message": "328", "line": 4, "column": 1, "nodeType": "329", "endLine": 4, "endColumn": 50, "fix": "434"}, {"ruleId": "327", "severity": 1, "message": "365", "line": 4, "column": 1, "nodeType": "329", "endLine": 4, "endColumn": 50, "fix": "435"}, {"ruleId": "436", "severity": 1, "message": "437", "line": 151, "column": 19, "nodeType": "438", "endLine": 155, "endColumn": 21}, {"ruleId": "327", "severity": 1, "message": "355", "line": 3, "column": 1, "nodeType": "329", "endLine": 3, "endColumn": 44, "fix": "439"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 5, "column": 1, "nodeType": "329", "endLine": 5, "endColumn": 39, "fix": "440"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 6, "column": 1, "nodeType": "329", "endLine": 6, "endColumn": 42, "fix": "441"}, {"ruleId": "327", "severity": 1, "message": "442", "line": 6, "column": 1, "nodeType": "329", "endLine": 6, "endColumn": 42, "fix": "443"}, {"ruleId": "436", "severity": 1, "message": "437", "line": 149, "column": 17, "nodeType": "438", "endLine": 153, "endColumn": 19}, {"ruleId": "327", "severity": 1, "message": "328", "line": 4, "column": 1, "nodeType": "329", "endLine": 4, "endColumn": 44, "fix": "444"}, {"ruleId": "327", "severity": 1, "message": "353", "line": 4, "column": 1, "nodeType": "329", "endLine": 4, "endColumn": 44, "fix": "445"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 4, "column": 1, "nodeType": "329", "endLine": 11, "endColumn": 22, "fix": "446"}, {"ruleId": "327", "severity": 1, "message": "365", "line": 4, "column": 1, "nodeType": "329", "endLine": 11, "endColumn": 22, "fix": "447"}, {"ruleId": "333", "severity": 1, "message": "429", "line": 6, "column": 18, "nodeType": null, "messageId": "335", "endLine": 6, "endColumn": 26}, {"ruleId": "333", "severity": 1, "message": "448", "line": 7, "column": 16, "nodeType": null, "messageId": "335", "endLine": 7, "endColumn": 24}, {"ruleId": "333", "severity": 1, "message": "449", "line": 7, "column": 26, "nodeType": null, "messageId": "335", "endLine": 7, "endColumn": 36}, {"ruleId": "333", "severity": 1, "message": "450", "line": 7, "column": 38, "nodeType": null, "messageId": "335", "endLine": 7, "endColumn": 46}, {"ruleId": "333", "severity": 1, "message": "451", "line": 7, "column": 60, "nodeType": null, "messageId": "335", "endLine": 7, "endColumn": 72}, {"ruleId": "333", "severity": 1, "message": "452", "line": 8, "column": 31, "nodeType": null, "messageId": "335", "endLine": 8, "endColumn": 32}, {"ruleId": "333", "severity": 1, "message": "453", "line": 8, "column": 49, "nodeType": null, "messageId": "335", "endLine": 8, "endColumn": 53}, {"ruleId": "333", "severity": 1, "message": "454", "line": 8, "column": 55, "nodeType": null, "messageId": "335", "endLine": 8, "endColumn": 59}, {"ruleId": "333", "severity": 1, "message": "455", "line": 8, "column": 61, "nodeType": null, "messageId": "335", "endLine": 8, "endColumn": 66}, {"ruleId": "333", "severity": 1, "message": "456", "line": 10, "column": 41, "nodeType": null, "messageId": "335", "endLine": 10, "endColumn": 48}, {"ruleId": "333", "severity": 1, "message": "457", "line": 10, "column": 60, "nodeType": null, "messageId": "335", "endLine": 10, "endColumn": 70}, {"ruleId": "387", "severity": 1, "message": "388", "line": 30, "column": 41, "nodeType": "389", "messageId": "390", "endLine": 30, "endColumn": 44, "suggestions": "458"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 31, "column": 43, "nodeType": "389", "messageId": "390", "endLine": 31, "endColumn": 46, "suggestions": "459"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 32, "column": 48, "nodeType": "389", "messageId": "390", "endLine": 32, "endColumn": 51, "suggestions": "460"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 33, "column": 44, "nodeType": "389", "messageId": "390", "endLine": 33, "endColumn": 47, "suggestions": "461"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 34, "column": 46, "nodeType": "389", "messageId": "390", "endLine": 34, "endColumn": 49, "suggestions": "462"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 35, "column": 42, "nodeType": "389", "messageId": "390", "endLine": 35, "endColumn": 45, "suggestions": "463"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 48, "column": 5, "nodeType": "360", "messageId": "361", "endLine": 48, "endColumn": 16, "suggestions": "464"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 53, "column": 5, "nodeType": "360", "messageId": "361", "endLine": 53, "endColumn": 16, "suggestions": "465"}, {"ruleId": "333", "severity": 1, "message": "466", "line": 75, "column": 9, "nodeType": null, "messageId": "335", "endLine": 75, "endColumn": 20}, {"ruleId": "333", "severity": 1, "message": "467", "line": 88, "column": 9, "nodeType": null, "messageId": "335", "endLine": 88, "endColumn": 23}, {"ruleId": "436", "severity": 1, "message": "437", "line": 108, "column": 17, "nodeType": "438", "endLine": 112, "endColumn": 19}, {"ruleId": "387", "severity": 1, "message": "388", "line": 123, "column": 65, "nodeType": "389", "messageId": "390", "endLine": 123, "endColumn": 68, "suggestions": "468"}, {"ruleId": "436", "severity": 1, "message": "437", "line": 1378, "column": 19, "nodeType": "438", "endLine": 1382, "endColumn": 21}, {"ruleId": "327", "severity": 1, "message": "355", "line": 3, "column": 1, "nodeType": "329", "endLine": 3, "endColumn": 44, "fix": "469"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 410, "column": 7, "nodeType": "360", "messageId": "361", "endLine": 410, "endColumn": 18, "suggestions": "470"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 3, "column": 1, "nodeType": "329", "endLine": 3, "endColumn": 43, "fix": "471"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 99, "column": 44, "nodeType": "389", "messageId": "390", "endLine": 99, "endColumn": 47, "suggestions": "472"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 124, "column": 21, "nodeType": "389", "messageId": "390", "endLine": 124, "endColumn": 24, "suggestions": "473"}, {"ruleId": "333", "severity": 1, "message": "334", "line": 129, "column": 12, "nodeType": null, "messageId": "335", "endLine": 129, "endColumn": 17}, {"ruleId": "387", "severity": 1, "message": "388", "line": 134, "column": 65, "nodeType": "389", "messageId": "390", "endLine": 134, "endColumn": 68, "suggestions": "474"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 151, "column": 44, "nodeType": "389", "messageId": "390", "endLine": 151, "endColumn": 47, "suggestions": "475"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 1, "column": 1, "nodeType": "329", "endLine": 1, "endColumn": 46, "fix": "476"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 154, "column": 7, "nodeType": "360", "messageId": "361", "endLine": 154, "endColumn": 18, "suggestions": "477"}, {"ruleId": "358", "severity": 1, "message": "359", "line": 155, "column": 7, "nodeType": "360", "messageId": "361", "endLine": 155, "endColumn": 18, "suggestions": "478"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 1, "column": 1, "nodeType": "329", "endLine": 1, "endColumn": 53, "fix": "479"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 29, "column": 32, "nodeType": "389", "messageId": "390", "endLine": 29, "endColumn": 35, "suggestions": "480"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 49, "column": 29, "nodeType": "389", "messageId": "390", "endLine": 49, "endColumn": 32, "suggestions": "481"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 58, "column": 34, "nodeType": "389", "messageId": "390", "endLine": 58, "endColumn": 37, "suggestions": "482"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 96, "column": 30, "nodeType": "389", "messageId": "390", "endLine": 96, "endColumn": 33, "suggestions": "483"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 97, "column": 35, "nodeType": "389", "messageId": "390", "endLine": 97, "endColumn": 38, "suggestions": "484"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 3, "column": 1, "nodeType": "329", "endLine": 3, "endColumn": 65, "fix": "485"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 82, "column": 47, "nodeType": "389", "messageId": "390", "endLine": 82, "endColumn": 50, "suggestions": "486"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 91, "column": 47, "nodeType": "389", "messageId": "390", "endLine": 91, "endColumn": 50, "suggestions": "487"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 191, "column": 46, "nodeType": "389", "messageId": "390", "endLine": 191, "endColumn": 49, "suggestions": "488"}, {"ruleId": "387", "severity": 1, "message": "388", "line": 191, "column": 56, "nodeType": "389", "messageId": "390", "endLine": 191, "endColumn": 59, "suggestions": "489"}, "import/order", "There should be at least one empty line between import groups", "ImportDeclaration", {"range": "490", "text": "491"}, "`@google/generative-ai` import should occur before import of `next/server`", {"range": "492", "text": "493"}, "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", {"range": "494", "text": "491"}, "`@/lib/api-utils` import should occur before import of `@/lib/supabase`", {"range": "495", "text": "496"}, {"range": "497", "text": "491"}, {"range": "498", "text": "491"}, {"range": "499", "text": "500"}, {"range": "501", "text": "491"}, "'sampleRate' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'language' is defined but never used. Allowed unused args must match /^_/u.", "'apiKey' is defined but never used. Allowed unused args must match /^_/u.", {"range": "502", "text": "491"}, {"range": "503", "text": "491"}, "`react` import should occur after import of `next/link`", {"range": "504", "text": "505"}, "`next/navigation` import should occur after import of `next/link`", {"range": "506", "text": "507"}, {"range": "508", "text": "491"}, "`next/navigation` import should occur before import of `react`", {"range": "509", "text": "510"}, "`react` import should occur after import of `next-themes`", {"range": "511", "text": "512"}, {"range": "513", "text": "491"}, "no-console", "Unexpected console statement. Only these console methods are allowed: warn, error.", "MemberExpression", "limited", ["514"], "`next-themes` import should occur before import of `react`", {"range": "515", "text": "516"}, "`lucide-react` import should occur before import of `react`", {"range": "517", "text": "518"}, "'MicOff' is defined but never used. Allowed unused vars must match /^_/u.", "`framer-motion` import should occur before import of `react`", {"range": "519", "text": "520"}, ["521"], ["522"], ["523"], ["524"], ["525"], {"range": "526", "text": "516"}, {"range": "527", "text": "528"}, {"range": "529", "text": "530"}, "`react` import should occur after import of `lucide-react`", {"range": "531", "text": "532"}, "`next-themes` import should occur after import of `lucide-react`", {"range": "533", "text": "534"}, "'index' is defined but never used. Allowed unused args must match /^_/u.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'selectedCategory'. Either include it or remove the dependency array.", "ArrayExpression", ["535"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["536", "537"], "React Hook useMemo has missing dependencies: 'filters.showTrends' and 'getChartTheme'. Either include them or remove the dependency array.", ["538"], ["539", "540"], "React Hook useMemo has a missing dependency: 'getChartTheme'. Either include it or remove the dependency array.", ["541"], ["542", "543"], ["544"], ["545", "546"], ["547"], "React Hook useMemo has missing dependencies: 'chartData.performanceMetrics.efficiency' and 'getChartTheme'. Either include them or remove the dependency array. Mutable values like 'chartData.performanceMetrics.efficiency.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["548"], "React Hook useMemo has an unnecessary dependency: 'stats.totalDebtAmount'. Either exclude it or remove the dependency array.", ["549"], {"range": "550", "text": "551"}, {"range": "552", "text": "491"}, {"range": "553", "text": "554"}, {"range": "555", "text": "491"}, {"range": "556", "text": "491"}, "`@/lib/supabase` import should occur before import of `./DebtModal`", {"range": "557", "text": "558"}, "`date-fns` import should occur before import of `react`", {"range": "559", "text": "560"}, {"range": "561", "text": "562"}, "'SkipBack' is defined but never used. Allowed unused vars must match /^_/u.", "'SkipForward' is defined but never used. Allowed unused vars must match /^_/u.", "'Maximize2' is defined but never used. Allowed unused vars must match /^_/u.", "'TrendingUp' is defined but never used. Allowed unused vars must match /^_/u.", "'BarChart3' is defined but never used. Allowed unused vars must match /^_/u.", "'Zap' is defined but never used. Allowed unused vars must match /^_/u.", "'Copy' is defined but never used. Allowed unused vars must match /^_/u.", "'Archive' is defined but never used. Allowed unused vars must match /^_/u.", "prefer-const", "'filtered' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "563", "text": "564"}, "'Filter' is defined but never used. Allowed unused vars must match /^_/u.", "'Calendar' is defined but never used. Allowed unused vars must match /^_/u.", "'Globe' is defined but never used. Allowed unused vars must match /^_/u.", "'Smartphone' is defined but never used. Allowed unused vars must match /^_/u.", "'Monitor' is defined but never used. Allowed unused vars must match /^_/u.", "'MapPin' is defined but never used. Allowed unused vars must match /^_/u.", {"range": "565", "text": "491"}, {"range": "566", "text": "567"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", {"range": "568", "text": "569"}, {"range": "570", "text": "491"}, {"range": "571", "text": "491"}, "`./ProductModal` import should occur after import of `@/lib/supabase`", {"range": "572", "text": "573"}, {"range": "574", "text": "491"}, {"range": "575", "text": "510"}, {"range": "576", "text": "491"}, {"range": "577", "text": "578"}, "'Building' is defined but never used. Allowed unused vars must match /^_/u.", "'CreditCard' is defined but never used. Allowed unused vars must match /^_/u.", "'FileText' is defined but never used. Allowed unused vars must match /^_/u.", "'SettingsIcon' is defined but never used. Allowed unused vars must match /^_/u.", "'X' is defined but never used. Allowed unused vars must match /^_/u.", "'Info' is defined but never used. Allowed unused vars must match /^_/u.", "'Star' is defined but never used. Allowed unused vars must match /^_/u.", "'Heart' is defined but never used. Allowed unused vars must match /^_/u.", "'History' is defined but never used. Allowed unused vars must match /^_/u.", "'ShieldIcon' is defined but never used. Allowed unused vars must match /^_/u.", ["579", "580"], ["581", "582"], ["583", "584"], ["585", "586"], ["587", "588"], ["589", "590"], ["591"], ["592"], "'addLocation' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'removeLocation' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["593", "594"], {"range": "595", "text": "596"}, ["597"], {"range": "598", "text": "491"}, ["599", "600"], ["601", "602"], ["603", "604"], ["605", "606"], {"range": "607", "text": "491"}, ["608"], ["609"], {"range": "610", "text": "491"}, ["611", "612"], ["613", "614"], ["615", "616"], ["617", "618"], ["619", "620"], {"range": "621", "text": "491"}, ["622", "623"], ["624", "625"], ["626", "627"], ["628", "629"], [114, 114], "\n", [0, 115], "import { GoogleGenerativeAI } from '@google/generative-ai'\nimport { NextRequest, NextResponse } from 'next/server'\n", [55, 55], [56, 248], "import {\n  successResponse,\n  withError<PERSON>andler,\n  handleDatabaseError,\n  parsePaginationParams,\n  handleCorsPreflightRequest\n} from '@/lib/api-utils'\nimport { supabase } from '@/lib/supabase'\n", [55, 55], [41, 41], [42, 300], "import {\n  successResponse,\n  errorResponse,\n  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  validateRequestBody,\n  validateRequiredFields,\n  handleDatabaseError,\n  parsePaginationParams,\n  handleCorsPreflightRequest\n} from '@/lib/api-utils'\nimport { supabase } from '@/lib/supabase'\n", [55, 55], [55, 55], [88, 88], [14, 244], "import { useRouter } from 'next/navigation'\nimport { motion } from 'framer-motion'\nimport { Eye, EyeOff, Lock, Mail, ArrowRight, Store } from 'lucide-react'\nimport Link from 'next/link'\nimport { useState, useEffect } from 'react'\n", [58, 244], "import { motion } from 'framer-motion'\nimport { Eye, EyeOff, Lock, Mail, ArrowRight, Store } from 'lucide-react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\n", [243, 243], [14, 92], "import { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n", [14, 218], "import { Search, Home, Package, Users, Image, Moon, Sun, LogOut, User } from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [217, 217], {"fix": "630", "messageId": "631", "data": "632", "desc": "633"}, [14, 105], "import { useTheme } from 'next-themes'\nimport { useState, useRef, useEffect } from 'react'\n", [14, 269], "import {\n  Send,\n  <PERSON>t,\n  User,\n  Loader2,\n  <PERSON><PERSON>les,\n  X,\n  Minimize2,\n  Maximize2,\n  Mic,\n  Mic<PERSON>ff,\n  Square,\n  RotateCcw,\n  Copy,\n  Check\n} from 'lucide-react'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\n", [14, 325], "import { motion, AnimatePresence } from 'framer-motion'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\nimport {\n  Send,\n  Bot,\n  User,\n  Loader2,\n  Sparkles,\n  X,\n  Minimize2,\n  Maximize2,\n  Mic,\n  MicOff,\n  Square,\n  RotateCcw,\n  Copy,\n  Check\n} from 'lucide-react'\n", {"fix": "634", "messageId": "631", "data": "635", "desc": "633"}, {"fix": "636", "messageId": "631", "data": "637", "desc": "633"}, {"fix": "638", "messageId": "631", "data": "639", "desc": "633"}, {"fix": "640", "messageId": "631", "data": "641", "desc": "633"}, {"fix": "642", "messageId": "631", "data": "643", "desc": "633"}, [14, 105], [14, 264], "import {\n  Bot,\n  User,\n  Send,\n  Loader2,\n  TrendingUp,\n  Package,\n  CreditCard,\n  BarChart3,\n  Lightbulb,\n  HelpCircle,\n  Zap,\n  Brain\n} from 'lucide-react'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\n", [14, 303], "import { motion } from 'framer-motion'\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\nimport {\n  Bot,\n  User,\n  Send,\n  Loader2,\n  TrendingUp,\n  Package,\n  CreditCard,\n  BarChart3,\n  Lightbulb,\n  HelpCircle,\n  Zap,\n  Brain\n} from 'lucide-react'\n", [14, 398], "import ReactECharts from 'echarts-for-react'\nimport { useTheme } from 'next-themes'\nimport {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\nimport { useEffect, useState, useMemo, useCallback } from 'react'\n", [125, 398], "import {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\n", {"desc": "644", "fix": "645"}, {"messageId": "646", "fix": "647", "desc": "648"}, {"messageId": "649", "fix": "650", "desc": "651"}, {"desc": "652", "fix": "653"}, {"messageId": "646", "fix": "654", "desc": "648"}, {"messageId": "649", "fix": "655", "desc": "651"}, {"desc": "656", "fix": "657"}, {"messageId": "646", "fix": "658", "desc": "648"}, {"messageId": "649", "fix": "659", "desc": "651"}, {"desc": "660", "fix": "661"}, {"messageId": "646", "fix": "662", "desc": "648"}, {"messageId": "649", "fix": "663", "desc": "651"}, {"desc": "664", "fix": "665"}, {"desc": "666", "fix": "667"}, {"desc": "668", "fix": "669"}, [14, 133], "import { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n", [171, 171], [14, 172], "import { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\n", [207, 207], [253, 253], [172, 254], "import { CustomerDebt } from '@/lib/supabase'\nimport DebtModal from './DebtModal'\n", [14, 288], "import { format } from 'date-fns'\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport DebtModal from './DebtModal'\nimport { CustomerDebt } from '@/lib/supabase'\n", [14, 449], "import {\n  Upload, Heart, Share2, Download, Trash2, Plus, Image as ImageIcon,\n  Search, Filter, Grid, List, Calendar, Eye, Star, Users, Camera,\n  Play, Pause, SkipBack, SkipForward, Maximize2, X, ChevronLeft, ChevronRight,\n  FolderOpen, Tag, Clock, TrendingUp, BarChart3, Activity, Zap,\n  Settings, MoreVertical, Edit, Copy, Archive, RefreshCw, SortAsc, SortDesc\n} from 'lucide-react'\nimport { useState, useMemo, useRef } from 'react'\n", [4909, 5383], "const filtered = photos.filter(photo => {\n      const matchesSearch = searchTerm === '' ||\n        photo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        photo.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        photo.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))\n\n      const matchesCategory = selectedCategory === 'all' || photo.category === selectedCategory\n\n      return matchesSearch && matchesCategory\n    })", [107, 107], [14, 108], "import { X, Upload, Package } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n", [14, 164], "import { Plus, Edit, Trash2, Search, Package } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [163, 163], [205, 205], [164, 267], "import { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\nimport ProductModal from './ProductModal'\n", [91, 91], [14, 92], [505, 505], [14, 506], "import { \n  Save, User, Store, Bell, Shield, Palette, Database, Download, Upload,\n  MapPin, Clock, Calendar, Globe, Camera, Eye, EyeOff, Key, Smartphone,\n  Mail, Phone, Building, CreditCard, FileText, Settings as SettingsIcon,\n  Trash2, Plus, Edit3, Check, X, AlertTriangle, Info, Star, Heart,\n  Monitor, Sun, Moon, Palette as PaletteIcon, Type, Layout, Zap,\n  Cloud, HardDrive, RefreshCw, Archive, History, Shield as ShieldIcon\n} from 'lucide-react'\nimport { useState, useRef } from 'react'\n", {"messageId": "646", "fix": "670", "desc": "648"}, {"messageId": "649", "fix": "671", "desc": "651"}, {"messageId": "646", "fix": "672", "desc": "648"}, {"messageId": "649", "fix": "673", "desc": "651"}, {"messageId": "646", "fix": "674", "desc": "648"}, {"messageId": "649", "fix": "675", "desc": "651"}, {"messageId": "646", "fix": "676", "desc": "648"}, {"messageId": "649", "fix": "677", "desc": "651"}, {"messageId": "646", "fix": "678", "desc": "648"}, {"messageId": "649", "fix": "679", "desc": "651"}, {"messageId": "646", "fix": "680", "desc": "648"}, {"messageId": "649", "fix": "681", "desc": "651"}, {"fix": "682", "messageId": "631", "data": "683", "desc": "633"}, {"fix": "684", "messageId": "631", "data": "685", "desc": "633"}, {"messageId": "646", "fix": "686", "desc": "648"}, {"messageId": "649", "fix": "687", "desc": "651"}, [14, 213], "import {\n  BarChart3,\n  History,\n  Calendar,\n  Settings,\n  ChevronLeft,\n  ChevronRight,\n  Bot\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", {"fix": "688", "messageId": "631", "data": "689", "desc": "633"}, [114, 114], {"messageId": "646", "fix": "690", "desc": "648"}, {"messageId": "649", "fix": "691", "desc": "651"}, {"messageId": "646", "fix": "692", "desc": "648"}, {"messageId": "649", "fix": "693", "desc": "651"}, {"messageId": "646", "fix": "694", "desc": "648"}, {"messageId": "649", "fix": "695", "desc": "651"}, {"messageId": "646", "fix": "696", "desc": "648"}, {"messageId": "649", "fix": "697", "desc": "651"}, [45, 45], {"fix": "698", "messageId": "631", "data": "699", "desc": "633"}, {"fix": "700", "messageId": "631", "data": "701", "desc": "633"}, [52, 52], {"messageId": "646", "fix": "702", "desc": "648"}, {"messageId": "649", "fix": "703", "desc": "651"}, {"messageId": "646", "fix": "704", "desc": "648"}, {"messageId": "649", "fix": "705", "desc": "651"}, {"messageId": "646", "fix": "706", "desc": "648"}, {"messageId": "649", "fix": "707", "desc": "651"}, {"messageId": "646", "fix": "708", "desc": "648"}, {"messageId": "649", "fix": "709", "desc": "651"}, {"messageId": "646", "fix": "710", "desc": "648"}, {"messageId": "649", "fix": "711", "desc": "651"}, [93, 93], {"messageId": "646", "fix": "712", "desc": "648"}, {"messageId": "649", "fix": "713", "desc": "651"}, {"messageId": "646", "fix": "714", "desc": "648"}, {"messageId": "649", "fix": "715", "desc": "651"}, {"messageId": "646", "fix": "716", "desc": "648"}, {"messageId": "649", "fix": "717", "desc": "651"}, {"messageId": "646", "fix": "718", "desc": "648"}, {"messageId": "649", "fix": "719", "desc": "651"}, {"range": "720", "text": "721"}, "removeConsole", {"propertyName": "722"}, "Remove the console.log().", {"range": "723", "text": "721"}, {"propertyName": "722"}, {"range": "724", "text": "721"}, {"propertyName": "722"}, {"range": "725", "text": "721"}, {"propertyName": "722"}, {"range": "726", "text": "721"}, {"propertyName": "722"}, {"range": "727", "text": "721"}, {"propertyName": "722"}, "Update the dependencies array to be: [selectedCategory, stats]", {"range": "728", "text": "729"}, "suggestUnknown", {"range": "730", "text": "731"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "732", "text": "733"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [chartData.salesData, filters.chartType, filters.showTrends, getChartTheme, resolvedTheme]", {"range": "734", "text": "735"}, {"range": "736", "text": "731"}, {"range": "737", "text": "733"}, "Update the dependencies array to be: [chartData.debtData, getChartTheme, resolvedTheme]", {"range": "738", "text": "739"}, {"range": "740", "text": "731"}, {"range": "741", "text": "733"}, "Update the dependencies array to be: [chartData.categoryData, getChartTheme, resolvedTheme]", {"range": "742", "text": "743"}, {"range": "744", "text": "731"}, {"range": "745", "text": "733"}, "Update the dependencies array to be: [getChartTheme, resolvedTheme]", {"range": "746", "text": "747"}, "Update the dependencies array to be: [chartData.performanceMetrics.efficiency, getChartTheme, resolvedTheme]", {"range": "748", "text": "749"}, "Update the dependencies array to be: [chartData]", {"range": "750", "text": "751"}, {"range": "752", "text": "731"}, {"range": "753", "text": "733"}, {"range": "754", "text": "731"}, {"range": "755", "text": "733"}, {"range": "756", "text": "731"}, {"range": "757", "text": "733"}, {"range": "758", "text": "731"}, {"range": "759", "text": "733"}, {"range": "760", "text": "731"}, {"range": "761", "text": "733"}, {"range": "762", "text": "731"}, {"range": "763", "text": "733"}, {"range": "764", "text": "721"}, {"propertyName": "722"}, {"range": "765", "text": "721"}, {"propertyName": "722"}, {"range": "766", "text": "731"}, {"range": "767", "text": "733"}, {"range": "768", "text": "721"}, {"propertyName": "722"}, {"range": "769", "text": "731"}, {"range": "770", "text": "733"}, {"range": "771", "text": "731"}, {"range": "772", "text": "733"}, {"range": "773", "text": "731"}, {"range": "774", "text": "733"}, {"range": "775", "text": "731"}, {"range": "776", "text": "733"}, {"range": "777", "text": "721"}, {"propertyName": "722"}, {"range": "778", "text": "721"}, {"propertyName": "722"}, {"range": "779", "text": "731"}, {"range": "780", "text": "733"}, {"range": "781", "text": "731"}, {"range": "782", "text": "733"}, {"range": "783", "text": "731"}, {"range": "784", "text": "733"}, {"range": "785", "text": "731"}, {"range": "786", "text": "733"}, {"range": "787", "text": "731"}, {"range": "788", "text": "733"}, {"range": "789", "text": "731"}, {"range": "790", "text": "733"}, {"range": "791", "text": "731"}, {"range": "792", "text": "733"}, {"range": "793", "text": "731"}, {"range": "794", "text": "733"}, {"range": "795", "text": "731"}, {"range": "796", "text": "733"}, [1430, 1472], "", "log", [6935, 7026], [7112, 7197], [7283, 7373], [7568, 7644], [7718, 7799], [6489, 6496], "[selected<PERSON>ategory, stats]", [7649, 7652], "unknown", [7649, 7652], "never", [11685, 11740], "[chartData.salesData, filters.chartType, filters.showTrends, getChartTheme, resolvedTheme]", [12397, 12400], [12397, 12400], [15432, 15467], "[chartData.debtData, getChartTheme, resolvedTheme]", [16127, 16130], [16127, 16130], [18529, 18568], "[chartData.categoryData, getChartTheme, resolvedTheme]", [19645, 19648], [19645, 19648], [21446, 21461], "[getChartTheme, resolvedTheme]", [23577, 23641], "[chartData.performanceMetrics.efficiency, getChartTheme, resolvedTheme]", [27279, 27313], "[chartData]", [1325, 1328], [1325, 1328], [1408, 1411], [1408, 1411], [1498, 1501], [1498, 1501], [1590, 1593], [1590, 1593], [1679, 1682], [1679, 1682], [1766, 1769], [1766, 1769], [2106, 2138], [2255, 2287], [4713, 4716], [4713, 4716], [9559, 9612], [2131, 2134], [2131, 2134], [2784, 2787], [2784, 2787], [3046, 3049], [3046, 3049], [3475, 3478], [3475, 3478], [4201, 4262], [4269, 4475], [633, 636], [633, 636], [1044, 1047], [1044, 1047], [1211, 1214], [1211, 1214], [1967, 1970], [1967, 1970], [2027, 2030], [2027, 2030], [2410, 2413], [2410, 2413], [2612, 2615], [2612, 2615], [5337, 5340], [5337, 5340], [5347, 5350], [5347, 5350]]