'use client'

import { useState, useRef, useEffect } from 'react'
import { useTheme } from 'next-themes'
import {
  Send,
  Bot,
  User,
  Loader2,
  Sparkles,
  X,
  Minimize2,
  Maximize2,
  Mic,
  MicOff,
  Square,
  RotateCcw,
  Copy,
  Check
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  isTyping?: boolean
  reactions?: string[]
}

interface AIAssistantProps {
  context?: string
}

export default function AIAssistant({ context = 'dashboard' }: AIAssistantProps) {
  const { resolvedTheme } = useTheme()
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Hello! I\'m your AI assistant for Revantad Store. I can help you with business analytics, inventory management, customer debt tracking, and store operations. How can I assist you today?',
      timestamp: new Date()
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null)
  const [recordingTime, setRecordingTime] = useState(0)
  const [isProcessingAudio, setIsProcessingAudio] = useState(false)

  // Recording timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingTime(prev => prev + 1)
      }, 1000)
    } else {
      setRecordingTime(0)
    }
    return () => clearInterval(interval)
  }, [isRecording])

  // Cleanup audio stream on unmount
  useEffect(() => {
    return () => {
      if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop())
      }
    }
  }, [audioStream])
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen, isMinimized])

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    // Add typing indicator
    const typingMessage: Message = {
      id: 'typing',
      type: 'ai',
      content: '',
      timestamp: new Date(),
      isTyping: true
    }
    setMessages(prev => [...prev, typingMessage])

    try {
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          context: context
        })
      })

      const data = await response.json()

      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== 'typing'))

      if (data.success) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: data.response,
          timestamp: new Date()
        }
        setMessages(prev => [...prev, aiMessage])

        // AI response received successfully
      } else {
        throw new Error(data.error || 'Failed to get AI response')
      }
    } catch (error) {
      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== 'typing'))

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: 'Sorry, I encountered an error. Please try again later.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  // Start voice recording
  const startRecording = async () => {
    try {
      setIsProcessingAudio(true)

      // Request microphone permission
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      })

      setAudioStream(stream)

      // Create MediaRecorder
      const recorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      })

      const audioChunks: Blob[] = []

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data)
        }
      }

      recorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm;codecs=opus' })
        await processAudioToText(audioBlob)

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop())
        setAudioStream(null)
        setIsProcessingAudio(false)
      }

      setMediaRecorder(recorder)
      recorder.start()
      setIsRecording(true)
      setIsProcessingAudio(false)

    } catch (error) {
      console.error('Error starting recording:', error)
      setIsProcessingAudio(false)
      alert('Unable to access microphone. Please check your permissions.')
    }
  }

  // Stop voice recording
  const stopRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop()
      setIsRecording(false)
    }
  }

  // Process audio to text using Google Speech-to-Text API
  const processAudioToText = async (audioBlob: Blob) => {
    try {
      setIsProcessingAudio(true)

      // Convert audio blob to base64
      const reader = new FileReader()
      reader.readAsDataURL(audioBlob)

      reader.onloadend = async () => {
        try {
          const base64Audio = reader.result as string

          // Send to our Speech-to-Text API
          const response = await fetch('/api/speech-to-text', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              audioData: base64Audio,
              language: 'en-US',
              sampleRate: 44100
            })
          })

          const result = await response.json()

          if (result.success && result.transcript) {
            // Set the transcribed text in the input field
            setInputMessage(result.transcript)

            // Professional success feedback
            if (result.confidence && result.confidence > 0.9) {
              console.log(`🎯 Excellent transcription quality: ${(result.confidence * 100).toFixed(1)}%`)
            } else if (result.confidence && result.confidence > 0.8) {
              console.log(`✅ Good transcription quality: ${(result.confidence * 100).toFixed(1)}%`)
            } else if (result.confidence && result.confidence > 0.7) {
              console.log(`⚠️ Moderate transcription quality: ${(result.confidence * 100).toFixed(1)}%`)
            }

            // Log alternatives for debugging (in production, you might show these to user)
            if (result.alternatives && result.alternatives.length > 0) {
              console.log('📝 Alternative transcriptions available:', result.alternatives)
            }

            // Add a subtle success indicator
            console.log('🔊 Voice message successfully transcribed by Google Speech-to-Text')

          } else {
            throw new Error(result.error || 'Google Speech-to-Text failed to transcribe audio')
          }

        } catch (apiError) {
          console.error('Speech-to-text API error:', apiError)
          setInputMessage('Sorry, I couldn\'t understand that. Please try speaking more clearly or type your message.')
        } finally {
          setIsProcessingAudio(false)
        }
      }

      reader.onerror = () => {
        console.error('Error reading audio file')
        setIsProcessingAudio(false)
        alert('Error reading audio file. Please try again.')
      }

    } catch (error) {
      console.error('Error processing audio:', error)
      setIsProcessingAudio(false)
      alert('Error processing voice recording. Please try again.')
    }
  }

  const copyMessage = async (content: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopiedMessageId(messageId)
      setTimeout(() => setCopiedMessageId(null), 2000)
    } catch (error) {
      console.error('Failed to copy message:', error)
    }
  }

  const clearChat = () => {
    setMessages([
      {
        id: '1',
        type: 'ai',
        content: 'Hello! I\'m your AI assistant for Revantad Store. I can help you with business analytics, inventory management, customer debt tracking, and store operations. How can I assist you today?',
        timestamp: new Date()
      }
    ])
  }



  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    })
  }

  return (
    <>
      {/* AI Assistant Toggle Button - Professional Design */}
      <motion.div
        className={`fixed bottom-6 right-6 z-50 ${isOpen ? 'hidden' : 'block'}`}
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ type: 'spring', stiffness: 300, damping: 25, delay: 0.1 }}
      >
        <motion.button
          onClick={() => setIsOpen(true)}
          className="relative group"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {/* Main Button Container */}
          <div
            className="relative w-16 h-16 rounded-2xl shadow-xl transition-all duration-300 overflow-hidden"
            style={{
              background: resolvedTheme === 'dark'
                ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
                : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
              border: resolvedTheme === 'dark'
                ? '1px solid rgba(148, 163, 184, 0.2)'
                : '1px solid rgba(226, 232, 240, 0.8)',
              boxShadow: resolvedTheme === 'dark'
                ? '0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05)'
                : '0 10px 25px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.05)'
            }}
          >
            {/* Gradient Overlay on Hover */}
            <div
              className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              style={{
                background: 'linear-gradient(135deg, rgba(217, 119, 6, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%)'
              }}
            />

            {/* AI Icon */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div
                className="p-2 rounded-xl transition-all duration-300 group-hover:scale-110"
                style={{
                  background: 'linear-gradient(135deg, #d97706 0%, #059669 100%)',
                  boxShadow: '0 4px 12px rgba(217, 119, 6, 0.3)'
                }}
              >
                <Bot className="w-6 h-6 text-white" />
              </div>
            </div>

            {/* Status Indicator */}
            <div className="absolute top-2 right-2">
              <div className="w-3 h-3 bg-emerald-500 rounded-full animate-pulse shadow-sm">
                <div className="absolute inset-0 bg-emerald-500 rounded-full animate-ping opacity-75" />
              </div>
            </div>

            {/* Notification Badge */}
            <div className="absolute -top-1 -right-1 w-5 h-5 bg-amber-600 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-xs font-bold text-white">!</span>
            </div>
          </div>

          {/* Professional Tooltip */}
          <div
            className="absolute right-full mr-4 top-1/2 transform -translate-y-1/2 px-4 py-2 rounded-xl text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap shadow-lg"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#ffffff',
              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#0f172a',
              border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(226, 232, 240, 0.8)',
              boxShadow: resolvedTheme === 'dark'
                ? '0 8px 25px rgba(0, 0, 0, 0.4)'
                : '0 8px 25px rgba(0, 0, 0, 0.15)'
            }}
          >
            <div className="flex items-center space-x-2">
              <Sparkles className="w-4 h-4 text-emerald-500" />
              <span>AI Assistant</span>
            </div>
            {/* Tooltip Arrow */}
            <div
              className="absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0"
              style={{
                borderTop: '6px solid transparent',
                borderBottom: '6px solid transparent',
                borderLeft: `6px solid ${resolvedTheme === 'dark' ? '#0f172a' : '#ffffff'}`
              }}
            />
          </div>
        </motion.button>
      </motion.div>

      {/* AI Chat Interface - Professional Design */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className={`fixed bottom-6 right-6 z-50 rounded-2xl shadow-2xl overflow-hidden backdrop-blur-md flex flex-col ${
              isMinimized ? 'w-80 h-[60px]' : 'w-[420px] h-[550px]'
            }`}
            style={{
              backgroundColor: resolvedTheme === 'dark'
                ? 'rgba(15, 23, 42, 0.95)'
                : 'rgba(255, 255, 255, 0.95)',
              border: resolvedTheme === 'dark'
                ? '1px solid rgba(217, 119, 6, 0.3)'
                : '1px solid rgba(16, 185, 129, 0.3)',
              boxShadow: resolvedTheme === 'dark'
                ? '0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(217, 119, 6, 0.2)'
                : '0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(16, 185, 129, 0.2)'
            }}
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            {/* Professional Header - Always Visible */}
            <div
              className={`flex items-center justify-between px-4 flex-shrink-0 ${
                isMinimized ? 'py-2 border-0' : 'py-3 border-b'
              }`}
              style={{
                background: resolvedTheme === 'dark'
                  ? 'linear-gradient(135deg, rgba(120, 53, 15, 0.9) 0%, rgba(6, 78, 59, 0.8) 100%)'
                  : 'linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(16, 185, 129, 0.15) 100%)',
                borderColor: resolvedTheme === 'dark' ? 'rgba(217, 119, 6, 0.3)' : 'rgba(16, 185, 129, 0.3)',
                height: isMinimized ? '60px' : 'auto',
                minHeight: '60px'
              }}
            >
              <div className="flex items-center space-x-3">
                <div
                  className={`rounded-xl ${isMinimized ? 'p-1.5' : 'p-2'}`}
                  style={{
                    background: 'linear-gradient(135deg, #d97706 0%, #059669 100%)',
                    boxShadow: '0 4px 12px rgba(217, 119, 6, 0.3)'
                  }}
                >
                  <Bot className={`text-white ${isMinimized ? 'w-3.5 h-3.5' : 'w-4 h-4'}`} />
                </div>
                {!isMinimized && (
                  <div>
                    <h3
                      className="font-semibold text-sm"
                      style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#0f172a' }}
                    >
                      AI Assistant
                    </h3>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse" />
                      <p
                        className="text-xs"
                        style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                      >
                        Online
                      </p>
                    </div>
                  </div>
                )}
                {isMinimized && (
                  <div className="flex items-center space-x-2">
                    <h3
                      className="font-semibold text-sm"
                      style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#0f172a' }}
                    >
                      AI Assistant
                    </h3>
                    <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse" />
                  </div>
                )}
              </div>
              <div className={`flex items-center ${isMinimized ? 'space-x-0.5' : 'space-x-1'}`}>
                {/* Voice Recording Button */}
                {!isMinimized && (
                  <div className="relative group">
                    <motion.button
                      onClick={isRecording ? stopRecording : startRecording}
                      disabled={isProcessingAudio}
                      className={`p-2 rounded-lg transition-all duration-200 relative ${
                        isRecording
                          ? 'bg-amber-50 dark:bg-amber-900/20 hover:bg-amber-100 dark:hover:bg-amber-900/30'
                          : isProcessingAudio
                          ? 'bg-emerald-50 dark:bg-emerald-900/20'
                          : 'hover:bg-amber-50 dark:hover:bg-amber-900/20'
                      } ${isProcessingAudio ? 'cursor-not-allowed opacity-70' : ''}`}
                      title={
                        isProcessingAudio
                          ? 'Processing with Google Speech-to-Text...'
                          : isRecording
                          ? 'Recording voice message - Click to stop'
                          : 'Voice Recording powered by Google'
                      }
                      whileHover={!isProcessingAudio ? { scale: 1.05 } : {}}
                      whileTap={!isProcessingAudio ? { scale: 0.95 } : {}}
                    >
                    {isProcessingAudio ? (
                      <Loader2
                        className="w-4 h-4 animate-spin"
                        style={{ color: resolvedTheme === 'dark' ? '#10b981' : '#059669' }}
                      />
                    ) : isRecording ? (
                      <div className="relative">
                        <Square
                          className="w-4 h-4 fill-current"
                          style={{ color: resolvedTheme === 'dark' ? '#d97706' : '#b45309' }}
                        />
                        {/* Recording pulse animation */}
                        <motion.div
                          className="absolute -inset-1 rounded-full border-2 border-amber-500"
                          animate={{
                            scale: [1, 1.3, 1],
                            opacity: [0.7, 0.3, 0.7]
                          }}
                          transition={{
                            duration: 1,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        />
                      </div>
                    ) : (
                      <Mic
                        className="w-4 h-4"
                        style={{ color: resolvedTheme === 'dark' ? '#10b981' : '#059669' }}
                      />
                    )}

                    {/* Recording status indicator */}
                    <div
                      className={`absolute -top-1 -right-1 w-2 h-2 rounded-full transition-all duration-200 ${
                        isRecording
                          ? 'bg-amber-600 animate-pulse shadow-sm'
                          : isProcessingAudio
                          ? 'bg-emerald-500 shadow-sm'
                          : 'bg-amber-400 dark:bg-amber-600'
                      }`}
                    />
                  </motion.button>

                  {/* Voice Recording Tooltip */}
                  <div
                    className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 px-3 py-2 rounded-lg text-xs font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap shadow-lg z-10"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#ffffff',
                      color: resolvedTheme === 'dark' ? '#f1f5f9' : '#0f172a',
                      border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(226, 232, 240, 0.8)',
                      boxShadow: resolvedTheme === 'dark'
                        ? '0 8px 25px rgba(0, 0, 0, 0.4)'
                        : '0 8px 25px rgba(0, 0, 0, 0.15)'
                    }}
                  >
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        isRecording ? 'bg-amber-600 animate-pulse' :
                        isProcessingAudio ? 'bg-emerald-500' : 'bg-amber-400'
                      }`} />
                      <span>
                        {isProcessingAudio
                          ? 'Google Speech-to-Text Processing...'
                          : isRecording
                          ? `Recording ${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`
                          : 'Voice Recording (Google Powered)'
                        }
                      </span>
                    </div>
                    {/* Tooltip Arrow */}
                    <div
                      className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0"
                      style={{
                        borderLeft: '6px solid transparent',
                        borderRight: '6px solid transparent',
                        borderTop: `6px solid ${resolvedTheme === 'dark' ? '#0f172a' : '#ffffff'}`
                      }}
                    />
                  </div>
                </div>
                )}

                {/* Control Buttons */}
                {!isMinimized && (
                  <>
                    <button
                      onClick={clearChat}
                      className="p-2 rounded-lg transition-all duration-200 hover:bg-amber-50 dark:hover:bg-amber-900/20"
                      title="Clear chat"
                    >
                      <RotateCcw className="w-4 h-4" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                    </button>
                  </>
                )}

                {/* Always visible buttons */}
                <button
                  onClick={() => setIsMinimized(!isMinimized)}
                  className={`rounded-lg transition-all duration-200 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 ${
                    isMinimized ? 'p-1.5' : 'p-2'
                  }`}
                  title={isMinimized ? 'Maximize' : 'Minimize'}
                >
                  {isMinimized ? (
                    <Maximize2 className={`${isMinimized ? 'w-3.5 h-3.5' : 'w-4 h-4'}`} style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                  ) : (
                    <Minimize2 className={`${isMinimized ? 'w-3.5 h-3.5' : 'w-4 h-4'}`} style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                  )}
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className={`rounded-lg transition-all duration-200 hover:bg-amber-100 dark:hover:bg-amber-900/20 ${
                    isMinimized ? 'p-1.5' : 'p-2'
                  }`}
                  title="Close chat"
                >
                  <X className={`hover:text-amber-600 ${isMinimized ? 'w-3.5 h-3.5' : 'w-4 h-4'}`} style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                </button>
              </div>
            </div>

            {!isMinimized && (
              <>
                {/* Messages Area - Professional Design */}
                <div className="flex-1 overflow-y-auto p-3 space-y-3 h-[380px] scrollbar-thin scrollbar-thumb-amber-300 dark:scrollbar-thumb-amber-600 scrollbar-track-transparent">
                  {messages.map((message) => {
                    if (message.isTyping) {
                      return (
                        <motion.div
                          key={message.id}
                          className="flex justify-start"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                        >
                          <div className="flex items-start space-x-2">
                            <div
                              className="p-2 rounded-full"
                              style={{ backgroundColor: resolvedTheme === 'dark' ? '#059669' : '#10b981' }}
                            >
                              <Bot className="w-4 h-4 text-white" />
                            </div>
                            <div
                              className="p-3 rounded-2xl rounded-tl-md flex items-center space-x-2"
                              style={{
                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)'
                              }}
                            >
                              <div className="flex space-x-1">
                                <motion.div
                                  className="w-2 h-2 rounded-full"
                                  style={{ backgroundColor: resolvedTheme === 'dark' ? '#059669' : '#10b981' }}
                                  animate={{ scale: [1, 1.2, 1] }}
                                  transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
                                />
                                <motion.div
                                  className="w-2 h-2 rounded-full"
                                  style={{ backgroundColor: resolvedTheme === 'dark' ? '#059669' : '#10b981' }}
                                  animate={{ scale: [1, 1.2, 1] }}
                                  transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
                                />
                                <motion.div
                                  className="w-2 h-2 rounded-full"
                                  style={{ backgroundColor: resolvedTheme === 'dark' ? '#059669' : '#10b981' }}
                                  animate={{ scale: [1, 1.2, 1] }}
                                  transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
                                />
                              </div>
                              <span className="text-sm" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>
                                AI is thinking...
                              </span>
                            </div>
                          </div>
                        </motion.div>
                      )
                    }

                    return (
                      <motion.div
                        key={message.id}
                        className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} group`}
                        initial={{ opacity: 0, y: 20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{ duration: 0.3, type: 'spring', stiffness: 200 }}
                      >
                        <div className={`flex items-start space-x-2 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                          <motion.div
                            className="p-2 rounded-full flex-shrink-0 relative overflow-hidden"
                            style={{
                              backgroundColor: message.type === 'user'
                                ? (resolvedTheme === 'dark' ? '#22c55e' : '#16a34a')
                                : (resolvedTheme === 'dark' ? '#059669' : '#10b981')
                            }}
                            whileHover={{ scale: 1.1 }}
                          >
                            {/* Shimmer effect */}
                            <div
                              className="absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-300"
                              style={{
                                background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)',
                                transform: 'translateX(-100%)',
                                animation: 'shimmer 1.5s infinite'
                              }}
                            />
                            {message.type === 'user' ? (
                              <User className="w-4 h-4 text-white relative z-10" />
                            ) : (
                              <Bot className="w-4 h-4 text-white relative z-10" />
                            )}
                          </motion.div>
                          <div className="relative">
                            <motion.div
                              className={`p-3 rounded-2xl ${message.type === 'user' ? 'rounded-tr-md' : 'rounded-tl-md'} relative overflow-hidden`}
                              style={{
                                backgroundColor: message.type === 'user'
                                  ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')
                                  : (resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)'),
                                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',
                                border: `1px solid ${message.type === 'user'
                                  ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.3)' : 'rgba(34, 197, 94, 0.2)')
                                  : (resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.3)' : 'rgba(139, 92, 246, 0.2)')}`
                              }}
                              whileHover={{ scale: 1.02 }}
                            >
                              <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
                              <div className="flex items-center justify-between mt-2">
                                <p
                                  className="text-xs opacity-70"
                                  style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                                >
                                  {formatTime(message.timestamp)}
                                </p>
                                {message.type === 'ai' && (
                                  <button
                                    onClick={() => copyMessage(message.content, message.id)}
                                    className="opacity-0 group-hover:opacity-100 p-1 rounded transition-all duration-200 hover:bg-emerald-100 dark:hover:bg-emerald-900/20"
                                    title="Copy message"
                                  >
                                    {copiedMessageId === message.id ? (
                                      <Check className="w-3 h-3 text-emerald-500" />
                                    ) : (
                                      <Copy className="w-3 h-3" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                                    )}
                                  </button>
                                )}
                              </div>
                            </motion.div>
                          </div>
                        </div>
                      </motion.div>
                    )
                  })}

                  <div ref={messagesEndRef} />
                </div>

                {/* Professional Input Section */}
                <div
                  className="p-4 border-t"
                  style={{
                    borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.2)' : 'rgba(226, 232, 240, 0.8)',
                    background: resolvedTheme === 'dark'
                      ? 'rgba(15, 23, 42, 0.8)'
                      : 'rgba(248, 250, 252, 0.8)'
                  }}
                >
                  {/* Voice Recording Status */}
                  {(isRecording || isProcessingAudio) && (
                    <motion.div
                      className="mb-3 p-3 rounded-lg border"
                      style={{
                        backgroundColor: isRecording
                          ? (resolvedTheme === 'dark' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(254, 226, 226, 0.8)')
                          : (resolvedTheme === 'dark' ? 'rgba(251, 191, 36, 0.1)' : 'rgba(254, 243, 199, 0.8)'),
                        borderColor: isRecording
                          ? (resolvedTheme === 'dark' ? 'rgba(239, 68, 68, 0.3)' : 'rgba(239, 68, 68, 0.3)')
                          : (resolvedTheme === 'dark' ? 'rgba(251, 191, 36, 0.3)' : 'rgba(251, 191, 36, 0.3)')
                      }}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          {isRecording ? (
                            <motion.div
                              className="w-3 h-3 bg-amber-600 rounded-full"
                              animate={{ scale: [1, 1.2, 1] }}
                              transition={{ duration: 1, repeat: Infinity }}
                            />
                          ) : (
                            <Loader2 className="w-4 h-4 animate-spin text-emerald-500" />
                          )}
                          <span
                            className="text-sm font-medium"
                            style={{
                              color: isRecording
                                ? (resolvedTheme === 'dark' ? '#d97706' : '#b45309')
                                : (resolvedTheme === 'dark' ? '#10b981' : '#059669')
                            }}
                          >
                            {isRecording
                              ? `🎙️ Recording... ${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`
                              : '🔄 Google Speech-to-Text Processing...'
                            }
                          </span>
                        </div>
                        {isRecording && (
                          <button
                            onClick={stopRecording}
                            className="px-3 py-1 text-xs rounded-lg bg-amber-600 hover:bg-amber-700 text-white transition-colors duration-200"
                          >
                            Stop
                          </button>
                        )}
                      </div>
                    </motion.div>
                  )}
                  <div className="flex items-end space-x-3">
                    <div className="flex-1 relative">
                      <div className="relative">
                        <textarea
                          ref={inputRef}
                          value={inputMessage}
                          onChange={(e) => {
                            setInputMessage(e.target.value)
                            // Auto-resize functionality
                            const target = e.target as HTMLTextAreaElement
                            target.style.height = 'auto'
                            const scrollHeight = target.scrollHeight
                            const newHeight = Math.min(Math.max(scrollHeight, 44), 120)
                            target.style.height = newHeight + 'px'
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault()
                              sendMessage()
                            }
                          }}
                          placeholder="Ask me anything about your store..."
                          disabled={isLoading}
                          rows={1}
                          className="w-full px-4 py-3 pr-12 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 resize-none overflow-y-auto text-sm"
                          style={{
                            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                            borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(226, 232, 240, 0.8)',
                            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#0f172a',
                            minHeight: '44px',
                            maxHeight: '120px',
                            lineHeight: '1.5'
                          }}
                        />

                        {/* Character count */}
                        <div
                          className="absolute bottom-2 right-12 text-xs px-2 py-1 rounded"
                          style={{
                            color: resolvedTheme === 'dark' ? '#64748b' : '#94a3b8',
                            backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.8)' : 'rgba(255, 255, 255, 0.8)'
                          }}
                        >
                          {inputMessage.length}
                        </div>
                      </div>
                    </div>

                    <motion.button
                      onClick={sendMessage}
                      disabled={!inputMessage.trim() || isLoading}
                      className="p-3 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-emerald-500 flex-shrink-0"
                      style={{
                        background: !inputMessage.trim() || isLoading
                          ? (resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.6)' : 'rgba(156, 163, 175, 0.6)')
                          : 'linear-gradient(135deg, #d97706 0%, #059669 100%)',
                        boxShadow: !inputMessage.trim() || isLoading
                          ? 'none'
                          : '0 4px 12px rgba(217, 119, 6, 0.3)',
                        height: '44px',
                        width: '44px'
                      }}
                      whileHover={!inputMessage.trim() || isLoading ? {} : { scale: 1.05 }}
                      whileTap={!inputMessage.trim() || isLoading ? {} : { scale: 0.95 }}
                    >
                      <div className="flex items-center justify-center">
                        {isLoading ? (
                          <Loader2 className="w-4 h-4 text-white animate-spin" />
                        ) : (
                          <Send className="w-4 h-4 text-white" />
                        )}
                      </div>
                    </motion.button>
                  </div>

                  {/* Quick Actions & Google Branding */}
                  {!inputMessage && (
                    <div className="mt-3">
                      <div className="flex flex-wrap gap-2 mb-2">
                        {['Sales trends', 'Inventory tips', 'Debt analysis'].map((suggestion) => (
                          <button
                            key={suggestion}
                            onClick={() => setInputMessage(`Show me ${suggestion.toLowerCase()}`)}
                            className="px-3 py-1 text-xs rounded-lg transition-colors duration-200"
                            style={{
                              backgroundColor: resolvedTheme === 'dark' ? 'rgba(16, 185, 129, 0.1)' : 'rgba(16, 185, 129, 0.1)',
                              color: resolvedTheme === 'dark' ? '#34d399' : '#059669',
                              border: resolvedTheme === 'dark' ? '1px solid rgba(16, 185, 129, 0.2)' : '1px solid rgba(16, 185, 129, 0.2)'
                            }}
                          >
                            {suggestion}
                          </button>
                        ))}
                      </div>

                      {/* Google Speech-to-Text Branding */}
                      <div className="flex items-center justify-center space-x-2 text-xs opacity-60">
                        <Mic className="w-3 h-3" />
                        <span style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}>
                          Voice powered by Google Speech-to-Text
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
