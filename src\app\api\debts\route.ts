import { NextRequest, NextResponse } from 'next/server'

import {
  successResponse,
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  handleDatabaseError,
  parsePaginationParams,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all customer debts with pagination
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const { page, limit, offset } = parsePaginationParams(searchParams)

  // Optional filters
  const search = searchParams.get('search')
  const dateFrom = searchParams.get('dateFrom')
  const dateTo = searchParams.get('dateTo')

  let query = supabase
    .from('customer_debts')
    .select('*', { count: 'exact' })
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1)

  // Apply filters
  if (search) {
    query = query.or(`customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%,product_name.ilike.%${search}%`)
  }

  if (dateFrom) {
    query = query.gte('debt_date', dateFrom)
  }

  if (dateTo) {
    query = query.lte('debt_date', dateTo)
  }

  const { data: debts, error, count } = await query

  if (error) {
    handleDatabaseError(error)
  }

  return successResponse({
    debts: debts || [],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  })
})

// POST - Create new customer debt
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      customer_name,
      customer_family_name,
      product_name,
      product_price,
      quantity,
      debt_date,
    } = body

    // Validate required fields
    if (
      !customer_name ||
      !customer_family_name ||
      !product_name ||
      !product_price ||
      !quantity
    ) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const { data: debt, error } = await supabase
      .from('customer_debts')
      .insert([
        {
          customer_name,
          customer_family_name,
          product_name,
          product_price: parseFloat(product_price),
          quantity: parseInt(quantity),
          debt_date: debt_date || new Date().toISOString().split('T')[0],
        },
      ])
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ debt }, { status: 201 })
  } catch {
    return NextResponse.json(
      { error: 'Failed to create customer debt' },
      { status: 500 }
    )
  }
}
