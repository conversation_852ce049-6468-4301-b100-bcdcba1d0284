{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/AdminHeader.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Search, Home, Package, Users, Image, Moon, Sun, LogOut, User } from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from 'next-themes'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface AdminHeaderProps {\n  activeSection: string\n  setActiveSection: (section: string) => void\n}\n\nexport default function AdminHeader({ activeSection, setActiveSection }: AdminHeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n  const { setTheme, resolvedTheme } = useTheme()\n  const [isProfileOpen, setIsProfileOpen] = useState(false)\n  const [mounted, setMounted] = useState(false)\n  const { user, logout } = useAuth()\n\n  // Handle hydration\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n\n\n  const navigationItems = [\n    {\n      id: 'dashboard',\n      label: 'Home Dashboard',\n      icon: Home,\n      tooltip: 'Dashboard Overview'\n    },\n    {\n      id: 'products',\n      label: 'Product Lists',\n      icon: Package,\n      tooltip: 'Manage Products'\n    },\n    {\n      id: 'debts',\n      label: 'Customer Debts',\n      icon: Users,\n      tooltip: 'Customer Debt Management'\n    },\n    {\n      id: 'family-gallery',\n      label: 'Family Gallery',\n      icon: Image,\n      tooltip: 'Family Photos & Memories'\n    },\n  ]\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    // Implement search functionality\n    console.log('Searching for:', searchQuery)\n  }\n\n  const toggleTheme = () => {\n    if (!mounted) return\n\n    // Manual DOM manipulation for immediate visual feedback\n    const html = document.documentElement\n    const isDark = resolvedTheme === 'dark'\n\n    if (isDark) {\n      html.classList.remove('dark')\n      setTheme('light')\n    } else {\n      html.classList.add('dark')\n      setTheme('dark')\n    }\n  }\n\n  const handleLogout = () => {\n    logout()\n    window.location.href = '/login'\n  }\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm transition-all duration-300\" style={{\n      backgroundColor: resolvedTheme === 'dark' ? '#111827' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb'\n    }}>\n      <div className=\"grid grid-cols-3 items-center h-16 px-3 sm:px-4 lg:px-6 max-w-full overflow-hidden gap-4\">\n        \n        {/* Left Section - Logo & Search (Fixed Width) */}\n        <div className=\"flex items-center space-x-3 w-auto\">\n          {/* Revantad Logo */}\n          <Link\n            href=\"/landing\"\n            className=\"flex items-center space-x-2 hover:opacity-80 transition-opacity flex-shrink-0\"\n            title=\"Return to Front Page\"\n          >\n            <div className=\"w-10 h-10 hero-gradient rounded-full flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">R</span>\n            </div>\n            <span className=\"text-xl font-bold text-gradient hidden sm:block\">Revantad</span>\n          </Link>\n\n          {/* Search Bar - Much Shorter than Sidebar (320px) */}\n          <form onSubmit={handleSearch} className=\"w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-slate-700 border-0 rounded-full text-sm placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:bg-white dark:focus:bg-slate-600 transition-all duration-200\"\n              />\n            </div>\n          </form>\n        </div>\n\n        {/* Center Section - Navigation Icons (Facebook-style) */}\n        <div className=\"hidden sm:flex items-center justify-center\">\n          <div className=\"flex items-center space-x-3 md:space-x-4 lg:space-x-5\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon\n              const isActive = activeSection === item.id\n\n              return (\n                <button\n                  key={item.id}\n                  onClick={() => setActiveSection(item.id)}\n                  className={`relative p-3 md:p-3.5 lg:p-4 rounded-xl transition-all duration-200 group min-w-[48px] md:min-w-[52px] lg:min-w-[56px] hover:scale-[1.05]`}\n                  style={{\n                    backgroundColor: isActive\n                      ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')\n                      : 'transparent',\n                    color: isActive\n                      ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                      : (resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'),\n                    boxShadow: isActive ? '0 2px 8px rgba(34, 197, 94, 0.2)' : 'none'\n                  }}\n                  title={item.tooltip}\n                  onMouseEnter={(e) => {\n                    if (!isActive) {\n                      e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.5)' : 'rgba(243, 244, 246, 0.8)'\n                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'\n                    }\n                  }}\n                  onMouseLeave={(e) => {\n                    if (!isActive) {\n                      e.currentTarget.style.backgroundColor = 'transparent'\n                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'\n                    }\n                  }}\n                >\n                  <Icon className=\"h-5 w-5 md:h-6 md:w-6 mx-auto transition-all duration-200\" />\n                  {isActive && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-10 md:w-12 lg:w-14 h-1 bg-green-500 rounded-full\"></div>\n                  )}\n\n                  {/* Enhanced Tooltip */}\n                  <div className=\"absolute top-full mt-3 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:bg-slate-700 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap pointer-events-none shadow-lg z-10\">\n                    {item.tooltip}\n                    <div className=\"absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 dark:bg-slate-700 rotate-45\"></div>\n                  </div>\n                </button>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Mobile Navigation - Simplified */}\n        <div className=\"sm:hidden flex items-center justify-center\">\n          <button\n            onClick={() => setActiveSection(activeSection === 'dashboard' ? 'products' : 'dashboard')}\n            className=\"p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors\"\n            title=\"Toggle View\"\n          >\n            {activeSection === 'dashboard' ? (\n              <Package className=\"h-5 w-5\" />\n            ) : (\n              <Home className=\"h-5 w-5\" />\n            )}\n          </button>\n        </div>\n\n        {/* Right Section - Dark Mode & Profile */}\n        <div className=\"flex items-center justify-end space-x-3\">\n\n\n          {/* Dark Mode Toggle */}\n          <button\n            onClick={toggleTheme}\n            className=\"p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 flex-shrink-0\"\n            title={mounted ? `Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode (Current: ${resolvedTheme})` : 'Toggle theme'}\n            disabled={!mounted}\n          >\n            {!mounted ? (\n              <div className=\"h-5 w-5 bg-gray-400 rounded-full animate-pulse\" />\n            ) : resolvedTheme === 'dark' ? (\n              <Sun className=\"h-5 w-5\" />\n            ) : (\n              <Moon className=\"h-5 w-5\" />\n            )}\n          </button>\n\n          {/* Profile Dropdown */}\n          <div className=\"relative flex-shrink-0\">\n            <button\n              onClick={() => setIsProfileOpen(!isProfileOpen)}\n              className=\"flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 group\"\n            >\n              <div className=\"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow\">\n                <User className=\"h-4 w-4 text-white\" />\n              </div>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300 hidden sm:block group-hover:text-gray-900 dark:group-hover:text-white transition-colors\">\n                {user?.name || 'Admin'}\n              </span>\n            </button>\n\n            {/* Dropdown Menu */}\n            {isProfileOpen && (\n              <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1\">\n                <div className=\"px-4 py-2 border-b border-gray-200 dark:border-slate-700\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white\">{user?.name || 'Admin User'}</p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">{user?.email || '<EMAIL>'}</p>\n                </div>\n                \n                <button\n                  onClick={() => setActiveSection('settings')}\n                  className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2\"\n                >\n                  <User className=\"h-4 w-4\" />\n                  <span>Settings</span>\n                </button>\n                \n                <button\n                  onClick={handleLogout}\n                  className=\"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                  <span>Logout</span>\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAae,SAAS,YAAY,EAAE,aAAa,EAAE,gBAAgB,EAAoB;;IACvF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE/B,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE;IAIL,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,OAAO;YACP,MAAM,sMAAA,CAAA,OAAI;YACV,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,2MAAA,CAAA,UAAO;YACb,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,SAAS;QACX;KACD;IAED,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,iCAAiC;QACjC,QAAQ,GAAG,CAAC,kBAAkB;IAChC;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QAEd,wDAAwD;QACxD,MAAM,OAAO,SAAS,eAAe;QACrC,MAAM,SAAS,kBAAkB;QAEjC,IAAI,QAAQ;YACV,KAAK,SAAS,CAAC,MAAM,CAAC;YACtB,SAAS;QACX,OAAO;YACL,KAAK,SAAS,CAAC,GAAG,CAAC;YACnB,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,6LAAC;QAAO,WAAU;QAAgJ,OAAO;YACvK,iBAAiB,kBAAkB,SAAS,YAAY;YACxD,aAAa,kBAAkB,SAAS,YAAY;QACtD;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,OAAM;;8CAEN,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAAkD;;;;;;;;;;;;sCAIpE,6LAAC;4BAAK,UAAU;4BAAc,WAAU;sCACtC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOlB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC;4BACpB,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,kBAAkB,KAAK,EAAE;4BAE1C,qBACE,6LAAC;gCAEC,SAAS,IAAM,iBAAiB,KAAK,EAAE;gCACvC,WAAW,CAAC,yIAAyI,CAAC;gCACtJ,OAAO;oCACL,iBAAiB,WACZ,kBAAkB,SAAS,2BAA2B,2BACvD;oCACJ,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;oCAC5C,WAAW,WAAW,qCAAqC;gCAC7D;gCACA,OAAO,KAAK,OAAO;gCACnB,cAAc,CAAC;oCACb,IAAI,CAAC,UAAU;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,2BAA2B;wCAC9F,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,kBAAkB,SAAS,YAAY;oCACvE;gCACF;gCACA,cAAc,CAAC;oCACb,IAAI,CAAC,UAAU;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,kBAAkB,SAAS,YAAY;oCACvE;gCACF;;kDAEA,6LAAC;wCAAK,WAAU;;;;;;oCACf,0BACC,6LAAC;wCAAI,WAAU;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO;0DACb,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAlCZ,KAAK,EAAE;;;;;wBAsClB;;;;;;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,SAAS,IAAM,iBAAiB,kBAAkB,cAAc,aAAa;wBAC7E,WAAU;wBACV,OAAM;kCAEL,kBAAkB,4BACjB,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;iDAEnB,6LAAC,sMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;8BAMtB,6LAAC;oBAAI,WAAU;;sCAIb,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO,UAAU,CAAC,UAAU,EAAE,kBAAkB,SAAS,UAAU,OAAO,gBAAgB,EAAE,cAAc,CAAC,CAAC,GAAG;4BAC/G,UAAU,CAAC;sCAEV,CAAC,wBACA,6LAAC;gCAAI,WAAU;;;;;uCACb,kBAAkB,uBACpB,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;qDAEf,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAKpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,iBAAiB,CAAC;oCACjC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAK,WAAU;sDACb,MAAM,QAAQ;;;;;;;;;;;;gCAKlB,+BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqD,MAAM,QAAQ;;;;;;8DAChF,6LAAC;oDAAE,WAAU;8DAA4C,MAAM,SAAS;;;;;;;;;;;;sDAG1E,6LAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;sDAGR,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GA1OwB;;QAEc,mJAAA,CAAA,WAAQ;QAGnB,kIAAA,CAAA,UAAO;;;KALV", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  BarChart3,\n  History,\n  Calendar,\n  Settings,\n  ChevronLeft,\n  ChevronRight,\n  Bot\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\ninterface SidebarProps {\n  activeSection: string\n  setActiveSection: (section: string) => void\n}\n\nexport default function Sidebar({ activeSection, setActiveSection }: SidebarProps) {\n  const { resolvedTheme } = useTheme()\n  const [isCollapsed, setIsCollapsed] = useState(false)\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n    const savedState = localStorage.getItem('sidebar-collapsed')\n    if (savedState !== null) {\n      setIsCollapsed(JSON.parse(savedState))\n    }\n  }, [])\n\n  useEffect(() => {\n    if (mounted) {\n      localStorage.setItem('sidebar-collapsed', JSON.stringify(isCollapsed))\n    }\n  }, [isCollapsed, mounted])\n\n  const toggleSidebar = () => {\n    setIsCollapsed(!isCollapsed)\n  }\n\n  const menuItems = [\n    {\n      id: 'ai-support',\n      label: 'AI Support',\n      icon: Bot\n    },\n    {\n      id: 'api-graphing',\n      label: 'API Graphing & Visuals',\n      icon: BarChart3\n    },\n    {\n      id: 'history',\n      label: 'History',\n      icon: History\n    },\n    {\n      id: 'calendar',\n      label: 'Calendar',\n      icon: Calendar\n    },\n    {\n      id: 'settings',\n      label: 'Settings',\n      icon: Settings\n    },\n  ]\n\n  return (\n    <div\n      className={`shadow-xl border-r sticky top-16 h-[calc(100vh-4rem)] transition-all duration-300 ease-in-out ${\n        isCollapsed ? 'w-20 min-w-20' : 'w-80 min-w-80'\n      }`}\n      style={{\n        backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n        borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb',\n        borderWidth: '1px',\n        boxShadow: resolvedTheme === 'dark'\n          ? '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05)'\n          : '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'\n      }}\n    >\n      <div className=\"flex flex-col h-full\">\n        <div\n          className={`sticky top-0 z-20 transition-all duration-300 backdrop-blur-md ${\n            isCollapsed ? 'px-3 py-3' : 'px-6 py-4'\n          }`}\n          style={{\n            background: resolvedTheme === 'dark'\n              ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.98) 0%, rgba(51, 65, 85, 0.95) 100%)'\n              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(249, 250, 251, 0.95) 100%)',\n            borderBottom: resolvedTheme === 'dark'\n              ? '1px solid rgba(148, 163, 184, 0.2)'\n              : '1px solid rgba(229, 231, 235, 0.8)',\n            boxShadow: resolvedTheme === 'dark'\n              ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'\n              : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'\n          }}\n        >\n          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'mb-3'}`}>\n            <div\n              className={`rounded-lg flex items-center justify-center transition-all duration-300 ${\n                isCollapsed ? 'w-10 h-10' : 'w-8 h-8 mr-3'\n              }`}\n              style={{\n                background: resolvedTheme === 'dark'\n                  ? 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)'\n                  : 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n                boxShadow: '0 4px 8px rgba(34, 197, 94, 0.3)'\n              }}\n            >\n              <span className={`text-white font-bold ${isCollapsed ? 'text-base' : 'text-sm'}`}>⚡</span>\n            </div>\n            {!isCollapsed && (\n              <h2\n                className=\"text-lg font-bold transition-all duration-300 crisp-text\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',\n                  textShadow: resolvedTheme === 'dark' ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none'\n                }}\n              >\n                Additional Tools\n              </h2>\n            )}\n          </div>\n          {!isCollapsed && (\n            <p\n              className=\"text-xs font-medium transition-all duration-300 crisp-text\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b',\n                letterSpacing: '0.025em'\n              }}\n            >\n              Advanced features and utilities\n            </p>\n          )}\n        </div>\n\n        <div className=\"flex-1 relative\">\n          <button\n            onClick={toggleSidebar}\n            className=\"absolute top-4 right-2 z-30 p-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 group\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' \n                ? 'rgba(34, 197, 94, 0.1)' \n                : 'rgba(34, 197, 94, 0.08)',\n              border: resolvedTheme === 'dark' \n                ? '1px solid rgba(34, 197, 94, 0.3)' \n                : '1px solid rgba(34, 197, 94, 0.2)',\n              boxShadow: resolvedTheme === 'dark'\n                ? '0 2px 8px rgba(34, 197, 94, 0.2)'\n                : '0 2px 8px rgba(34, 197, 94, 0.15)'\n            }}\n            title={isCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar'}\n          >\n            {isCollapsed ? (\n              <ChevronRight \n                className=\"w-4 h-4 transition-all duration-200 group-hover:scale-105\" \n                style={{ color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a' }}\n              />\n            ) : (\n              <ChevronLeft \n                className=\"w-4 h-4 transition-all duration-200 group-hover:scale-105\" \n                style={{ color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a' }}\n              />\n            )}\n          </button>\n\n          <div className=\"absolute inset-0 scroll-fade-top scroll-fade-bottom\">\n            <nav className={`h-full pt-16 pb-6 overflow-y-auto sidebar-nav-scroll transition-all duration-300 space-y-1 ${\n              isCollapsed ? 'px-2' : 'px-4'\n            }`}>\n        {menuItems.map((item) => {\n          const Icon = item.icon\n          const isActive = activeSection === item.id\n\n          return (\n            <div key={item.id} className=\"relative group\">\n              <button\n                onClick={() => setActiveSection(item.id)}\n                className={`w-full flex items-center text-left transition-all duration-200 group sidebar-nav-item crisp-text relative overflow-hidden ${\n                  isCollapsed\n                    ? 'p-2.5 rounded-lg justify-center'\n                    : 'p-3 rounded-xl'\n                }`}\n                style={{\n                  background: isActive\n                    ? (resolvedTheme === 'dark'\n                      ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)'\n                      : 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%)')\n                    : 'transparent',\n                  border: isActive\n                    ? (resolvedTheme === 'dark' ? '1px solid rgba(34, 197, 94, 0.4)' : '1px solid rgba(34, 197, 94, 0.3)')\n                    : '1px solid transparent',\n                  boxShadow: isActive\n                    ? (resolvedTheme === 'dark'\n                      ? '0 4px 12px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'\n                      : '0 4px 12px rgba(34, 197, 94, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)')\n                    : 'none'\n                }}\n                onMouseEnter={(e) => {\n                  if (!isActive) {\n                    e.currentTarget.style.background = resolvedTheme === 'dark'\n                      ? 'rgba(71, 85, 105, 0.15)'\n                      : 'rgba(243, 244, 246, 0.6)'\n                    e.currentTarget.style.border = resolvedTheme === 'dark'\n                      ? '1px solid rgba(148, 163, 184, 0.2)'\n                      : '1px solid rgba(229, 231, 235, 0.6)'\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  if (!isActive) {\n                    e.currentTarget.style.background = 'transparent'\n                    e.currentTarget.style.border = '1px solid transparent'\n                  }\n                }}\n              >\n                {isCollapsed ? (\n                  <Icon\n                    className=\"h-5 w-5 transition-all duration-200 relative z-10\"\n                    style={{\n                      color: isActive\n                        ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                        : (resolvedTheme === 'dark' ? '#e2e8f0' : '#64748b'),\n                      filter: isActive ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))' : 'none'\n                    }}\n                  />\n                ) : (\n                  <>\n                    <div\n                      className=\"transition-all duration-200 relative p-2 rounded-lg mr-3 overflow-hidden\"\n                      style={{\n                        background: isActive\n                          ? (resolvedTheme === 'dark'\n                            ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.25) 100%)'\n                            : 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)')\n                          : (resolvedTheme === 'dark'\n                            ? 'linear-gradient(135deg, rgba(71, 85, 105, 0.5) 0%, rgba(51, 65, 85, 0.4) 100%)'\n                            : 'linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%)'),\n                        boxShadow: isActive\n                          ? (resolvedTheme === 'dark'\n                            ? '0 2px 8px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'\n                            : '0 2px 8px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)')\n                          : (resolvedTheme === 'dark'\n                            ? 'inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n                            : 'inset 0 1px 0 rgba(255, 255, 255, 0.9)')\n                      }}\n                    >\n                      <Icon\n                        className=\"h-4 w-4 transition-all duration-200 relative z-10\"\n                        style={{\n                          color: isActive\n                            ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                            : (resolvedTheme === 'dark' ? '#e2e8f0' : '#64748b'),\n                          filter: isActive ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))' : 'none'\n                        }}\n                      />\n                    </div>\n                    <div className=\"flex-1 sidebar-text\">\n                      <h3\n                        className=\"font-medium text-sm transition-colors duration-200 leading-snug\"\n                        style={{\n                          color: isActive\n                            ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                            : (resolvedTheme === 'dark' ? '#f8fafc' : '#111827'),\n                          fontWeight: isActive ? '600' : '500'\n                        }}\n                      >\n                        {item.label}\n                      </h3>\n                    </div>\n                  </>\n                )}\n              </button>\n              \n              {isCollapsed && (\n                <div\n                  className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 rounded-lg text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-50 whitespace-nowrap\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',\n                    border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.3)' : '1px solid rgba(229, 231, 235, 0.8)',\n                    boxShadow: resolvedTheme === 'dark'\n                      ? '0 4px 12px rgba(0, 0, 0, 0.3)'\n                      : '0 4px 12px rgba(0, 0, 0, 0.15)'\n                  }}\n                >\n                  <div className=\"font-semibold\">{item.label}</div>\n                  <div\n                    className=\"absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0\"\n                    style={{\n                      borderTop: '6px solid transparent',\n                      borderBottom: '6px solid transparent',\n                      borderRight: `6px solid ${resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'}`\n                    }}\n                  />\n                </div>\n              )}\n            </div>\n          )\n        })}\n            </nav>\n          </div>\n        </div>\n\n        {/* Enhanced Sticky Footer Section */}\n        <div\n          className={`sticky bottom-0 z-20 transition-all duration-300 backdrop-blur-md ${\n            isCollapsed ? 'px-3 py-3' : 'px-6 py-4'\n          }`}\n          style={{\n            background: resolvedTheme === 'dark'\n              ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%)'\n              : 'linear-gradient(135deg, rgba(249, 250, 251, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%)',\n            borderTop: resolvedTheme === 'dark'\n              ? '1px solid rgba(148, 163, 184, 0.2)'\n              : '1px solid rgba(229, 231, 235, 0.8)',\n            boxShadow: resolvedTheme === 'dark'\n              ? '0 -4px 6px -1px rgba(0, 0, 0, 0.3), 0 -2px 4px -1px rgba(0, 0, 0, 0.2)'\n              : '0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06)'\n          }}\n        >\n          <div\n            className=\"text-sm transition-colors duration-300\"\n            style={{\n              color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b'\n            }}\n          >\n            {isCollapsed ? (\n              <div className=\"flex justify-center\">\n                <div\n                  className=\"rounded-xl flex items-center justify-center relative overflow-hidden w-10 h-10\"\n                  style={{\n                    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                    boxShadow: '0 4px 8px rgba(59, 130, 246, 0.3)'\n                  }}\n                >\n                  <span className=\"text-white font-bold relative z-10 text-base\">R</span>\n                  <div\n                    className=\"absolute inset-0 opacity-20\"\n                    style={{\n                      background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)'\n                    }}\n                  />\n                </div>\n              </div>\n            ) : (\n              <>\n                <div className=\"flex items-center mb-2 space-x-3\">\n                  <div\n                    className=\"rounded-xl flex items-center justify-center relative overflow-hidden w-8 h-8\"\n                    style={{\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      boxShadow: '0 4px 8px rgba(59, 130, 246, 0.3)'\n                    }}\n                  >\n                    <span className=\"text-white font-bold relative z-10 text-sm\">R</span>\n                    <div\n                      className=\"absolute inset-0 opacity-20\"\n                      style={{\n                        background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)'\n                      }}\n                    />\n                  </div>\n                  <div>\n                    <span\n                      className=\"font-bold text-sm transition-colors duration-300 block\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f8fafc' : '#1e293b',\n                        textShadow: resolvedTheme === 'dark' ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none'\n                      }}\n                    >\n                      Revantad Store\n                    </span>\n                    <span\n                      className=\"text-xs font-medium\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#64748b' : '#94a3b8',\n                        letterSpacing: '0.025em'\n                      }}\n                    >\n                      Professional Business Management\n                    </span>\n                  </div>\n                </div>\n                <div\n                  className=\"text-xs font-medium px-3 py-2 rounded-lg\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)',\n                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280',\n                    border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(229, 231, 235, 0.6)'\n                  }}\n                >\n                  Admin Dashboard v2.0\n                </div>\n              </>\n            )}\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        {!isCollapsed && (\n          <div\n            className=\"absolute bottom-2 left-1/2 transform -translate-x-1/2 opacity-60 pointer-events-none\"\n            style={{\n              color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b'\n            }}\n          >\n            <div className=\"flex flex-col items-center space-y-1\">\n              <div className=\"text-xs font-medium\">Scroll for more</div>\n              <div className=\"flex space-x-1\">\n                <div className=\"w-1 h-1 rounded-full bg-current animate-pulse\"></div>\n                <div className=\"w-1 h-1 rounded-full bg-current animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n                <div className=\"w-1 h-1 rounded-full bg-current animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAZA;;;;AAmBe,SAAS,QAAQ,EAAE,aAAa,EAAE,gBAAgB,EAAgB;;IAC/E,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,WAAW;YACX,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,eAAe,MAAM;gBACvB,eAAe,KAAK,KAAK,CAAC;YAC5B;QACF;4BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,SAAS;gBACX,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAC3D;QACF;4BAAG;QAAC;QAAa;KAAQ;IAEzB,MAAM,gBAAgB;QACpB,eAAe,CAAC;IAClB;IAEA,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,MAAM,mMAAA,CAAA,MAAG;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,qNAAA,CAAA,YAAS;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,2MAAA,CAAA,UAAO;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,6MAAA,CAAA,WAAQ;QAChB;KACD;IAED,qBACE,6LAAC;QACC,WAAW,CAAC,8FAA8F,EACxG,cAAc,kBAAkB,iBAChC;QACF,OAAO;YACL,iBAAiB,kBAAkB,SAAS,YAAY;YACxD,aAAa,kBAAkB,SAAS,YAAY;YACpD,aAAa;YACb,WAAW,kBAAkB,SACzB,8EACA;QACN;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,WAAW,CAAC,+DAA+D,EACzE,cAAc,cAAc,aAC5B;oBACF,OAAO;wBACL,YAAY,kBAAkB,SAC1B,oFACA;wBACJ,cAAc,kBAAkB,SAC5B,uCACA;wBACJ,WAAW,kBAAkB,SACzB,yEACA;oBACN;;sCAEA,6LAAC;4BAAI,WAAW,CAAC,kBAAkB,EAAE,cAAc,mBAAmB,QAAQ;;8CAC5E,6LAAC;oCACC,WAAW,CAAC,wEAAwE,EAClF,cAAc,cAAc,gBAC5B;oCACF,OAAO;wCACL,YAAY,kBAAkB,SAC1B,sDACA;wCACJ,WAAW;oCACb;8CAEA,cAAA,6LAAC;wCAAK,WAAW,CAAC,qBAAqB,EAAE,cAAc,cAAc,WAAW;kDAAE;;;;;;;;;;;gCAEnF,CAAC,6BACA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,OAAO,kBAAkB,SAAS,YAAY;wCAC9C,YAAY,kBAAkB,SAAS,iCAAiC;oCAC1E;8CACD;;;;;;;;;;;;wBAKJ,CAAC,6BACA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,OAAO,kBAAkB,SAAS,YAAY;gCAC9C,eAAe;4BACjB;sCACD;;;;;;;;;;;;8BAML,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAC/B,2BACA;gCACJ,QAAQ,kBAAkB,SACtB,qCACA;gCACJ,WAAW,kBAAkB,SACzB,qCACA;4BACN;4BACA,OAAO,cAAc,mBAAmB;sCAEvC,4BACC,6LAAC,yNAAA,CAAA,eAAY;gCACX,WAAU;gCACV,OAAO;oCAAE,OAAO,kBAAkB,SAAS,YAAY;gCAAU;;;;;qDAGnE,6LAAC,uNAAA,CAAA,cAAW;gCACV,WAAU;gCACV,OAAO;oCAAE,OAAO,kBAAkB,SAAS,YAAY;gCAAU;;;;;;;;;;;sCAKvE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,CAAC,2FAA2F,EAC1G,cAAc,SAAS,QACvB;0CACL,UAAU,GAAG,CAAC,CAAC;oCACd,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,kBAAkB,KAAK,EAAE;oCAE1C,qBACE,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC;gDACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;gDACvC,WAAW,CAAC,0HAA0H,EACpI,cACI,oCACA,kBACJ;gDACF,OAAO;oDACL,YAAY,WACP,kBAAkB,SACjB,sFACA,sFACF;oDACJ,QAAQ,WACH,kBAAkB,SAAS,qCAAqC,qCACjE;oDACJ,WAAW,WACN,kBAAkB,SACjB,8EACA,+EACF;gDACN;gDACA,cAAc,CAAC;oDACb,IAAI,CAAC,UAAU;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,kBAAkB,SACjD,4BACA;wDACJ,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,kBAAkB,SAC7C,uCACA;oDACN;gDACF;gDACA,cAAc,CAAC;oDACb,IAAI,CAAC,UAAU;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDACjC;gDACF;0DAEC,4BACC,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;wDAC5C,QAAQ,WAAW,8CAA8C;oDACnE;;;;;yEAGF;;sEACE,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,YAAY,WACP,kBAAkB,SACjB,sFACA,sFACD,kBAAkB,SACjB,mFACA;gEACN,WAAW,WACN,kBAAkB,SACjB,6EACA,6EACD,kBAAkB,SACjB,4CACA;4DACR;sEAEA,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;oEAC5C,QAAQ,WAAW,8CAA8C;gEACnE;;;;;;;;;;;sEAGJ,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;oEAC5C,YAAY,WAAW,QAAQ;gEACjC;0EAEC,KAAK,KAAK;;;;;;;;;;;;;;;;;;4CAOpB,6BACC,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDACxD,OAAO,kBAAkB,SAAS,YAAY;oDAC9C,QAAQ,kBAAkB,SAAS,uCAAuC;oDAC1E,WAAW,kBAAkB,SACzB,kCACA;gDACN;;kEAEA,6LAAC;wDAAI,WAAU;kEAAiB,KAAK,KAAK;;;;;;kEAC1C,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,WAAW;4DACX,cAAc;4DACd,aAAa,CAAC,UAAU,EAAE,kBAAkB,SAAS,YAAY,WAAW;wDAC9E;;;;;;;;;;;;;uCArHE,KAAK,EAAE;;;;;gCA2HrB;;;;;;;;;;;;;;;;;8BAMA,6LAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,cAAc,cAAc,aAC5B;oBACF,OAAO;wBACL,YAAY,kBAAkB,SAC1B,oFACA;wBACJ,WAAW,kBAAkB,SACzB,uCACA;wBACJ,WAAW,kBAAkB,SACzB,2EACA;oBACN;8BAEA,cAAA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,kBAAkB,SAAS,YAAY;wBAChD;kCAEC,4BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY;oCACZ,WAAW;gCACb;;kDAEA,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;kDAC/D,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,YAAY;wCACd;;;;;;;;;;;;;;;;iDAKN;;8CACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,YAAY;gDACZ,WAAW;4CACb;;8DAEA,6LAAC;oDAAK,WAAU;8DAA6C;;;;;;8DAC7D,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,YAAY;oDACd;;;;;;;;;;;;sDAGJ,6LAAC;;8DACC,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,kBAAkB,SAAS,YAAY;wDAC9C,YAAY,kBAAkB,SAAS,iCAAiC;oDAC1E;8DACD;;;;;;8DAGD,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,kBAAkB,SAAS,YAAY;wDAC9C,eAAe;oDACjB;8DACD;;;;;;;;;;;;;;;;;;8CAKL,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,kBAAkB,SAAS,2BAA2B;wCACvE,OAAO,kBAAkB,SAAS,YAAY;wCAC9C,QAAQ,kBAAkB,SAAS,uCAAuC;oCAC5E;8CACD;;;;;;;;;;;;;;;;;;gBASR,CAAC,6BACA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,OAAO,kBAAkB,SAAS,YAAY;oBAChD;8BAEA,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAsB;;;;;;0CACrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;wCAAgD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;kDAC/F,6LAAC;wCAAI,WAAU;wCAAgD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/G;GApZwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/DashboardStats.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  Package,\n  Users,\n  DollarSign,\n  AlertTriangle,\n  Activity,\n  Clock,\n  Plus,\n  BarChart3,\n  PieChart,\n  Bell,\n  RefreshCw,\n  ArrowUpRight,\n  ArrowDownRight,\n  Zap,\n  Target,\n  ShoppingCart,\n  CreditCard\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n\nimport type { DashboardStats } from '@/types'\n\ninterface DashboardStatsProps {\n  stats: DashboardStats\n  onSectionChange?: (section: string) => void\n}\n\ninterface PerformanceMetric {\n  label: string\n  value: number\n  previousValue: number\n  change: number\n  changeType: 'increase' | 'decrease' | 'neutral'\n  icon: React.ComponentType<{ className?: string; style?: React.CSSProperties }>\n  color: string\n  bgColor: string\n}\n\nexport default function DashboardStats({ stats, onSectionChange }: DashboardStatsProps) {\n  const { resolvedTheme } = useTheme()\n  const [isRefreshing, setIsRefreshing] = useState(false)\n  const [currentTime, setCurrentTime] = useState(new Date())\n  const [animatedStats, setAnimatedStats] = useState(stats)\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date())\n    }, 1000)\n    return () => clearInterval(timer)\n  }, [])\n\n  // Animate stats changes\n  useEffect(() => {\n    setAnimatedStats(stats)\n  }, [stats])\n\n  // Auto-refresh functionality\n  const handleRefresh = async () => {\n    setIsRefreshing(true)\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1000))\n    setIsRefreshing(false)\n  }\n\n  // Performance metrics with trend analysis\n  const performanceMetrics: PerformanceMetric[] = [\n    {\n      label: 'Products in List',\n      value: animatedStats.totalProducts,\n      previousValue: Math.max(0, animatedStats.totalProducts - Math.floor(Math.random() * 5)),\n      change: 12.5,\n      changeType: 'increase',\n      icon: Package,\n      color: '#3b82f6',\n      bgColor: resolvedTheme === 'dark' ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'\n    },\n    {\n      label: 'Customer Debts',\n      value: animatedStats.totalDebts,\n      previousValue: Math.max(0, animatedStats.totalDebts + Math.floor(Math.random() * 3)),\n      change: -8.3,\n      changeType: 'decrease',\n      icon: Users,\n      color: '#10b981',\n      bgColor: resolvedTheme === 'dark' ? 'rgba(16, 185, 129, 0.1)' : 'rgba(16, 185, 129, 0.05)'\n    },\n    {\n      label: 'Total Debt Amount',\n      value: animatedStats.totalDebtAmount,\n      previousValue: animatedStats.totalDebtAmount + Math.floor(Math.random() * 1000),\n      change: -15.7,\n      changeType: 'decrease',\n      icon: DollarSign,\n      color: '#f59e0b',\n      bgColor: resolvedTheme === 'dark' ? 'rgba(245, 158, 11, 0.1)' : 'rgba(245, 158, 11, 0.05)'\n    },\n    {\n      label: 'Low Stock Items',\n      value: animatedStats.lowStockItems,\n      previousValue: Math.max(0, animatedStats.lowStockItems + Math.floor(Math.random() * 2)),\n      change: -25.0,\n      changeType: 'decrease',\n      icon: AlertTriangle,\n      color: '#ef4444',\n      bgColor: resolvedTheme === 'dark' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(239, 68, 68, 0.05)'\n    }\n  ]\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP',\n      minimumFractionDigits: 2\n    }).format(amount)\n  }\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('en-PH', {\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    })\n  }\n\n  // Quick action handlers\n  const handleQuickAction = (action: string) => {\n    if (onSectionChange) {\n      switch (action) {\n        case 'add-product':\n          onSectionChange('products')\n          break\n        case 'record-debt':\n          onSectionChange('debts')\n          break\n        case 'view-analytics':\n          onSectionChange('api-graphing')\n          break\n        case 'manage-stock':\n          onSectionChange('products')\n          break\n        case 'view-history':\n          onSectionChange('history')\n          break\n        default:\n          break\n      }\n    }\n  }\n\n  return (\n    <div className=\"space-y-8 animate-fade-in\">\n      {/* Dashboard Header with Real-time Info */}\n      <div\n        className=\"rounded-2xl shadow-lg p-6 border transition-all duration-300\"\n        style={{\n          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',\n          background: resolvedTheme === 'dark'\n            ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'\n            : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'\n        }}\n      >\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2\n              className=\"text-2xl font-bold mb-2 flex items-center gap-3\"\n              style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}\n            >\n              <Activity className=\"h-7 w-7 text-green-500\" />\n              Dashboard Overview of your Revantad Store\n            </h2>\n            <p\n              className=\"text-sm flex items-center gap-2\"\n              style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}\n            >\n              <Clock className=\"h-4 w-4\" />\n              Last updated: {formatTime(currentTime)} • Real-time monitoring active\n            </p>\n          </div>\n          <button\n            onClick={handleRefresh}\n            disabled={isRefreshing}\n            className=\"flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.08)',\n              border: resolvedTheme === 'dark' ? '1px solid rgba(34, 197, 94, 0.3)' : '1px solid rgba(34, 197, 94, 0.2)',\n              color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a'\n            }}\n          >\n            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />\n            {isRefreshing ? 'Refreshing...' : 'Refresh'}\n          </button>\n        </div>\n      </div>\n\n      {/* Enhanced Performance Metrics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {performanceMetrics.map((metric, index) => {\n          const Icon = metric.icon\n          const TrendIcon = metric.changeType === 'increase' ? ArrowUpRight : ArrowDownRight\n          const isPositiveTrend = metric.changeType === 'increase'\n\n          return (\n            <div\n              key={index}\n              className=\"group rounded-2xl shadow-lg p-6 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] cursor-pointer border\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',\n                background: resolvedTheme === 'dark'\n                  ? `linear-gradient(135deg, #1e293b 0%, ${metric.bgColor} 100%)`\n                  : `linear-gradient(135deg, #ffffff 0%, ${metric.bgColor} 100%)`\n              }}\n            >\n              <div className=\"flex items-start justify-between mb-4\">\n                <div\n                  className=\"p-3 rounded-xl transition-all duration-300 group-hover:scale-110\"\n                  style={{\n                    backgroundColor: metric.bgColor,\n                    border: `1px solid ${metric.color}20`\n                  }}\n                >\n                  <Icon\n                    className=\"h-6 w-6 transition-all duration-300\"\n                    style={{ color: metric.color }}\n                  />\n                </div>\n                <div className=\"flex items-center gap-1\">\n                  <TrendIcon\n                    className={`h-4 w-4 ${isPositiveTrend ? 'text-green-500' : 'text-red-500'}`}\n                  />\n                  <span\n                    className={`text-sm font-semibold ${isPositiveTrend ? 'text-green-500' : 'text-red-500'}`}\n                  >\n                    {Math.abs(metric.change)}%\n                  </span>\n                </div>\n              </div>\n\n              <div>\n                <p\n                  className=\"text-sm font-medium mb-2 transition-colors duration-300\"\n                  style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}\n                >\n                  {metric.label}\n                </p>\n                <p\n                  className=\"text-3xl font-bold mb-1 transition-colors duration-300\"\n                  style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}\n                >\n                  {metric.label.includes('Amount') ? formatCurrency(metric.value) : metric.value.toLocaleString()}\n                </p>\n                <p\n                  className=\"text-xs transition-colors duration-300\"\n                  style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#9ca3af' }}\n                >\n                  vs {metric.label.includes('Amount') ? formatCurrency(metric.previousValue) : metric.previousValue.toLocaleString()} last period\n                </p>\n              </div>\n\n              {/* Mini Progress Bar */}\n              <div className=\"mt-4\">\n                <div\n                  className=\"h-1 rounded-full overflow-hidden\"\n                  style={{ backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f1f5f9' }}\n                >\n                  <div\n                    className=\"h-full rounded-full transition-all duration-1000 ease-out\"\n                    style={{\n                      backgroundColor: metric.color,\n                      width: `${Math.min(100, (metric.value / (metric.value + metric.previousValue)) * 100)}%`\n                    }}\n                  />\n                </div>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n\n      {/* Enhanced Quick Actions Grid */}\n      <div\n        className=\"rounded-2xl shadow-lg p-6 border transition-all duration-300\"\n        style={{\n          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',\n          background: resolvedTheme === 'dark'\n            ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'\n            : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'\n        }}\n      >\n        <div className=\"flex items-center gap-3 mb-6\">\n          <Zap className=\"h-6 w-6 text-yellow-500\" />\n          <h3\n            className=\"text-xl font-bold transition-colors duration-300\"\n            style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}\n          >\n            Quick Actions\n          </h3>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          {/* Add Product Action */}\n          <button\n            onClick={() => handleQuickAction('add-product')}\n            className=\"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n            style={{\n              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',\n              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',\n              background: resolvedTheme === 'dark'\n                ? 'linear-gradient(135deg, #334155 0%, #475569 100%)'\n                : 'linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)'\n            }}\n            title=\"Navigate to Products section to add new items\"\n          >\n            <div className=\"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110\"\n                 style={{ backgroundColor: 'rgba(59, 130, 246, 0.1)' }}>\n              <Plus className=\"h-6 w-6 text-blue-600\" />\n            </div>\n            <p className=\"font-semibold mb-1\" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>\n              Add Product\n            </p>\n            <p className=\"text-xs text-center\" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>\n              Add new item to inventory\n            </p>\n          </button>\n\n          {/* Record Debt Action */}\n          <button\n            onClick={() => handleQuickAction('record-debt')}\n            className=\"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2\"\n            style={{\n              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',\n              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',\n              background: resolvedTheme === 'dark'\n                ? 'linear-gradient(135deg, #334155 0%, #475569 100%)'\n                : 'linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)'\n            }}\n            title=\"Navigate to Debts section to record customer debt\"\n          >\n            <div className=\"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110\"\n                 style={{ backgroundColor: 'rgba(16, 185, 129, 0.1)' }}>\n              <CreditCard className=\"h-6 w-6 text-green-600\" />\n            </div>\n            <p className=\"font-semibold mb-1\" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>\n              Record Debt\n            </p>\n            <p className=\"text-xs text-center\" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>\n              Add customer debt record\n            </p>\n          </button>\n\n          {/* View Analytics Action */}\n          <button\n            onClick={() => handleQuickAction('view-analytics')}\n            className=\"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2\"\n            style={{\n              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',\n              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',\n              background: resolvedTheme === 'dark'\n                ? 'linear-gradient(135deg, #334155 0%, #475569 100%)'\n                : 'linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)'\n            }}\n            title=\"Navigate to API Graphing & Visuals for business analytics\"\n          >\n            <div className=\"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110\"\n                 style={{ backgroundColor: 'rgba(245, 158, 11, 0.1)' }}>\n              <BarChart3 className=\"h-6 w-6 text-yellow-600\" />\n            </div>\n            <p className=\"font-semibold mb-1\" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>\n              View Analytics\n            </p>\n            <p className=\"text-xs text-center\" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>\n              Business insights & reports\n            </p>\n          </button>\n\n          {/* Manage Inventory Action */}\n          <button\n            onClick={() => handleQuickAction('manage-stock')}\n            className=\"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2\"\n            style={{\n              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',\n              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',\n              background: resolvedTheme === 'dark'\n                ? 'linear-gradient(135deg, #334155 0%, #475569 100%)'\n                : 'linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)'\n            }}\n            title=\"Navigate to Products section to manage inventory levels\"\n          >\n            <div className=\"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110\"\n                 style={{ backgroundColor: 'rgba(139, 69, 19, 0.1)' }}>\n              <ShoppingCart className=\"h-6 w-6 text-amber-700\" />\n            </div>\n            <p className=\"font-semibold mb-1\" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>\n              Manage Stock\n            </p>\n            <p className=\"text-xs text-center\" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>\n              Update inventory levels\n            </p>\n          </button>\n        </div>\n      </div>\n\n      {/* Enhanced Store Overview & Recent Activities */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Store Overview */}\n        <div\n          className=\"rounded-2xl shadow-lg p-6 border transition-all duration-300\"\n          style={{\n            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n            border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',\n            background: resolvedTheme === 'dark'\n              ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'\n              : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'\n          }}\n        >\n          <div className=\"flex items-center gap-3 mb-6\">\n            <Target className=\"h-6 w-6 text-blue-500\" />\n            <h3\n              className=\"text-xl font-bold transition-colors duration-300\"\n              style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}\n            >\n              Store Overview\n            </h3>\n          </div>\n\n          <div className=\"space-y-4\">\n            {[\n              { label: 'Products in List', value: animatedStats.totalProducts, icon: Package, color: '#3b82f6' },\n              { label: 'Outstanding Debts', value: animatedStats.totalDebts, icon: Users, color: '#10b981' },\n              { label: 'Total Amount Owed', value: formatCurrency(animatedStats.totalDebtAmount), icon: DollarSign, color: '#f59e0b' },\n              { label: 'Items Need Restocking', value: animatedStats.lowStockItems, icon: AlertTriangle, color: animatedStats.lowStockItems > 0 ? '#ef4444' : '#10b981' }\n            ].map((item, index) => {\n              const Icon = item.icon\n              return (\n                <div\n                  key={index}\n                  className=\"flex items-center justify-between p-4 rounded-xl transition-all duration-300 hover:scale-[1.02] border\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',\n                    border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'\n                  }}\n                >\n                  <div className=\"flex items-center gap-3\">\n                    <div\n                      className=\"p-2 rounded-lg\"\n                      style={{ backgroundColor: `${item.color}20` }}\n                    >\n                      <Icon className=\"h-5 w-5\" style={{ color: item.color }} />\n                    </div>\n                    <span\n                      className=\"font-medium transition-colors duration-300\"\n                      style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}\n                    >\n                      {item.label}\n                    </span>\n                  </div>\n                  <span\n                    className=\"text-lg font-bold transition-colors duration-300\"\n                    style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}\n                  >\n                    {typeof item.value === 'string' ? item.value : item.value.toLocaleString()}\n                  </span>\n                </div>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Recent Activities */}\n        <div\n          className=\"rounded-2xl shadow-lg p-6 border transition-all duration-300\"\n          style={{\n            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n            border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',\n            background: resolvedTheme === 'dark'\n              ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'\n              : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'\n          }}\n        >\n          <div className=\"flex items-center justify-between mb-6\">\n            <div className=\"flex items-center gap-3\">\n              <Bell className=\"h-6 w-6 text-green-500\" />\n              <h3\n                className=\"text-xl font-bold transition-colors duration-300\"\n                style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}\n              >\n                Recent Activities\n              </h3>\n            </div>\n            <button\n              onClick={() => handleQuickAction('view-history')}\n              className=\"text-sm px-3 py-1 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.08)',\n                color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a'\n              }}\n              title=\"Navigate to History section for detailed activity logs\"\n            >\n              View All\n            </button>\n          </div>\n\n          <div className=\"space-y-3\">\n            {[\n              {\n                action: 'New product added',\n                item: 'Coca Cola 1.5L',\n                time: '2 minutes ago',\n                type: 'product',\n                icon: Package,\n                priority: 'normal',\n                clickable: true\n              },\n              {\n                action: 'Debt payment received',\n                item: 'Juan Dela Cruz - ₱500',\n                time: '15 minutes ago',\n                type: 'payment',\n                icon: DollarSign,\n                priority: 'high',\n                clickable: true\n              },\n              {\n                action: 'Low stock alert',\n                item: 'Rice 25kg - Only 3 left',\n                time: '1 hour ago',\n                type: 'alert',\n                icon: AlertTriangle,\n                priority: 'urgent',\n                clickable: true\n              },\n              {\n                action: 'New customer debt',\n                item: 'Maria Santos - ₱1,200',\n                time: '2 hours ago',\n                type: 'debt',\n                icon: Users,\n                priority: 'normal',\n                clickable: true\n              }\n            ].map((activity, index) => {\n              const Icon = activity.icon\n              const getActivityColor = (type: string) => {\n                switch (type) {\n                  case 'product': return '#3b82f6'\n                  case 'payment': return '#10b981'\n                  case 'alert': return '#ef4444'\n                  case 'debt': return '#f59e0b'\n                  default: return '#6b7280'\n                }\n              }\n\n              const getPriorityIndicator = (priority: string) => {\n                switch (priority) {\n                  case 'urgent': return { color: '#ef4444', pulse: true }\n                  case 'high': return { color: '#f59e0b', pulse: false }\n                  case 'normal': return { color: '#10b981', pulse: false }\n                  default: return { color: '#6b7280', pulse: false }\n                }\n              }\n\n              const priorityInfo = getPriorityIndicator(activity.priority)\n\n              return (\n                <button\n                  key={index}\n                  onClick={() => activity.clickable && handleQuickAction(activity.type === 'product' ? 'add-product' : activity.type === 'debt' ? 'record-debt' : 'view-history')}\n                  className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-300 hover:scale-[1.01] border text-left focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1 ${activity.clickable ? 'cursor-pointer' : 'cursor-default'}`}\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',\n                    border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'\n                  }}\n                  disabled={!activity.clickable}\n                  title={activity.clickable ? `Click to navigate to ${activity.type} section` : ''}\n                >\n                  <div className=\"relative\">\n                    <div\n                      className={`p-2 rounded-full flex-shrink-0 ${priorityInfo.pulse ? 'animate-pulse' : ''}`}\n                      style={{ backgroundColor: `${getActivityColor(activity.type)}20` }}\n                    >\n                      <Icon className=\"h-4 w-4\" style={{ color: getActivityColor(activity.type) }} />\n                    </div>\n                    {/* Priority indicator */}\n                    <div\n                      className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 ${priorityInfo.pulse ? 'animate-ping' : ''}`}\n                      style={{\n                        backgroundColor: priorityInfo.color,\n                        borderColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'\n                      }}\n                    />\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <p\n                      className=\"text-sm font-medium truncate\"\n                      style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}\n                    >\n                      {activity.action}\n                    </p>\n                    <p\n                      className=\"text-xs truncate\"\n                      style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}\n                    >\n                      {activity.item}\n                    </p>\n                  </div>\n                  <span\n                    className=\"text-xs flex-shrink-0\"\n                    style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#9ca3af' }}\n                  >\n                    {activity.time}\n                  </span>\n                </button>\n              )\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Performance Summary */}\n      <div\n        className=\"rounded-2xl shadow-lg p-6 border transition-all duration-300\"\n        style={{\n          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',\n          background: resolvedTheme === 'dark'\n            ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'\n            : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'\n        }}\n      >\n        <div className=\"flex items-center gap-3 mb-6\">\n          <PieChart className=\"h-6 w-6 text-purple-500\" />\n          <h3\n            className=\"text-xl font-bold transition-colors duration-300\"\n            style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}\n          >\n            Performance Summary\n          </h3>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"text-center\">\n            <div className=\"relative w-20 h-20 mx-auto mb-3\">\n              <svg className=\"w-20 h-20 transform -rotate-90\" viewBox=\"0 0 36 36\">\n                <path\n                  d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  fill=\"none\"\n                  stroke={resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'}\n                  strokeWidth=\"2\"\n                />\n                <path\n                  d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  fill=\"none\"\n                  stroke=\"#10b981\"\n                  strokeWidth=\"2\"\n                  strokeDasharray=\"85, 100\"\n                  className=\"animate-pulse\"\n                />\n              </svg>\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <span className=\"text-lg font-bold text-green-500\">85%</span>\n              </div>\n            </div>\n            <p className=\"text-sm font-medium\" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>\n              Store Health\n            </p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"relative w-20 h-20 mx-auto mb-3\">\n              <svg className=\"w-20 h-20 transform -rotate-90\" viewBox=\"0 0 36 36\">\n                <path\n                  d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  fill=\"none\"\n                  stroke={resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'}\n                  strokeWidth=\"2\"\n                />\n                <path\n                  d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  fill=\"none\"\n                  stroke=\"#3b82f6\"\n                  strokeWidth=\"2\"\n                  strokeDasharray=\"72, 100\"\n                  className=\"animate-pulse\"\n                />\n              </svg>\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <span className=\"text-lg font-bold text-blue-500\">72%</span>\n              </div>\n            </div>\n            <p className=\"text-sm font-medium\" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>\n              Inventory Level\n            </p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"relative w-20 h-20 mx-auto mb-3\">\n              <svg className=\"w-20 h-20 transform -rotate-90\" viewBox=\"0 0 36 36\">\n                <path\n                  d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  fill=\"none\"\n                  stroke={resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'}\n                  strokeWidth=\"2\"\n                />\n                <path\n                  d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                  fill=\"none\"\n                  stroke=\"#f59e0b\"\n                  strokeWidth=\"2\"\n                  strokeDasharray=\"58, 100\"\n                  className=\"animate-pulse\"\n                />\n              </svg>\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <span className=\"text-lg font-bold text-yellow-500\">58%</span>\n              </div>\n            </div>\n            <p className=\"text-sm font-medium\" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>\n              Debt Recovery\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AACA;;;AAtBA;;;;AA0Ce,SAAS,eAAe,EAAE,KAAK,EAAE,eAAe,EAAuB;;IACpF,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAY;oBACxB,eAAe,IAAI;gBACrB;iDAAG;YACH;4CAAO,IAAM,cAAc;;QAC7B;mCAAG,EAAE;IAEL,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,iBAAiB;QACnB;mCAAG;QAAC;KAAM;IAEV,6BAA6B;IAC7B,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,0BAA0B;QAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,gBAAgB;IAClB;IAEA,0CAA0C;IAC1C,MAAM,qBAA0C;QAC9C;YACE,OAAO;YACP,OAAO,cAAc,aAAa;YAClC,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc,aAAa,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YACpF,QAAQ;YACR,YAAY;YACZ,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS,kBAAkB,SAAS,4BAA4B;QAClE;QACA;YACE,OAAO;YACP,OAAO,cAAc,UAAU;YAC/B,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc,UAAU,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YACjF,QAAQ,CAAC;YACT,YAAY;YACZ,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS,kBAAkB,SAAS,4BAA4B;QAClE;QACA;YACE,OAAO;YACP,OAAO,cAAc,eAAe;YACpC,eAAe,cAAc,eAAe,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YAC1E,QAAQ,CAAC;YACT,YAAY;YACZ,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS,kBAAkB,SAAS,4BAA4B;QAClE;QACA;YACE,OAAO;YACP,OAAO,cAAc,aAAa;YAClC,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc,aAAa,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YACpF,QAAQ,CAAC;YACT,YAAY;YACZ,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,SAAS,kBAAkB,SAAS,2BAA2B;QACjE;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,wBAAwB;IACxB,MAAM,oBAAoB,CAAC;QACzB,IAAI,iBAAiB;YACnB,OAAQ;gBACN,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF;oBACE;YACJ;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oBACzD,YAAY,kBAAkB,SAC1B,sDACA;gBACN;0BAEA,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,kBAAkB,SAAS,YAAY;oCAAU;;sDAEjE,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAA2B;;;;;;;8CAGjD,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,kBAAkB,SAAS,YAAY;oCAAU;;sDAEjE,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;wCACd,WAAW;wCAAa;;;;;;;;;;;;;sCAG3C,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAAS,2BAA2B;gCACvE,QAAQ,kBAAkB,SAAS,qCAAqC;gCACxE,OAAO,kBAAkB,SAAS,YAAY;4BAChD;;8CAEA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAW,CAAC,QAAQ,EAAE,eAAe,iBAAiB,IAAI;;;;;;gCACpE,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;0BAMxC,6LAAC;gBAAI,WAAU;0BACZ,mBAAmB,GAAG,CAAC,CAAC,QAAQ;oBAC/B,MAAM,OAAO,OAAO,IAAI;oBACxB,MAAM,YAAY,OAAO,UAAU,KAAK,aAAa,6NAAA,CAAA,eAAY,GAAG,iOAAA,CAAA,iBAAc;oBAClF,MAAM,kBAAkB,OAAO,UAAU,KAAK;oBAE9C,qBACE,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,YAAY;4BACxD,QAAQ,kBAAkB,SAAS,sBAAsB;4BACzD,YAAY,kBAAkB,SAC1B,CAAC,oCAAoC,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,GAC7D,CAAC,oCAAoC,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC;wBACnE;;0CAEA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB,OAAO,OAAO;4CAC/B,QAAQ,CAAC,UAAU,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC;wCACvC;kDAEA,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,OAAO,KAAK;4CAAC;;;;;;;;;;;kDAGjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,QAAQ,EAAE,kBAAkB,mBAAmB,gBAAgB;;;;;;0DAE7E,6LAAC;gDACC,WAAW,CAAC,sBAAsB,EAAE,kBAAkB,mBAAmB,gBAAgB;;oDAExF,KAAK,GAAG,CAAC,OAAO,MAAM;oDAAE;;;;;;;;;;;;;;;;;;;0CAK/B,6LAAC;;kDACC,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAEhE,OAAO,KAAK;;;;;;kDAEf,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAEhE,OAAO,KAAK,CAAC,QAAQ,CAAC,YAAY,eAAe,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,cAAc;;;;;;kDAE/F,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;;4CAClE;4CACK,OAAO,KAAK,CAAC,QAAQ,CAAC,YAAY,eAAe,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,cAAc;4CAAG;;;;;;;;;;;;;0CAKvH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB,kBAAkB,SAAS,YAAY;oCAAU;8CAE3E,cAAA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB,OAAO,KAAK;4CAC7B,OAAO,GAAG,KAAK,GAAG,CAAC,KAAK,AAAC,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,aAAa,IAAK,KAAK,CAAC,CAAC;wCAC1F;;;;;;;;;;;;;;;;;uBAnED;;;;;gBAyEX;;;;;;0BAIF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oBACzD,YAAY,kBAAkB,SAC1B,sDACA;gBACN;;kCAEA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,kBAAkB,SAAS,YAAY;gCAAU;0CAClE;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;gCACV,OAAO;oCACL,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,iBAAiB,kBAAkB,SAAS,YAAY;oCACxD,YAAY,kBAAkB,SAC1B,sDACA;gCACN;gCACA,OAAM;;kDAEN,6LAAC;wCAAI,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAA0B;kDACvD,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAE,WAAU;wCAAqB,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAAG;;;;;;kDAGtG,6LAAC;wCAAE,WAAU;wCAAsB,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAAG;;;;;;;;;;;;0CAMzG,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;gCACV,OAAO;oCACL,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,iBAAiB,kBAAkB,SAAS,YAAY;oCACxD,YAAY,kBAAkB,SAC1B,sDACA;gCACN;gCACA,OAAM;;kDAEN,6LAAC;wCAAI,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAA0B;kDACvD,cAAA,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6LAAC;wCAAE,WAAU;wCAAqB,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAAG;;;;;;kDAGtG,6LAAC;wCAAE,WAAU;wCAAsB,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAAG;;;;;;;;;;;;0CAMzG,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;gCACV,OAAO;oCACL,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,iBAAiB,kBAAkB,SAAS,YAAY;oCACxD,YAAY,kBAAkB,SAC1B,sDACA;gCACN;gCACA,OAAM;;kDAEN,6LAAC;wCAAI,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAA0B;kDACvD,cAAA,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAE,WAAU;wCAAqB,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAAG;;;;;;kDAGtG,6LAAC;wCAAE,WAAU;wCAAsB,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAAG;;;;;;;;;;;;0CAMzG,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;gCACV,OAAO;oCACL,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,iBAAiB,kBAAkB,SAAS,YAAY;oCACxD,YAAY,kBAAkB,SAC1B,sDACA;gCACN;gCACA,OAAM;;kDAEN,6LAAC;wCAAI,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAyB;kDACtD,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,6LAAC;wCAAE,WAAU;wCAAqB,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAAG;;;;;;kDAGtG,6LAAC;wCAAE,WAAU;wCAAsB,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7G,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,YAAY;4BACxD,QAAQ,kBAAkB,SAAS,sBAAsB;4BACzD,YAAY,kBAAkB,SAC1B,sDACA;wBACN;;0CAEA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAClE;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,OAAO;wCAAoB,OAAO,cAAc,aAAa;wCAAE,MAAM,2MAAA,CAAA,UAAO;wCAAE,OAAO;oCAAU;oCACjG;wCAAE,OAAO;wCAAqB,OAAO,cAAc,UAAU;wCAAE,MAAM,uMAAA,CAAA,QAAK;wCAAE,OAAO;oCAAU;oCAC7F;wCAAE,OAAO;wCAAqB,OAAO,eAAe,cAAc,eAAe;wCAAG,MAAM,qNAAA,CAAA,aAAU;wCAAE,OAAO;oCAAU;oCACvH;wCAAE,OAAO;wCAAyB,OAAO,cAAc,aAAa;wCAAE,MAAM,2NAAA,CAAA,gBAAa;wCAAE,OAAO,cAAc,aAAa,GAAG,IAAI,YAAY;oCAAU;iCAC3J,CAAC,GAAG,CAAC,CAAC,MAAM;oCACX,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,6LAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,iBAAiB,kBAAkB,SAAS,YAAY;4CACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wCAC3D;;0DAEA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC;wDAAC;kEAE5C,cAAA,6LAAC;4DAAK,WAAU;4DAAU,OAAO;gEAAE,OAAO,KAAK,KAAK;4DAAC;;;;;;;;;;;kEAEvD,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,kBAAkB,SAAS,YAAY;wDAAU;kEAEhE,KAAK,KAAK;;;;;;;;;;;;0DAGf,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,kBAAkB,SAAS,YAAY;gDAAU;0DAEhE,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,cAAc;;;;;;;uCAzBrE;;;;;gCA6BX;;;;;;;;;;;;kCAKJ,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,YAAY;4BACxD,QAAQ,kBAAkB,SAAS,sBAAsB;4BACzD,YAAY,kBAAkB,SAC1B,sDACA;wBACN;;0CAEA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,kBAAkB,SAAS,YAAY;gDAAU;0DAClE;;;;;;;;;;;;kDAIH,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;wCACV,OAAO;4CACL,iBAAiB,kBAAkB,SAAS,2BAA2B;4CACvE,OAAO,kBAAkB,SAAS,YAAY;wCAChD;wCACA,OAAM;kDACP;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,QAAQ;wCACR,MAAM;wCACN,MAAM;wCACN,MAAM;wCACN,MAAM,2MAAA,CAAA,UAAO;wCACb,UAAU;wCACV,WAAW;oCACb;oCACA;wCACE,QAAQ;wCACR,MAAM;wCACN,MAAM;wCACN,MAAM;wCACN,MAAM,qNAAA,CAAA,aAAU;wCAChB,UAAU;wCACV,WAAW;oCACb;oCACA;wCACE,QAAQ;wCACR,MAAM;wCACN,MAAM;wCACN,MAAM;wCACN,MAAM,2NAAA,CAAA,gBAAa;wCACnB,UAAU;wCACV,WAAW;oCACb;oCACA;wCACE,QAAQ;wCACR,MAAM;wCACN,MAAM;wCACN,MAAM;wCACN,MAAM,uMAAA,CAAA,QAAK;wCACX,UAAU;wCACV,WAAW;oCACb;iCACD,CAAC,GAAG,CAAC,CAAC,UAAU;oCACf,MAAM,OAAO,SAAS,IAAI;oCAC1B,MAAM,mBAAmB,CAAC;wCACxB,OAAQ;4CACN,KAAK;gDAAW,OAAO;4CACvB,KAAK;gDAAW,OAAO;4CACvB,KAAK;gDAAS,OAAO;4CACrB,KAAK;gDAAQ,OAAO;4CACpB;gDAAS,OAAO;wCAClB;oCACF;oCAEA,MAAM,uBAAuB,CAAC;wCAC5B,OAAQ;4CACN,KAAK;gDAAU,OAAO;oDAAE,OAAO;oDAAW,OAAO;gDAAK;4CACtD,KAAK;gDAAQ,OAAO;oDAAE,OAAO;oDAAW,OAAO;gDAAM;4CACrD,KAAK;gDAAU,OAAO;oDAAE,OAAO;oDAAW,OAAO;gDAAM;4CACvD;gDAAS,OAAO;oDAAE,OAAO;oDAAW,OAAO;gDAAM;wCACnD;oCACF;oCAEA,MAAM,eAAe,qBAAqB,SAAS,QAAQ;oCAE3D,qBACE,6LAAC;wCAEC,SAAS,IAAM,SAAS,SAAS,IAAI,kBAAkB,SAAS,IAAI,KAAK,YAAY,gBAAgB,SAAS,IAAI,KAAK,SAAS,gBAAgB;wCAChJ,WAAW,CAAC,uLAAuL,EAAE,SAAS,SAAS,GAAG,mBAAmB,kBAAkB;wCAC/P,OAAO;4CACL,iBAAiB,kBAAkB,SAAS,YAAY;4CACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wCAC3D;wCACA,UAAU,CAAC,SAAS,SAAS;wCAC7B,OAAO,SAAS,SAAS,GAAG,CAAC,qBAAqB,EAAE,SAAS,IAAI,CAAC,QAAQ,CAAC,GAAG;;0DAE9E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAW,CAAC,+BAA+B,EAAE,aAAa,KAAK,GAAG,kBAAkB,IAAI;wDACxF,OAAO;4DAAE,iBAAiB,GAAG,iBAAiB,SAAS,IAAI,EAAE,EAAE,CAAC;wDAAC;kEAEjE,cAAA,6LAAC;4DAAK,WAAU;4DAAU,OAAO;gEAAE,OAAO,iBAAiB,SAAS,IAAI;4DAAE;;;;;;;;;;;kEAG5E,6LAAC;wDACC,WAAW,CAAC,uDAAuD,EAAE,aAAa,KAAK,GAAG,iBAAiB,IAAI;wDAC/G,OAAO;4DACL,iBAAiB,aAAa,KAAK;4DACnC,aAAa,kBAAkB,SAAS,YAAY;wDACtD;;;;;;;;;;;;0DAGJ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,kBAAkB,SAAS,YAAY;wDAAU;kEAEhE,SAAS,MAAM;;;;;;kEAElB,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,kBAAkB,SAAS,YAAY;wDAAU;kEAEhE,SAAS,IAAI;;;;;;;;;;;;0DAGlB,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,kBAAkB,SAAS,YAAY;gDAAU;0DAEhE,SAAS,IAAI;;;;;;;uCA5CX;;;;;gCAgDX;;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oBACzD,YAAY,kBAAkB,SAC1B,sDACA;gBACN;;kCAEA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,kBAAkB,SAAS,YAAY;gCAAU;0CAClE;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;gDAAiC,SAAQ;;kEACtD,6LAAC;wDACC,GAAE;wDACF,MAAK;wDACL,QAAQ,kBAAkB,SAAS,YAAY;wDAC/C,aAAY;;;;;;kEAEd,6LAAC;wDACC,GAAE;wDACF,MAAK;wDACL,QAAO;wDACP,aAAY;wDACZ,iBAAgB;wDAChB,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;;;;;;kDAGvD,6LAAC;wCAAE,WAAU;wCAAsB,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAAG;;;;;;;;;;;;0CAKzG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;gDAAiC,SAAQ;;kEACtD,6LAAC;wDACC,GAAE;wDACF,MAAK;wDACL,QAAQ,kBAAkB,SAAS,YAAY;wDAC/C,aAAY;;;;;;kEAEd,6LAAC;wDACC,GAAE;wDACF,MAAK;wDACL,QAAO;wDACP,aAAY;wDACZ,iBAAgB;wDAChB,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;wCAAsB,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAAG;;;;;;;;;;;;0CAKzG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;gDAAiC,SAAQ;;kEACtD,6LAAC;wDACC,GAAE;wDACF,MAAK;wDACL,QAAQ,kBAAkB,SAAS,YAAY;wDAC/C,aAAY;;;;;;kEAEd,6LAAC;wDACC,GAAE;wDACF,MAAK;wDACL,QAAO;wDACP,aAAY;wDACZ,iBAAgB;wDAChB,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;;;;;;;kDAGxD,6LAAC;wCAAE,WAAU;wCAAsB,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnH;GAjrBwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 2418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/ProductModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Upload, Package } from 'lucide-react'\nimport { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\n\ninterface ProductModalProps {\n  isOpen: boolean\n  onClose: () => void\n  product?: Product | null\n}\n\nexport default function ProductModal({ isOpen, onClose, product }: ProductModalProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    net_weight: '',\n    price: '',\n    stock_quantity: '',\n    category: '',\n    image_url: ''\n  })\n  const [imageFile, setImageFile] = useState<File | null>(null)\n  const [imagePreview, setImagePreview] = useState<string>('')\n  const [loading, setLoading] = useState(false)\n  const [uploading, setUploading] = useState(false)\n\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name,\n        net_weight: product.net_weight,\n        price: product.price.toString(),\n        stock_quantity: product.stock_quantity.toString(),\n        category: product.category,\n        image_url: product.image_url || ''\n      })\n      setImagePreview(product.image_url || '')\n    } else {\n      setFormData({\n        name: '',\n        net_weight: '',\n        price: '',\n        stock_quantity: '',\n        category: '',\n        image_url: ''\n      })\n      setImagePreview('')\n    }\n    setImageFile(null)\n  }, [product, isOpen])\n\n  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0]\n    if (file) {\n      setImageFile(file)\n      const reader = new FileReader()\n      reader.onloadend = () => {\n        setImagePreview(reader.result as string)\n      }\n      reader.readAsDataURL(file)\n    }\n  }\n\n  const uploadImage = async (): Promise<string> => {\n    if (!imageFile) return formData.image_url\n\n    setUploading(true)\n    try {\n      const uploadFormData = new FormData()\n      uploadFormData.append('file', imageFile)\n\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: uploadFormData,\n      })\n\n      const data = await response.json()\n      return data.url\n    } catch (error) {\n      console.error('Error uploading image:', error)\n      return formData.image_url\n    } finally {\n      setUploading(false)\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      // Upload image if there's a new one\n      const imageUrl = await uploadImage()\n\n      const productData = {\n        ...formData,\n        image_url: imageUrl,\n        price: parseFloat(formData.price),\n        stock_quantity: parseInt(formData.stock_quantity)\n      }\n\n      const url = product ? `/api/products/${product.id}` : '/api/products'\n      const method = product ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(productData),\n      })\n\n      if (response.ok) {\n        onClose()\n      } else {\n        console.error('Error saving product')\n      }\n    } catch (error) {\n      console.error('Error saving product:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-semibold\">\n            {product ? 'Edit Product in List' : 'Add Product to List'}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Image Upload */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Product Image\n            </label>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-2\">\n                {imagePreview ? (\n                  <img\n                    src={imagePreview}\n                    alt=\"Preview\"\n                    className=\"w-full h-full object-cover rounded-lg\"\n                  />\n                ) : (\n                  <Package className=\"h-12 w-12 text-gray-400\" />\n                )}\n              </div>\n              <input\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleImageChange}\n                className=\"hidden\"\n                id=\"image-upload\"\n              />\n              <label\n                htmlFor=\"image-upload\"\n                className=\"flex items-center px-3 py-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50\"\n              >\n                <Upload className=\"h-4 w-4 mr-2\" />\n                Choose Image\n              </label>\n            </div>\n          </div>\n\n          {/* Product Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.name}\n              onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Net Weight */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Net Weight *\n            </label>\n            <input\n              type=\"text\"\n              required\n              placeholder=\"e.g., 100g, 1L, 250ml\"\n              value={formData.net_weight}\n              onChange={(e) => setFormData({ ...formData, net_weight: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Price */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Price (₱) *\n            </label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              required\n              value={formData.price}\n              onChange={(e) => setFormData({ ...formData, price: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Stock Quantity */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Stock Quantity *\n            </label>\n            <input\n              type=\"number\"\n              min=\"0\"\n              required\n              value={formData.stock_quantity}\n              onChange={(e) => setFormData({ ...formData, stock_quantity: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Category */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Category *\n            </label>\n            <select\n              required\n              value={formData.category}\n              onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">Select Category</option>\n              {PRODUCT_CATEGORIES.map(category => (\n                <option key={category} value={category}>{category}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading || uploading}\n              className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n            >\n              {loading || uploading ? 'Saving...' : (product ? 'Update in List' : 'Add to List')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAYe,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAqB;;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,YAAY;QACZ,OAAO;QACP,gBAAgB;QAChB,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS;gBACX,YAAY;oBACV,MAAM,QAAQ,IAAI;oBAClB,YAAY,QAAQ,UAAU;oBAC9B,OAAO,QAAQ,KAAK,CAAC,QAAQ;oBAC7B,gBAAgB,QAAQ,cAAc,CAAC,QAAQ;oBAC/C,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS,IAAI;gBAClC;gBACA,gBAAgB,QAAQ,SAAS,IAAI;YACvC,OAAO;gBACL,YAAY;oBACV,MAAM;oBACN,YAAY;oBACZ,OAAO;oBACP,gBAAgB;oBAChB,UAAU;oBACV,WAAW;gBACb;gBACA,gBAAgB;YAClB;YACA,aAAa;QACf;iCAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,aAAa;YACb,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,gBAAgB,OAAO,MAAM;YAC/B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,OAAO,SAAS,SAAS;QAEzC,aAAa;QACb,IAAI;YACF,MAAM,iBAAiB,IAAI;YAC3B,eAAe,MAAM,CAAC,QAAQ;YAE9B,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,GAAG;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO,SAAS,SAAS;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,oCAAoC;YACpC,MAAM,WAAW,MAAM;YAEvB,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,WAAW;gBACX,OAAO,WAAW,SAAS,KAAK;gBAChC,gBAAgB,SAAS,SAAS,cAAc;YAClD;YAEA,MAAM,MAAM,UAAU,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE,GAAG;YACtD,MAAM,SAAS,UAAU,QAAQ;YAEjC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,UAAU,yBAAyB;;;;;;sCAEtC,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,6BACC,6LAAC;gDACC,KAAK;gDACL,KAAI;gDACJ,WAAU;;;;;qEAGZ,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAGvB,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;4CACV,IAAG;;;;;;sDAEL,6LAAC;4CACC,SAAQ;4CACR,WAAU;;8DAEV,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,aAAY;oCACZ,OAAO,SAAS,UAAU;oCAC1B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACvE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAClE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,cAAc;oCAC9B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC3E,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,QAAQ;oCACR,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrE,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,yHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,yBACtB,6LAAC;gDAAsB,OAAO;0DAAW;+CAA5B;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU,WAAW;oCACrB,WAAU;8CAET,WAAW,YAAY,cAAe,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlF;GAvQwB;KAAA", "debugId": null}}, {"offset": {"line": 2887, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/ProductsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Package } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport ProductModal from './ProductModal'\nimport { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\n\ninterface ProductsSectionProps {\n  onStatsUpdate: () => void\n}\n\nexport default function ProductsSection({ onStatsUpdate }: ProductsSectionProps) {\n  const { resolvedTheme } = useTheme()\n  const [products, setProducts] = useState<Product[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('')\n  const [isModalOpen, setIsModalOpen] = useState(false)\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null)\n\n  useEffect(() => {\n    fetchProducts()\n  }, [])\n\n  const fetchProducts = async () => {\n    try {\n      const response = await fetch('/api/products')\n      const data = await response.json()\n      setProducts(data.products || [])\n    } catch (error) {\n      console.error('Error fetching products:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this product?')) return\n\n    try {\n      const response = await fetch(`/api/products/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setProducts(products.filter(p => p.id !== id))\n        onStatsUpdate()\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error)\n    }\n  }\n\n  const handleEdit = (product: Product) => {\n    setEditingProduct(product)\n    setIsModalOpen(true)\n  }\n\n  const handleModalClose = () => {\n    setIsModalOpen(false)\n    setEditingProduct(null)\n    fetchProducts()\n    onStatsUpdate()\n  }\n\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesCategory = selectedCategory === '' || product.category === selectedCategory\n    return matchesSearch && matchesCategory\n  })\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div className=\"flex space-x-4\">\n          <div className=\"relative\">\n            <Search\n              className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n              }}\n            />\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n              }}\n            />\n          </div>\n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"px-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n            }}\n          >\n            <option value=\"\">All Categories</option>\n            {PRODUCT_CATEGORIES.map(category => (\n              <option key={category} value={category}>{category}</option>\n            ))}\n          </select>\n        </div>\n        <button\n          onClick={() => setIsModalOpen(true)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add to Product List\n        </button>\n      </div>\n\n      {/* Products Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {filteredProducts.map((product) => (\n          <div\n            key={product.id}\n            className=\"rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02]\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n              border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n            }}\n          >\n            <div\n              className=\"aspect-square flex items-center justify-center transition-colors duration-300\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'\n              }}\n            >\n              {product.image_url ? (\n                <img\n                  src={product.image_url}\n                  alt={product.name}\n                  className=\"w-full h-full object-cover\"\n                />\n              ) : (\n                <Package\n                  className=\"h-16 w-16 transition-colors duration-300\"\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                  }}\n                />\n              )}\n            </div>\n            <div className=\"p-4\">\n              <h3\n                className=\"font-semibold mb-1 transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                }}\n              >\n                {product.name}\n              </h3>\n              <p\n                className=\"text-sm mb-2 transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                }}\n              >\n                {product.category}\n              </p>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-lg font-bold text-green-600\">₱{product.price}</span>\n                <span\n                  className=\"text-sm transition-colors duration-300\"\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                  }}\n                >\n                  {product.net_weight}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center mb-4\">\n                <span\n                  className={`text-sm ${product.stock_quantity < 10 ? 'text-red-600' : ''}`}\n                  style={{\n                    color: product.stock_quantity >= 10\n                      ? (resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280')\n                      : '#dc2626'\n                  }}\n                >\n                  Stock: {product.stock_quantity}\n                </span>\n                {product.stock_quantity < 10 && (\n                  <span className=\"text-xs bg-red-100 text-red-800 px-2 py-1 rounded\">Low Stock</span>\n                )}\n              </div>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => handleEdit(product)}\n                  className=\"flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors\"\n                >\n                  <Edit className=\"h-4 w-4 mr-1\" />\n                  Edit\n                </button>\n                <button\n                  onClick={() => handleDelete(product.id)}\n                  className=\"flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors\"\n                >\n                  <Trash2 className=\"h-4 w-4 mr-1\" />\n                  Delete\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {filteredProducts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Package className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No products in list</h3>\n          <p className=\"text-gray-600\">\n            {searchTerm || selectedCategory\n              ? 'Try adjusting your search or filter criteria'\n              : 'Get started by adding your first product to the list'}\n          </p>\n        </div>\n      )}\n\n      {/* Product Modal */}\n      <ProductModal\n        isOpen={isModalOpen}\n        onClose={handleModalClose}\n        product={editingProduct}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAYe,SAAS,gBAAgB,EAAE,aAAa,EAAwB;;IAC7E,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY,KAAK,QAAQ,IAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,kDAAkD;QAE/D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;gBAClD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC1C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,kBAAkB;QAClB;QACA;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAChF,MAAM,kBAAkB,qBAAqB,MAAM,QAAQ,QAAQ,KAAK;QACxE,OAAO,iBAAiB;IAC1B;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCACL,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;;;;;;kDAEF,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;wCACV,OAAO;4CACL,iBAAiB,kBAAkB,SAAS,YAAY;4CACxD,QAAQ,kBAAkB,SAAS,sBAAsB;4CACzD,OAAO,kBAAkB,SAAS,YAAY;wCAChD;;;;;;;;;;;;0CAGJ,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,YAAY;oCACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,OAAO,kBAAkB,SAAS,YAAY;gCAChD;;kDAEA,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,yHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,yBACtB,6LAAC;4CAAsB,OAAO;sDAAW;2CAA5B;;;;;;;;;;;;;;;;;kCAInB,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,YAAY;4BACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wBAC3D;;0CAEA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,YAAY;gCAC1D;0CAEC,QAAQ,SAAS,iBAChB,6LAAC;oCACC,KAAK,QAAQ,SAAS;oCACtB,KAAK,QAAQ,IAAI;oCACjB,WAAU;;;;;yDAGZ,6LAAC,2MAAA,CAAA,UAAO;oCACN,WAAU;oCACV,OAAO;wCACL,OAAO,kBAAkB,SAAS,YAAY;oCAChD;;;;;;;;;;;0CAIN,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDAEC,QAAQ,IAAI;;;;;;kDAEf,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDAEC,QAAQ,QAAQ;;;;;;kDAEnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;oDAAmC;oDAAE,QAAQ,KAAK;;;;;;;0DAClE,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DAEC,QAAQ,UAAU;;;;;;;;;;;;kDAGvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,QAAQ,EAAE,QAAQ,cAAc,GAAG,KAAK,iBAAiB,IAAI;gDACzE,OAAO;oDACL,OAAO,QAAQ,cAAc,IAAI,KAC5B,kBAAkB,SAAS,YAAY,YACxC;gDACN;;oDACD;oDACS,QAAQ,cAAc;;;;;;;4CAE/B,QAAQ,cAAc,GAAG,oBACxB,6LAAC;gDAAK,WAAU;0DAAoD;;;;;;;;;;;;kDAGxE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,WAAW;gDAC1B,WAAU;;kEAEV,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC;gDACC,SAAS,IAAM,aAAa,QAAQ,EAAE;gDACtC,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;uBAnFpC,QAAQ,EAAE;;;;;;;;;;YA4FpB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,mBACX,iDACA;;;;;;;;;;;;0BAMV,6LAAC,qIAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,SAAS;gBACT,SAAS;;;;;;;;;;;;AAIjB;GA1OwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 3335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/DebtModal.tsx"], "sourcesContent": ["'use client'\n\nimport { X } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n\nimport { CustomerDebt } from '@/lib/supabase'\n\ninterface DebtModalProps {\n  isOpen: boolean\n  onClose: () => void\n  debt?: CustomerDebt | null\n}\n\nexport default function DebtModal({ isOpen, onClose, debt }: DebtModalProps) {\n  const [formData, setFormData] = useState({\n    customer_name: '',\n    customer_family_name: '',\n    product_name: '',\n    product_price: '',\n    quantity: '',\n    debt_date: ''\n  })\n  const [loading, setLoading] = useState(false)\n\n  useEffect(() => {\n    if (debt) {\n      setFormData({\n        customer_name: debt.customer_name,\n        customer_family_name: debt.customer_family_name,\n        product_name: debt.product_name,\n        product_price: debt.product_price.toString(),\n        quantity: debt.quantity.toString(),\n        debt_date: debt.debt_date\n      })\n    } else {\n      setFormData({\n        customer_name: '',\n        customer_family_name: '',\n        product_name: '',\n        product_price: '',\n        quantity: '',\n        debt_date: new Date().toISOString().split('T')[0] || ''\n      })\n    }\n  }, [debt, isOpen])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      const debtData = {\n        ...formData,\n        product_price: parseFloat(formData.product_price),\n        quantity: parseInt(formData.quantity)\n      }\n\n      const url = debt ? `/api/debts/${debt.id}` : '/api/debts'\n      const method = debt ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(debtData),\n      })\n\n      if (response.ok) {\n        onClose()\n      } else {\n        console.error('Error saving debt record')\n      }\n    } catch (error) {\n      console.error('Error saving debt record:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!isOpen) return null\n\n  const totalAmount = formData.product_price && formData.quantity \n    ? (parseFloat(formData.product_price) * parseInt(formData.quantity)).toFixed(2)\n    : '0.00'\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-semibold\">\n            {debt ? 'Edit Debt Record' : 'Add New Debt Record'}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Customer Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Customer First Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.customer_name}\n              onChange={(e) => setFormData({ ...formData, customer_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Juan\"\n            />\n          </div>\n\n          {/* Customer Family Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Customer Family Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.customer_family_name}\n              onChange={(e) => setFormData({ ...formData, customer_family_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Dela Cruz\"\n            />\n          </div>\n\n          {/* Product Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.product_name}\n              onChange={(e) => setFormData({ ...formData, product_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Lucky Me Pancit Canton\"\n            />\n          </div>\n\n          {/* Product Price */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Price (₱) *\n            </label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              required\n              value={formData.product_price}\n              onChange={(e) => setFormData({ ...formData, product_price: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"0.00\"\n            />\n          </div>\n\n          {/* Quantity */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Quantity *\n            </label>\n            <input\n              type=\"number\"\n              min=\"1\"\n              required\n              value={formData.quantity}\n              onChange={(e) => setFormData({ ...formData, quantity: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"1\"\n            />\n          </div>\n\n          {/* Debt Date */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Debt Date *\n            </label>\n            <input\n              type=\"date\"\n              required\n              value={formData.debt_date}\n              onChange={(e) => setFormData({ ...formData, debt_date: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Total Amount Display */}\n          <div className=\"bg-gray-50 p-3 rounded-md\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm font-medium text-gray-700\">Total Amount:</span>\n              <span className=\"text-lg font-bold text-green-600\">₱{totalAmount}</span>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n            >\n              {loading ? 'Saving...' : (debt ? 'Update' : 'Add Record')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAae,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAkB;;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,eAAe;QACf,sBAAsB;QACtB,cAAc;QACd,eAAe;QACf,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,MAAM;gBACR,YAAY;oBACV,eAAe,KAAK,aAAa;oBACjC,sBAAsB,KAAK,oBAAoB;oBAC/C,cAAc,KAAK,YAAY;oBAC/B,eAAe,KAAK,aAAa,CAAC,QAAQ;oBAC1C,UAAU,KAAK,QAAQ,CAAC,QAAQ;oBAChC,WAAW,KAAK,SAAS;gBAC3B;YACF,OAAO;gBACL,YAAY;oBACV,eAAe;oBACf,sBAAsB;oBACtB,cAAc;oBACd,eAAe;oBACf,UAAU;oBACV,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;gBACvD;YACF;QACF;8BAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,WAAW;gBACf,GAAG,QAAQ;gBACX,eAAe,WAAW,SAAS,aAAa;gBAChD,UAAU,SAAS,SAAS,QAAQ;YACtC;YAEA,MAAM,MAAM,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,GAAG;YAC7C,MAAM,SAAS,OAAO,QAAQ;YAE9B,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc,SAAS,aAAa,IAAI,SAAS,QAAQ,GAC3D,CAAC,WAAW,SAAS,aAAa,IAAI,SAAS,SAAS,QAAQ,CAAC,EAAE,OAAO,CAAC,KAC3E;IAEJ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,OAAO,qBAAqB;;;;;;sCAE/B,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,aAAa;oCAC7B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC1E,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,oBAAoB;oCACpC,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,sBAAsB,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjF,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACzE,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,aAAa;oCAC7B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC1E,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrE,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACtE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,6LAAC;wCAAK,WAAU;;4CAAmC;4CAAE;;;;;;;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,cAAe,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1D;GAlNwB;KAAA", "debugId": null}}, {"offset": {"line": 3738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/DebtsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport DebtModal from './DebtModal'\nimport { CustomerDebt } from '@/lib/supabase'\nimport { format } from 'date-fns'\n\ninterface DebtsSectionProps {\n  onStatsUpdate: () => void\n}\n\nexport default function DebtsSection({ onStatsUpdate }: DebtsSectionProps) {\n  const { resolvedTheme } = useTheme()\n  const [debts, setDebts] = useState<CustomerDebt[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [isModalOpen, setIsModalOpen] = useState(false)\n  const [editingDebt, setEditingDebt] = useState<CustomerDebt | null>(null)\n\n  useEffect(() => {\n    fetchDebts()\n  }, [])\n\n  const fetchDebts = async () => {\n    try {\n      const response = await fetch('/api/debts')\n      const data = await response.json()\n      setDebts(data.debts || [])\n    } catch (error) {\n      console.error('Error fetching debts:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this debt record?')) return\n\n    try {\n      const response = await fetch(`/api/debts/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setDebts(debts.filter(d => d.id !== id))\n        onStatsUpdate()\n      }\n    } catch (error) {\n      console.error('Error deleting debt:', error)\n    }\n  }\n\n  const handleEdit = (debt: CustomerDebt) => {\n    setEditingDebt(debt)\n    setIsModalOpen(true)\n  }\n\n  const handleModalClose = () => {\n    setIsModalOpen(false)\n    setEditingDebt(null)\n    fetchDebts()\n    onStatsUpdate()\n  }\n\n  const filteredDebts = debts.filter(debt => {\n    const customerName = `${debt.customer_name} ${debt.customer_family_name}`.toLowerCase()\n    const productName = debt.product_name.toLowerCase()\n    const search = searchTerm.toLowerCase()\n    return customerName.includes(search) || productName.includes(search)\n  })\n\n  // Group debts by customer\n  const groupedDebts = filteredDebts.reduce((acc, debt) => {\n    const customerKey = `${debt.customer_name} ${debt.customer_family_name}`\n    if (!acc[customerKey]) {\n      acc[customerKey] = []\n    }\n    acc[customerKey].push(debt)\n    return acc\n  }, {} as Record<string, CustomerDebt[]>)\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div className=\"relative\">\n          <Search\n            className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300\"\n            style={{\n              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n            }}\n          />\n          <input\n            type=\"text\"\n            placeholder=\"Search by customer or product...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n            }}\n          />\n        </div>\n        <button\n          onClick={() => setIsModalOpen(true)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Debt Record\n        </button>\n      </div>\n\n      {/* Debts List */}\n      <div className=\"space-y-6\">\n        {Object.entries(groupedDebts).map(([customerName, customerDebts]) => {\n          const totalAmount = customerDebts.reduce((sum, debt) => sum + debt.total_amount, 0)\n          \n          return (\n            <div key={customerName} className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n              <div className=\"bg-gray-50 px-6 py-4 border-b border-gray-200\">\n                <div className=\"flex justify-between items-center\">\n                  <div className=\"flex items-center\">\n                    <Users className=\"h-5 w-5 text-gray-400 mr-2\" />\n                    <h3 className=\"text-lg font-semibold text-gray-900\">{customerName}</h3>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm text-gray-600\">{customerDebts.length} item(s)</p>\n                    <p className=\"text-lg font-bold text-red-600\">₱{totalAmount.toFixed(2)}</p>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"divide-y divide-gray-200\">\n                {customerDebts.map((debt) => (\n                  <div key={debt.id} className=\"px-6 py-4\">\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-medium text-gray-900\">{debt.product_name}</h4>\n                        <div className=\"mt-1 text-sm text-gray-600 space-y-1\">\n                          <div className=\"flex items-center\">\n                            <span>Quantity: {debt.quantity}</span>\n                            <span className=\"mx-2\">•</span>\n                            <span>Unit Price: ₱{debt.product_price.toFixed(2)}</span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Calendar className=\"h-4 w-4 mr-1\" />\n                            <span>Date: {format(new Date(debt.debt_date), 'MMM dd, yyyy')}</span>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2 ml-4\">\n                        <div className=\"text-right\">\n                          <p className=\"font-semibold text-gray-900\">₱{debt.total_amount.toFixed(2)}</p>\n                        </div>\n                        <button\n                          onClick={() => handleEdit(debt)}\n                          className=\"p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors\"\n                        >\n                          <Edit className=\"h-4 w-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDelete(debt.id)}\n                          className=\"p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors\"\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )\n        })}\n      </div>\n\n      {filteredDebts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Users className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No debt records found</h3>\n          <p className=\"text-gray-600\">\n            {searchTerm\n              ? 'Try adjusting your search criteria'\n              : 'Get started by adding your first debt record'}\n          </p>\n        </div>\n      )}\n\n      {/* Debt Modal */}\n      <DebtModal\n        isOpen={isModalOpen}\n        onClose={handleModalClose}\n        debt={editingDebt}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;AAPA;;;;;;AAae,SAAS,aAAa,EAAE,aAAa,EAAqB;;IACvE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,KAAK,KAAK,IAAI,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,sDAAsD;QAEnE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE;gBAC/C,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,eAAe;QACf;QACA;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,eAAe,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE,CAAC,WAAW;QACrF,MAAM,cAAc,KAAK,YAAY,CAAC,WAAW;QACjD,MAAM,SAAS,WAAW,WAAW;QACrC,OAAO,aAAa,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC;IAC/D;IAEA,0BAA0B;IAC1B,MAAM,eAAe,cAAc,MAAM,CAAC,CAAC,KAAK;QAC9C,MAAM,cAAc,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE;QACxE,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;YACrB,GAAG,CAAC,YAAY,GAAG,EAAE;QACvB;QACA,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC;QACtB,OAAO;IACT,GAAG,CAAC;IAEJ,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCACL,WAAU;gCACV,OAAO;oCACL,OAAO,kBAAkB,SAAS,YAAY;gCAChD;;;;;;0CAEF,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,YAAY;oCACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,OAAO,kBAAkB,SAAS,YAAY;gCAChD;;;;;;;;;;;;kCAGJ,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,cAAc,cAAc;oBAC9D,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;oBAEjF,qBACE,6LAAC;wBAAuB,WAAU;;0CAChC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;wDAAyB,cAAc,MAAM;wDAAC;;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;;wDAAiC;wDAAE,YAAY,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAK1E,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;wCAAkB,WAAU;kDAC3B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B,KAAK,YAAY;;;;;;sEAC5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;gFAAK;gFAAW,KAAK,QAAQ;;;;;;;sFAC9B,6LAAC;4EAAK,WAAU;sFAAO;;;;;;sFACvB,6LAAC;;gFAAK;gFAAc,KAAK,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;8EAEjD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,6LAAC;;gFAAK;gFAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;8DAIpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;oEAA8B;oEAAE,KAAK,YAAY,CAAC,OAAO,CAAC;;;;;;;;;;;;sEAEzE,6LAAC;4DACC,SAAS,IAAM,WAAW;4DAC1B,WAAU;sEAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;4DACC,SAAS,IAAM,aAAa,KAAK,EAAE;4DACnC,WAAU;sEAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCA9BhB,KAAK,EAAE;;;;;;;;;;;uBAhBb;;;;;gBAuDd;;;;;;YAGD,cAAc,MAAM,KAAK,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,aACG,uCACA;;;;;;;;;;;;0BAMV,6LAAC,kIAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS;gBACT,MAAM;;;;;;;;;;;;AAId;GAnMwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 4228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/Calendar.tsx"], "sourcesContent": ["'use client'\n\nimport { ChevronLeft, ChevronRight, Plus, Clock, MapPin, Users, Calendar as CalendarIcon, Moon } from 'lucide-react'\nimport { useState } from 'react'\n\n// Moon Phase Types and Interfaces\ninterface MoonPhase {\n  name: string\n  emoji: string\n  icon: string\n  illumination: number\n  description: string\n}\n\ninterface MoonPhaseData {\n  phase: MoonPhase\n  age: number\n  illumination: number\n  nextFullMoon: Date\n  nextNewMoon: Date\n}\n\ninterface Event {\n  id: string\n  title: string\n  description: string\n  date: string\n  time: string\n  type: 'delivery' | 'meeting' | 'reminder' | 'holiday' | 'personal'\n  location?: string\n  attendees?: string[]\n}\n\n// Bisaya-Tagalog Language Dictionary\nconst BISAYA_TAGALOG_TEXTS = {\n  // Calendar Terms\n  calendar: 'Kalendaryo',\n  today: 'Karon nga Adlaw',\n  events: 'Mga Panghitabo',\n  schedule: 'Iskedyul',\n\n  // Moon Phase Terms\n  moonPhases: 'Mga Hugis sa Bulan',\n  moonPhase: 'Hugis sa Bulan',\n  newMoon: 'Bag-ong Bulan',\n  waxingCrescent: 'Nagdako nga Sungay',\n  firstQuarter: 'Una nga Bahin',\n  waxingGibbous: 'Nagdako nga Bula',\n  fullMoon: 'Puno nga Bulan',\n  waningGibbous: 'Nagliit nga Bula',\n  lastQuarter: 'Katapusan nga Bahin',\n  waningCrescent: 'Nagliit nga Sungay',\n\n  // Moon Phase Descriptions\n  newMoonDesc: 'Ang bulan dili makita gikan sa yuta',\n  waxingCrescentDesc: 'Nipis nga sungay sa bulan sa tuo nga bahin',\n  firstQuarterDesc: 'Katunga sa bulan nag-hayag sa tuo nga bahin',\n  waxingGibbousDesc: 'Sobra sa katunga sa bulan nag-hayag',\n  fullMoonDesc: 'Tibuok nga bulan nag-hayag ug makita',\n  waningGibbousDesc: 'Sobra sa katunga nag-hayag, nagliit na',\n  lastQuarterDesc: 'Katunga sa bulan nag-hayag sa wala nga bahin',\n  waningCrescentDesc: 'Nipis nga sungay sa bulan sa wala nga bahin',\n\n  // UI Elements\n  addEvent: 'Dugang Event',\n  manage: 'Pagdumala',\n  upcoming: 'Umaabot na',\n  legend: 'Giya',\n  age: 'Edad',\n  illumination: 'Kahayag',\n  days: 'mga adlaw',\n  next: 'Sunod',\n  cancel: 'Kanselar',\n  description: 'Deskripsyon',\n  location: 'Lugar',\n  time: 'Oras',\n  date: 'Petsa',\n  title: 'Titulo',\n  type: 'Klase',\n  attendees: 'Mga Apil',\n  more: 'pa',\n\n  // Event Types\n  delivery: 'Delivery',\n  meeting: 'Meeting',\n  reminder: 'Pahinumdom',\n  holiday: 'Holiday',\n  personal: 'Personal'\n}\n\n// Moon Phase Calculation Functions\nconst getMoonPhases = (): MoonPhase[] => [\n  {\n    name: BISAYA_TAGALOG_TEXTS.newMoon,\n    emoji: '🌑',\n    icon: 'new-moon',\n    illumination: 0,\n    description: BISAYA_TAGALOG_TEXTS.newMoonDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.waxingCrescent,\n    emoji: '🌒',\n    icon: 'waxing-crescent',\n    illumination: 25,\n    description: BISAYA_TAGALOG_TEXTS.waxingCrescentDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.firstQuarter,\n    emoji: '🌓',\n    icon: 'first-quarter',\n    illumination: 50,\n    description: BISAYA_TAGALOG_TEXTS.firstQuarterDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.waxingGibbous,\n    emoji: '🌔',\n    icon: 'waxing-gibbous',\n    illumination: 75,\n    description: BISAYA_TAGALOG_TEXTS.waxingGibbousDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.fullMoon,\n    emoji: '🌕',\n    icon: 'full-moon',\n    illumination: 100,\n    description: BISAYA_TAGALOG_TEXTS.fullMoonDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.waningGibbous,\n    emoji: '🌖',\n    icon: 'waning-gibbous',\n    illumination: 75,\n    description: BISAYA_TAGALOG_TEXTS.waningGibbousDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.lastQuarter,\n    emoji: '🌗',\n    icon: 'last-quarter',\n    illumination: 50,\n    description: BISAYA_TAGALOG_TEXTS.lastQuarterDesc\n  },\n  {\n    name: BISAYA_TAGALOG_TEXTS.waningCrescent,\n    emoji: '🌘',\n    icon: 'waning-crescent',\n    illumination: 25,\n    description: BISAYA_TAGALOG_TEXTS.waningCrescentDesc\n  }\n]\n\nconst calculateMoonPhase = (date: Date): MoonPhaseData => {\n  // Known new moon date: January 6, 2000, 18:14 UTC\n  const knownNewMoon = new Date(2000, 0, 6, 18, 14)\n  const lunarCycle = 29.53058867 // Average lunar cycle in days\n\n  // Calculate days since known new moon\n  const daysSinceNewMoon = (date.getTime() - knownNewMoon.getTime()) / (1000 * 60 * 60 * 24)\n\n  // Calculate current position in lunar cycle\n  const cyclePosition = daysSinceNewMoon % lunarCycle\n  const moonAge = cyclePosition < 0 ? cyclePosition + lunarCycle : cyclePosition\n\n  // Calculate illumination percentage\n  const illumination = Math.round((1 - Math.cos((moonAge / lunarCycle) * 2 * Math.PI)) * 50)\n\n  // Determine moon phase based on age\n  const phases = getMoonPhases()\n  let phaseIndex = 0\n\n  if (moonAge < 1.84566) phaseIndex = 0      // New Moon\n  else if (moonAge < 5.53699) phaseIndex = 1 // Waxing Crescent\n  else if (moonAge < 9.22831) phaseIndex = 2 // First Quarter\n  else if (moonAge < 12.91963) phaseIndex = 3 // Waxing Gibbous\n  else if (moonAge < 16.61096) phaseIndex = 4 // Full Moon\n  else if (moonAge < 20.30228) phaseIndex = 5 // Waning Gibbous\n  else if (moonAge < 23.99361) phaseIndex = 6 // Last Quarter\n  else phaseIndex = 7                        // Waning Crescent\n\n  // Ensure phaseIndex is within bounds\n  phaseIndex = Math.max(0, Math.min(phaseIndex, phases.length - 1))\n\n  // Calculate next full moon and new moon dates\n  const daysToNextFullMoon = (14.76529 - moonAge + lunarCycle) % lunarCycle\n  const daysToNextNewMoon = (lunarCycle - moonAge) % lunarCycle\n\n  const nextFullMoon = new Date(date.getTime() + daysToNextFullMoon * 24 * 60 * 60 * 1000)\n  const nextNewMoon = new Date(date.getTime() + daysToNextNewMoon * 24 * 60 * 60 * 1000)\n\n  return {\n    phase: phases[phaseIndex],\n    age: Math.round(moonAge * 10) / 10,\n    illumination,\n    nextFullMoon,\n    nextNewMoon\n  }\n}\n\n// Moon Phase Icon Component\nconst MoonPhaseIcon = ({ phase, size = 16, className = \"\" }: { phase: MoonPhase, size?: number, className?: string }) => {\n  const iconStyle = {\n    width: size,\n    height: size,\n    fontSize: size,\n    display: 'inline-block',\n    lineHeight: 1\n  }\n\n  return (\n    <span\n      className={`moon-phase-icon ${className}`}\n      style={iconStyle}\n      title={`${phase.name} - ${phase.description}`}\n    >\n      {phase.emoji}\n    </span>\n  )\n}\n\n// Moon Phase Tooltip Component\nconst MoonPhaseTooltip = ({ moonData, className = \"\" }: { moonData: MoonPhaseData, className?: string }) => {\n  return (\n    <div className={`absolute z-10 p-3 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 min-w-48 ${className}`}>\n      <div className=\"flex items-center space-x-2 mb-2\">\n        <MoonPhaseIcon phase={moonData.phase} size={20} />\n        <span className=\"font-semibold text-gray-900 dark:text-white\">{moonData.phase.name}</span>\n      </div>\n      <div className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n        <p>{moonData.phase.description}</p>\n        <p>{BISAYA_TAGALOG_TEXTS.age}: {moonData.age} {BISAYA_TAGALOG_TEXTS.days}</p>\n        <p>{BISAYA_TAGALOG_TEXTS.illumination}: {moonData.illumination}%</p>\n        <p className=\"text-xs pt-1 border-t border-gray-200 dark:border-gray-600\">\n          {BISAYA_TAGALOG_TEXTS.next} {BISAYA_TAGALOG_TEXTS.fullMoon}: {moonData.nextFullMoon.toLocaleDateString('tl-PH')}\n        </p>\n      </div>\n    </div>\n  )\n}\n\nexport default function Calendar() {\n  const [currentDate, setCurrentDate] = useState(new Date())\n  const [selectedDate, setSelectedDate] = useState<Date | null>(null)\n  const [isEventModalOpen, setIsEventModalOpen] = useState(false)\n  const [showMoonPhases, setShowMoonPhases] = useState(true)\n  const [hoveredMoonPhase, setHoveredMoonPhase] = useState<{ date: Date, moonData: MoonPhaseData, position: { x: number, y: number } } | null>(null)\n  const [events, setEvents] = useState<Event[]>([\n    {\n      id: '1',\n      title: 'Supplier Delivery',\n      description: 'Weekly grocery delivery from main supplier',\n      date: '2024-01-22',\n      time: '09:00',\n      type: 'delivery',\n      location: 'Store Front',\n    },\n    {\n      id: '2',\n      title: 'Monthly Inventory Check',\n      description: 'Complete inventory count and stock verification',\n      date: '2024-01-25',\n      time: '14:00',\n      type: 'reminder',\n    },\n    {\n      id: '3',\n      title: 'Community Meeting',\n      description: 'Barangay business owners meeting',\n      date: '2024-01-28',\n      time: '16:00',\n      type: 'meeting',\n      location: 'Barangay Hall',\n      attendees: ['Maria Santos', 'Juan Dela Cruz', 'Ana Reyes'],\n    },\n    {\n      id: '4',\n      title: 'New Year Holiday',\n      description: 'Store closed for New Year celebration',\n      date: '2024-01-01',\n      time: '00:00',\n      type: 'holiday',\n    },\n  ])\n\n  const [newEvent, setNewEvent] = useState({\n    title: '',\n    description: '',\n    date: '',\n    time: '',\n    type: 'reminder' as Event['type'],\n    location: '',\n  })\n\n  const monthNames = [\n    'Enero', 'Pebrero', 'Marso', 'Abril', 'Mayo', 'Hunyo',\n    'Hulyo', 'Agosto', 'Septyembre', 'Oktubre', 'Nobyembre', 'Disyembre'\n  ]\n\n  const daysOfWeek = ['Dom', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab']\n\n  const getDaysInMonth = (date: Date) => {\n    const year = date.getFullYear()\n    const month = date.getMonth()\n    const firstDay = new Date(year, month, 1)\n    const lastDay = new Date(year, month + 1, 0)\n    const daysInMonth = lastDay.getDate()\n    const startingDayOfWeek = firstDay.getDay()\n\n    const days = []\n    \n    // Add empty cells for days before the first day of the month\n    for (let i = 0; i < startingDayOfWeek; i++) {\n      days.push(null)\n    }\n    \n    // Add days of the month\n    for (let day = 1; day <= daysInMonth; day++) {\n      days.push(new Date(year, month, day))\n    }\n    \n    return days\n  }\n\n  const getEventsForDate = (date: Date) => {\n    const dateString = date.toISOString().split('T')[0]\n    return events.filter(event => event.date === dateString)\n  }\n\n  const getEventTypeColor = (type: Event['type']) => {\n    switch (type) {\n      case 'delivery':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'\n      case 'meeting':\n        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'\n      case 'reminder':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'\n      case 'holiday':\n        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n      case 'personal':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'\n    }\n  }\n\n  const navigateMonth = (direction: 'prev' | 'next') => {\n    setCurrentDate(prev => {\n      const newDate = new Date(prev)\n      if (direction === 'prev') {\n        newDate.setMonth(prev.getMonth() - 1)\n      } else {\n        newDate.setMonth(prev.getMonth() + 1)\n      }\n      return newDate\n    })\n  }\n\n  const handleAddEvent = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (newEvent.title && newEvent.date && newEvent.time) {\n      const event: Event = {\n        id: Date.now().toString(),\n        ...newEvent,\n      }\n      setEvents([...events, event])\n      setNewEvent({\n        title: '',\n        description: '',\n        date: '',\n        time: '',\n        type: 'reminder',\n        location: '',\n      })\n      setIsEventModalOpen(false)\n    }\n  }\n\n  const days = getDaysInMonth(currentDate)\n  const today = new Date()\n\n  return (\n    <div className=\"space-y-6 bisaya-calendar\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white cultural-accent bisaya-text\">{BISAYA_TAGALOG_TEXTS.calendar}</h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1 bisaya-text\">\n            {BISAYA_TAGALOG_TEXTS.manage} sa inyong store {BISAYA_TAGALOG_TEXTS.events} ug {BISAYA_TAGALOG_TEXTS.schedule} uban sa lunar phases\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          <button\n            onClick={() => setShowMoonPhases(!showMoonPhases)}\n            className={`flex items-center px-3 py-2 rounded-lg border transition-all duration-200 hover:scale-105 ${\n              showMoonPhases\n                ? 'bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-900/30 dark:border-blue-600 dark:text-blue-400 shadow-md'\n                : 'border-gray-300 text-gray-600 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-slate-700'\n            }`}\n          >\n            <Moon className=\"h-4 w-4 mr-2\" />\n            {BISAYA_TAGALOG_TEXTS.moonPhases}\n          </button>\n          <button\n            onClick={() => setIsEventModalOpen(true)}\n            className=\"btn-primary flex items-center hover:scale-105 shadow-lg\"\n          >\n            <Plus className=\"h-4 w-4 mr-2\" />\n            {BISAYA_TAGALOG_TEXTS.addEvent}\n          </button>\n        </div>\n      </div>\n\n      {/* Calendar Navigation */}\n      <div className=\"card p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}\n          </h3>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => navigateMonth('prev')}\n              className=\"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700\"\n            >\n              <ChevronLeft className=\"h-4 w-4\" />\n            </button>\n            <button\n              onClick={() => setCurrentDate(new Date())}\n              className=\"px-4 py-2 text-sm bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-all duration-200 hover:scale-105 shadow-sm\"\n            >\n              {BISAYA_TAGALOG_TEXTS.today}\n            </button>\n            <button\n              onClick={() => navigateMonth('next')}\n              className=\"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700\"\n            >\n              <ChevronRight className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Calendar Grid */}\n        <div className=\"grid grid-cols-7 gap-1\">\n          {/* Day headers */}\n          {daysOfWeek.map(day => (\n            <div key={day} className=\"p-3 text-center text-sm font-medium text-gray-500 dark:text-gray-400\">\n              {day}\n            </div>\n          ))}\n          \n          {/* Calendar days */}\n          {days.map((day, index) => {\n            if (!day) {\n              return <div key={index} className=\"p-3 h-28\"></div>\n            }\n\n            const dayEvents = getEventsForDate(day)\n            const isToday = day.toDateString() === today.toDateString()\n            const isSelected = selectedDate?.toDateString() === day.toDateString()\n            const moonData = calculateMoonPhase(day)\n\n            return (\n              <div\n                key={index}\n                onClick={() => setSelectedDate(day)}\n                className={`calendar-day-cell p-2 h-28 border border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 transition-all duration-200 relative ${\n                  isToday ? 'bg-green-50 dark:bg-green-900/20 ring-1 ring-green-300 dark:ring-green-600' : ''\n                } ${isSelected ? 'ring-2 ring-green-500 bg-green-100 dark:bg-green-900/30' : ''}`}\n              >\n                {/* Day number and moon phase */}\n                <div className=\"flex items-center justify-between mb-1\">\n                  <div className={`text-sm font-medium ${\n                    isToday ? 'text-green-600 dark:text-green-400' : 'text-gray-900 dark:text-white'\n                  }`}>\n                    {day.getDate()}\n                  </div>\n                  {showMoonPhases && (\n                    <div\n                      className=\"moon-phase-container relative\"\n                      onMouseEnter={(e) => {\n                        const rect = e.currentTarget.getBoundingClientRect()\n                        setHoveredMoonPhase({\n                          date: day,\n                          moonData,\n                          position: { x: rect.left, y: rect.top }\n                        })\n                      }}\n                      onMouseLeave={() => setHoveredMoonPhase(null)}\n                    >\n                      <MoonPhaseIcon\n                        phase={moonData.phase}\n                        size={14}\n                        className=\"opacity-80 hover:opacity-100 transition-opacity duration-200\"\n                      />\n                    </div>\n                  )}\n                </div>\n\n                {/* Events */}\n                <div className=\"space-y-1\">\n                  {dayEvents.slice(0, showMoonPhases ? 1 : 2).map(event => (\n                    <div\n                      key={event.id}\n                      className={`text-xs px-1 py-0.5 rounded truncate ${getEventTypeColor(event.type)}`}\n                    >\n                      {event.title}\n                    </div>\n                  ))}\n                  {dayEvents.length > (showMoonPhases ? 1 : 2) && (\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      +{dayEvents.length - (showMoonPhases ? 1 : 2)} {BISAYA_TAGALOG_TEXTS.more}\n                    </div>\n                  )}\n                </div>\n              </div>\n            )\n          })}\n        </div>\n      </div>\n\n      {/* Moon Phase Information Panel */}\n      {showMoonPhases && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Current Moon Phase */}\n          <div className=\"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text\">\n              <Moon className=\"h-5 w-5 mr-2 text-blue-500\" />\n              {BISAYA_TAGALOG_TEXTS.moonPhase} Karon nga Adlaw\n            </h3>\n            {(() => {\n              const todayMoon = calculateMoonPhase(today)\n              return (\n                <div className=\"text-center\">\n                  <div className=\"mb-3\">\n                    <MoonPhaseIcon phase={todayMoon.phase} size={48} className=\"mx-auto\" />\n                  </div>\n                  <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-2\">{todayMoon.phase.name}</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">{todayMoon.phase.description}</p>\n                  <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                    <div className=\"bg-gray-50 dark:bg-slate-700/50 p-2 rounded hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200\">\n                      <div className=\"font-medium text-gray-900 dark:text-white\">{BISAYA_TAGALOG_TEXTS.age}</div>\n                      <div className=\"text-gray-600 dark:text-gray-400\">{todayMoon.age} {BISAYA_TAGALOG_TEXTS.days}</div>\n                    </div>\n                    <div className=\"bg-gray-50 dark:bg-slate-700/50 p-2 rounded hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200\">\n                      <div className=\"font-medium text-gray-900 dark:text-white\">{BISAYA_TAGALOG_TEXTS.illumination}</div>\n                      <div className=\"text-gray-600 dark:text-gray-400\">{todayMoon.illumination}%</div>\n                    </div>\n                  </div>\n                </div>\n              )\n            })()}\n          </div>\n\n          {/* Upcoming Moon Events */}\n          <div className=\"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text\">\n              <span className=\"text-lg mr-2\">🌙</span>\n              {BISAYA_TAGALOG_TEXTS.upcoming} nga Bulan {BISAYA_TAGALOG_TEXTS.events}\n            </h3>\n            {(() => {\n              const todayMoon = calculateMoonPhase(today)\n              return (\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200\">\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-xl\">🌕</span>\n                      <div>\n                        <div className=\"font-medium text-gray-900 dark:text-white\">{BISAYA_TAGALOG_TEXTS.next} {BISAYA_TAGALOG_TEXTS.fullMoon}</div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {todayMoon.nextFullMoon.toLocaleDateString('tl-PH', {\n                            weekday: 'long',\n                            year: 'numeric',\n                            month: 'long',\n                            day: 'numeric'\n                          })}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-slate-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200\">\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-xl\">🌑</span>\n                      <div>\n                        <div className=\"font-medium text-gray-900 dark:text-white\">{BISAYA_TAGALOG_TEXTS.next} {BISAYA_TAGALOG_TEXTS.newMoon}</div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {todayMoon.nextNewMoon.toLocaleDateString('tl-PH', {\n                            weekday: 'long',\n                            year: 'numeric',\n                            month: 'long',\n                            day: 'numeric'\n                          })}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )\n            })()}\n          </div>\n\n          {/* Moon Phase Legend */}\n          <div className=\"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text\">\n              <span className=\"text-lg mr-2\">📖</span>\n              {BISAYA_TAGALOG_TEXTS.legend} sa {BISAYA_TAGALOG_TEXTS.moonPhases}\n            </h3>\n            <div className=\"grid grid-cols-1 gap-2\">\n              {getMoonPhases().map((phase, index) => (\n                <div key={index} className=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 hover:scale-102\">\n                  <MoonPhaseIcon phase={phase} size={18} />\n                  <div className=\"flex-1\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">{phase.name}</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">{phase.illumination}% {BISAYA_TAGALOG_TEXTS.illumination}</div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Moon Phase Tooltip */}\n      {hoveredMoonPhase && (\n        <div\n          className=\"fixed pointer-events-none z-50\"\n          style={{\n            left: hoveredMoonPhase.position.x,\n            top: hoveredMoonPhase.position.y - 10,\n            transform: 'translateY(-100%)'\n          }}\n        >\n          <MoonPhaseTooltip moonData={hoveredMoonPhase.moonData} />\n        </div>\n      )}\n\n      {/* Upcoming Events */}\n      <div className=\"card p-6 hover:shadow-lg transition-all duration-300\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <span className=\"text-lg mr-2\">📅</span>\n          {BISAYA_TAGALOG_TEXTS.upcoming} nga {BISAYA_TAGALOG_TEXTS.events}\n        </h3>\n        \n        <div className=\"space-y-3\">\n          {events\n            .filter(event => new Date(event.date) >= today)\n            .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())\n            .slice(0, 5)\n            .map(event => (\n              <div key={event.id} className=\"flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700\">\n                <div className={`p-2 rounded-lg ${getEventTypeColor(event.type)}`}>\n                  <CalendarIcon className=\"h-4 w-4\" />\n                </div>\n                \n                <div className=\"flex-1\">\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">{event.title}</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">{event.description}</p>\n                  \n                  <div className=\"flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400\">\n                    <div className=\"flex items-center space-x-1\">\n                      <Clock className=\"h-3 w-3\" />\n                      <span>{new Date(event.date).toLocaleDateString()} at {event.time}</span>\n                    </div>\n                    \n                    {event.location && (\n                      <div className=\"flex items-center space-x-1\">\n                        <MapPin className=\"h-3 w-3\" />\n                        <span>{event.location}</span>\n                      </div>\n                    )}\n                    \n                    {event.attendees && (\n                      <div className=\"flex items-center space-x-1\">\n                        <Users className=\"h-3 w-3\" />\n                        <span>{event.attendees.length} {BISAYA_TAGALOG_TEXTS.attendees}</span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n        </div>\n      </div>\n\n      {/* Add Event Modal */}\n      {isEventModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md shadow-2xl animate-fade-in-up\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n              <Plus className=\"h-5 w-5 mr-2 text-green-500\" />\n              Dugang Bag-ong {BISAYA_TAGALOG_TEXTS.events}\n            </h3>\n            \n            <form onSubmit={handleAddEvent} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {BISAYA_TAGALOG_TEXTS.title} sa {BISAYA_TAGALOG_TEXTS.events}\n                </label>\n                <input\n                  type=\"text\"\n                  value={newEvent.title}\n                  onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                  placeholder=\"I-type ang titulo sa event...\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {BISAYA_TAGALOG_TEXTS.description}\n                </label>\n                <textarea\n                  value={newEvent.description}\n                  onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                  placeholder=\"Detalye sa event...\"\n                />\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {BISAYA_TAGALOG_TEXTS.date}\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={newEvent.date}\n                    onChange={(e) => setNewEvent({ ...newEvent, date: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {BISAYA_TAGALOG_TEXTS.time}\n                  </label>\n                  <input\n                    type=\"time\"\n                    value={newEvent.time}\n                    onChange={(e) => setNewEvent({ ...newEvent, time: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                    required\n                  />\n                </div>\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {BISAYA_TAGALOG_TEXTS.type} sa {BISAYA_TAGALOG_TEXTS.events}\n                </label>\n                <select\n                  value={newEvent.type}\n                  onChange={(e) => setNewEvent({ ...newEvent, type: e.target.value as Event['type'] })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                >\n                  <option value=\"reminder\">{BISAYA_TAGALOG_TEXTS.reminder}</option>\n                  <option value=\"delivery\">{BISAYA_TAGALOG_TEXTS.delivery}</option>\n                  <option value=\"meeting\">{BISAYA_TAGALOG_TEXTS.meeting}</option>\n                  <option value=\"holiday\">{BISAYA_TAGALOG_TEXTS.holiday}</option>\n                  <option value=\"personal\">{BISAYA_TAGALOG_TEXTS.personal}</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {BISAYA_TAGALOG_TEXTS.location} (Optional)\n                </label>\n                <input\n                  type=\"text\"\n                  value={newEvent.location}\n                  onChange={(e) => setNewEvent({ ...newEvent, location: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200\"\n                  placeholder=\"Asa ang event...\"\n                />\n              </div>\n              \n              <div className=\"flex space-x-3 pt-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => setIsEventModalOpen(false)}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700 transition-all duration-200 hover:scale-105\"\n                >\n                  {BISAYA_TAGALOG_TEXTS.cancel}\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 btn-primary hover:scale-105 shadow-lg\"\n                >\n                  {BISAYA_TAGALOG_TEXTS.addEvent}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAHA;;;AAiCA,qCAAqC;AACrC,MAAM,uBAAuB;IAC3B,iBAAiB;IACjB,UAAU;IACV,OAAO;IACP,QAAQ;IACR,UAAU;IAEV,mBAAmB;IACnB,YAAY;IACZ,WAAW;IACX,SAAS;IACT,gBAAgB;IAChB,cAAc;IACd,eAAe;IACf,UAAU;IACV,eAAe;IACf,aAAa;IACb,gBAAgB;IAEhB,0BAA0B;IAC1B,aAAa;IACb,oBAAoB;IACpB,kBAAkB;IAClB,mBAAmB;IACnB,cAAc;IACd,mBAAmB;IACnB,iBAAiB;IACjB,oBAAoB;IAEpB,cAAc;IACd,UAAU;IACV,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,KAAK;IACL,cAAc;IACd,MAAM;IACN,MAAM;IACN,QAAQ;IACR,aAAa;IACb,UAAU;IACV,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,WAAW;IACX,MAAM;IAEN,cAAc;IACd,UAAU;IACV,SAAS;IACT,UAAU;IACV,SAAS;IACT,UAAU;AACZ;AAEA,mCAAmC;AACnC,MAAM,gBAAgB,IAAmB;QACvC;YACE,MAAM,qBAAqB,OAAO;YAClC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,WAAW;QAC/C;QACA;YACE,MAAM,qBAAqB,cAAc;YACzC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,kBAAkB;QACtD;QACA;YACE,MAAM,qBAAqB,YAAY;YACvC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,gBAAgB;QACpD;QACA;YACE,MAAM,qBAAqB,aAAa;YACxC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,iBAAiB;QACrD;QACA;YACE,MAAM,qBAAqB,QAAQ;YACnC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,YAAY;QAChD;QACA;YACE,MAAM,qBAAqB,aAAa;YACxC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,iBAAiB;QACrD;QACA;YACE,MAAM,qBAAqB,WAAW;YACtC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,eAAe;QACnD;QACA;YACE,MAAM,qBAAqB,cAAc;YACzC,OAAO;YACP,MAAM;YACN,cAAc;YACd,aAAa,qBAAqB,kBAAkB;QACtD;KACD;AAED,MAAM,qBAAqB,CAAC;IAC1B,kDAAkD;IAClD,MAAM,eAAe,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI;IAC9C,MAAM,aAAa,YAAY,8BAA8B;;IAE7D,sCAAsC;IACtC,MAAM,mBAAmB,CAAC,KAAK,OAAO,KAAK,aAAa,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAEzF,4CAA4C;IAC5C,MAAM,gBAAgB,mBAAmB;IACzC,MAAM,UAAU,gBAAgB,IAAI,gBAAgB,aAAa;IAEjE,oCAAoC;IACpC,MAAM,eAAe,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,AAAC,UAAU,aAAc,IAAI,KAAK,EAAE,CAAC,IAAI;IAEvF,oCAAoC;IACpC,MAAM,SAAS;IACf,IAAI,aAAa;IAEjB,IAAI,UAAU,SAAS,aAAa,EAAO,WAAW;;SACjD,IAAI,UAAU,SAAS,aAAa,EAAE,kBAAkB;;SACxD,IAAI,UAAU,SAAS,aAAa,EAAE,gBAAgB;;SACtD,IAAI,UAAU,UAAU,aAAa,EAAE,iBAAiB;;SACxD,IAAI,UAAU,UAAU,aAAa,EAAE,YAAY;;SACnD,IAAI,UAAU,UAAU,aAAa,EAAE,iBAAiB;;SACxD,IAAI,UAAU,UAAU,aAAa,EAAE,eAAe;;SACtD,aAAa,EAAyB,kBAAkB;;IAE7D,qCAAqC;IACrC,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,YAAY,OAAO,MAAM,GAAG;IAE9D,8CAA8C;IAC9C,MAAM,qBAAqB,CAAC,WAAW,UAAU,UAAU,IAAI;IAC/D,MAAM,oBAAoB,CAAC,aAAa,OAAO,IAAI;IAEnD,MAAM,eAAe,IAAI,KAAK,KAAK,OAAO,KAAK,qBAAqB,KAAK,KAAK,KAAK;IACnF,MAAM,cAAc,IAAI,KAAK,KAAK,OAAO,KAAK,oBAAoB,KAAK,KAAK,KAAK;IAEjF,OAAO;QACL,OAAO,MAAM,CAAC,WAAW;QACzB,KAAK,KAAK,KAAK,CAAC,UAAU,MAAM;QAChC;QACA;QACA;IACF;AACF;AAEA,4BAA4B;AAC5B,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,YAAY,EAAE,EAA2D;IAClH,MAAM,YAAY;QAChB,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,YAAY;IACd;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,gBAAgB,EAAE,WAAW;QACzC,OAAO;QACP,OAAO,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,MAAM,WAAW,EAAE;kBAE5C,MAAM,KAAK;;;;;;AAGlB;KAlBM;AAoBN,+BAA+B;AAC/B,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmD;IACrG,qBACE,6LAAC;QAAI,WAAW,CAAC,uHAAuH,EAAE,WAAW;;0BACnJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAc,OAAO,SAAS,KAAK;wBAAE,MAAM;;;;;;kCAC5C,6LAAC;wBAAK,WAAU;kCAA+C,SAAS,KAAK,CAAC,IAAI;;;;;;;;;;;;0BAEpF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAG,SAAS,KAAK,CAAC,WAAW;;;;;;kCAC9B,6LAAC;;4BAAG,qBAAqB,GAAG;4BAAC;4BAAG,SAAS,GAAG;4BAAC;4BAAE,qBAAqB,IAAI;;;;;;;kCACxE,6LAAC;;4BAAG,qBAAqB,YAAY;4BAAC;4BAAG,SAAS,YAAY;4BAAC;;;;;;;kCAC/D,6LAAC;wBAAE,WAAU;;4BACV,qBAAqB,IAAI;4BAAC;4BAAE,qBAAqB,QAAQ;4BAAC;4BAAG,SAAS,YAAY,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;AAKjH;MAjBM;AAmBS,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsF;IAC7I,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;QAC5C;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;YACN,UAAU;YACV,WAAW;gBAAC;gBAAgB;gBAAkB;aAAY;QAC5D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;QACR;KACD;IAED,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IAEA,MAAM,aAAa;QACjB;QAAS;QAAW;QAAS;QAAS;QAAQ;QAC9C;QAAS;QAAU;QAAc;QAAW;QAAa;KAC1D;IAED,MAAM,aAAa;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAAM;IAEpE,MAAM,iBAAiB,CAAC;QACtB,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,KAAK,QAAQ;QAC3B,MAAM,WAAW,IAAI,KAAK,MAAM,OAAO;QACvC,MAAM,UAAU,IAAI,KAAK,MAAM,QAAQ,GAAG;QAC1C,MAAM,cAAc,QAAQ,OAAO;QACnC,MAAM,oBAAoB,SAAS,MAAM;QAEzC,MAAM,OAAO,EAAE;QAEf,6DAA6D;QAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,IAAK;YAC1C,KAAK,IAAI,CAAC;QACZ;QAEA,wBAAwB;QACxB,IAAK,IAAI,MAAM,GAAG,OAAO,aAAa,MAAO;YAC3C,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM,OAAO;QAClC;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK;IAC/C;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,eAAe,CAAA;YACb,MAAM,UAAU,IAAI,KAAK;YACzB,IAAI,cAAc,QAAQ;gBACxB,QAAQ,QAAQ,CAAC,KAAK,QAAQ,KAAK;YACrC,OAAO;gBACL,QAAQ,QAAQ,CAAC,KAAK,QAAQ,KAAK;YACrC;YACA,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,IAAI,SAAS,KAAK,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,EAAE;YACpD,MAAM,QAAe;gBACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,GAAG,QAAQ;YACb;YACA,UAAU;mBAAI;gBAAQ;aAAM;YAC5B,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;YACZ;YACA,oBAAoB;QACtB;IACF;IAEA,MAAM,OAAO,eAAe;IAC5B,MAAM,QAAQ,IAAI;IAElB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgF,qBAAqB,QAAQ;;;;;;0CAC3H,6LAAC;gCAAE,WAAU;;oCACV,qBAAqB,MAAM;oCAAC;oCAAkB,qBAAqB,MAAM;oCAAC;oCAAK,qBAAqB,QAAQ;oCAAC;;;;;;;;;;;;;kCAGlH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC;gCAClC,WAAW,CAAC,0FAA0F,EACpG,iBACI,mHACA,kHACJ;;kDAEF,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,qBAAqB,UAAU;;;;;;;0CAElC,6LAAC;gCACC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,qBAAqB,QAAQ;;;;;;;;;;;;;;;;;;;0BAMpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCACX,UAAU,CAAC,YAAY,QAAQ,GAAG;oCAAC;oCAAE,YAAY,WAAW;;;;;;;0CAE/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;kDAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,6LAAC;wCACC,SAAS,IAAM,eAAe,IAAI;wCAClC,WAAU;kDAET,qBAAqB,KAAK;;;;;;kDAE7B,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;kDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM9B,6LAAC;wBAAI,WAAU;;4BAEZ,WAAW,GAAG,CAAC,CAAA,oBACd,6LAAC;oCAAc,WAAU;8CACtB;mCADO;;;;;4BAMX,KAAK,GAAG,CAAC,CAAC,KAAK;gCACd,IAAI,CAAC,KAAK;oCACR,qBAAO,6LAAC;wCAAgB,WAAU;uCAAjB;;;;;gCACnB;gCAEA,MAAM,YAAY,iBAAiB;gCACnC,MAAM,UAAU,IAAI,YAAY,OAAO,MAAM,YAAY;gCACzD,MAAM,aAAa,cAAc,mBAAmB,IAAI,YAAY;gCACpE,MAAM,WAAW,mBAAmB;gCAEpC,qBACE,6LAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,oKAAoK,EAC9K,UAAU,+EAA+E,GAC1F,CAAC,EAAE,aAAa,4DAA4D,IAAI;;sDAGjF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,oBAAoB,EACnC,UAAU,uCAAuC,iCACjD;8DACC,IAAI,OAAO;;;;;;gDAEb,gCACC,6LAAC;oDACC,WAAU;oDACV,cAAc,CAAC;wDACb,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;wDAClD,oBAAoB;4DAClB,MAAM;4DACN;4DACA,UAAU;gEAAE,GAAG,KAAK,IAAI;gEAAE,GAAG,KAAK,GAAG;4DAAC;wDACxC;oDACF;oDACA,cAAc,IAAM,oBAAoB;8DAExC,cAAA,6LAAC;wDACC,OAAO,SAAS,KAAK;wDACrB,MAAM;wDACN,WAAU;;;;;;;;;;;;;;;;;sDAOlB,6LAAC;4CAAI,WAAU;;gDACZ,UAAU,KAAK,CAAC,GAAG,iBAAiB,IAAI,GAAG,GAAG,CAAC,CAAA,sBAC9C,6LAAC;wDAEC,WAAW,CAAC,qCAAqC,EAAE,kBAAkB,MAAM,IAAI,GAAG;kEAEjF,MAAM,KAAK;uDAHP,MAAM,EAAE;;;;;gDAMhB,UAAU,MAAM,GAAG,CAAC,iBAAiB,IAAI,CAAC,mBACzC,6LAAC;oDAAI,WAAU;;wDAA2C;wDACtD,UAAU,MAAM,GAAG,CAAC,iBAAiB,IAAI,CAAC;wDAAE;wDAAE,qBAAqB,IAAI;;;;;;;;;;;;;;mCA/C1E;;;;;4BAqDX;;;;;;;;;;;;;YAKH,gCACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,qBAAqB,SAAS;oCAAC;;;;;;;4BAEjC,CAAC;gCACA,MAAM,YAAY,mBAAmB;gCACrC,qBACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAc,OAAO,UAAU,KAAK;gDAAE,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAE7D,6LAAC;4CAAG,WAAU;sDAAwD,UAAU,KAAK,CAAC,IAAI;;;;;;sDAC1F,6LAAC;4CAAE,WAAU;sDAAiD,UAAU,KAAK,CAAC,WAAW;;;;;;sDACzF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA6C,qBAAqB,GAAG;;;;;;sEACpF,6LAAC;4DAAI,WAAU;;gEAAoC,UAAU,GAAG;gEAAC;gEAAE,qBAAqB,IAAI;;;;;;;;;;;;;8DAE9F,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA6C,qBAAqB,YAAY;;;;;;sEAC7F,6LAAC;4DAAI,WAAU;;gEAAoC,UAAU,YAAY;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;4BAKpF,CAAC;;;;;;;kCAIH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAe;;;;;;oCAC9B,qBAAqB,QAAQ;oCAAC;oCAAY,qBAAqB,MAAM;;;;;;;4BAEvE,CAAC;gCACA,MAAM,YAAY,mBAAmB;gCACrC,qBACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;oEAA6C,qBAAqB,IAAI;oEAAC;oEAAE,qBAAqB,QAAQ;;;;;;;0EACrH,6LAAC;gEAAI,WAAU;0EACZ,UAAU,YAAY,CAAC,kBAAkB,CAAC,SAAS;oEAClD,SAAS;oEACT,MAAM;oEACN,OAAO;oEACP,KAAK;gEACP;;;;;;;;;;;;;;;;;;;;;;;sDAKR,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;oEAA6C,qBAAqB,IAAI;oEAAC;oEAAE,qBAAqB,OAAO;;;;;;;0EACpH,6LAAC;gEAAI,WAAU;0EACZ,UAAU,WAAW,CAAC,kBAAkB,CAAC,SAAS;oEACjD,SAAS;oEACT,MAAM;oEACN,OAAO;oEACP,KAAK;gEACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAOd,CAAC;;;;;;;kCAIH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAe;;;;;;oCAC9B,qBAAqB,MAAM;oCAAC;oCAAK,qBAAqB,UAAU;;;;;;;0CAEnE,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAc,OAAO;gDAAO,MAAM;;;;;;0DACnC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAqD,MAAM,IAAI;;;;;;kEAC9E,6LAAC;wDAAI,WAAU;;4DAA4C,MAAM,YAAY;4DAAC;4DAAG,qBAAqB,YAAY;;;;;;;;;;;;;;uCAJ5G;;;;;;;;;;;;;;;;;;;;;;YAcnB,kCACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,MAAM,iBAAiB,QAAQ,CAAC,CAAC;oBACjC,KAAK,iBAAiB,QAAQ,CAAC,CAAC,GAAG;oBACnC,WAAW;gBACb;0BAEA,cAAA,6LAAC;oBAAiB,UAAU,iBAAiB,QAAQ;;;;;;;;;;;0BAKzD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAK,WAAU;0CAAe;;;;;;4BAC9B,qBAAqB,QAAQ;4BAAC;4BAAM,qBAAqB,MAAM;;;;;;;kCAGlE,6LAAC;wBAAI,WAAU;kCACZ,OACE,MAAM,CAAC,CAAA,QAAS,IAAI,KAAK,MAAM,IAAI,KAAK,OACxC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,sBACH,6LAAC;gCAAmB,WAAU;;kDAC5B,6LAAC;wCAAI,WAAW,CAAC,eAAe,EAAE,kBAAkB,MAAM,IAAI,GAAG;kDAC/D,cAAA,6LAAC,6MAAA,CAAA,WAAY;4CAAC,WAAU;;;;;;;;;;;kDAG1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA6C,MAAM,KAAK;;;;;;0DACtE,6LAAC;gDAAE,WAAU;0DAAiD,MAAM,WAAW;;;;;;0DAE/E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;;oEAAM,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB;oEAAG;oEAAK,MAAM,IAAI;;;;;;;;;;;;;oDAGjE,MAAM,QAAQ,kBACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAM,MAAM,QAAQ;;;;;;;;;;;;oDAIxB,MAAM,SAAS,kBACd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;;oEAAM,MAAM,SAAS,CAAC,MAAM;oEAAC;oEAAE,qBAAqB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;+BAzB9D,MAAM,EAAE;;;;;;;;;;;;;;;;YAoCzB,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAgC;gCAChC,qBAAqB,MAAM;;;;;;;sCAG7C,6LAAC;4BAAK,UAAU;4BAAgB,WAAU;;8CACxC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDACd,qBAAqB,KAAK;gDAAC;gDAAK,qBAAqB,MAAM;;;;;;;sDAE9D,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAClE,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDACd,qBAAqB,WAAW;;;;;;sDAEnC,6LAAC;4CACC,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DACd,qBAAqB,IAAI;;;;;;8DAE5B,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjE,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DACd,qBAAqB,IAAI;;;;;;8DAE5B,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjE,WAAU;oDACV,QAAQ;;;;;;;;;;;;;;;;;;8CAKd,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDACd,qBAAqB,IAAI;gDAAC;gDAAK,qBAAqB,MAAM;;;;;;;sDAE7D,6LAAC;4CACC,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAkB;4CAClF,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAY,qBAAqB,QAAQ;;;;;;8DACvD,6LAAC;oDAAO,OAAM;8DAAY,qBAAqB,QAAQ;;;;;;8DACvD,6LAAC;oDAAO,OAAM;8DAAW,qBAAqB,OAAO;;;;;;8DACrD,6LAAC;oDAAO,OAAM;8DAAW,qBAAqB,OAAO;;;;;;8DACrD,6LAAC;oDAAO,OAAM;8DAAY,qBAAqB,QAAQ;;;;;;;;;;;;;;;;;;8CAI3D,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDACd,qBAAqB,QAAQ;gDAAC;;;;;;;sDAEjC,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACrE,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDAET,qBAAqB,MAAM;;;;;;sDAE9B,6LAAC;4CACC,MAAK;4CACL,WAAU;sDAET,qBAAqB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;GA9iBwB;MAAA", "debugId": null}}, {"offset": {"line": 5859, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/AISupport.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useTheme } from 'next-themes'\nimport {\n  Bo<PERSON>,\n  User,\n  Send,\n  Loader2,\n  TrendingUp,\n  Package,\n  CreditCard,\n  BarChart3,\n  Lightbulb,\n  HelpCircle,\n  Zap,\n  Brain\n} from 'lucide-react'\nimport { motion } from 'framer-motion'\n\ninterface Message {\n  id: string\n  type: 'user' | 'ai'\n  content: string\n  timestamp: Date\n}\n\nconst quickPrompts = [\n  {\n    icon: TrendingUp,\n    title: 'Sales Analysis',\n    prompt: 'Analyze my current sales performance and suggest improvements'\n  },\n  {\n    icon: Package,\n    title: 'Inventory Management',\n    prompt: 'Help me optimize my inventory levels and identify slow-moving products'\n  },\n  {\n    icon: CreditCard,\n    title: 'Debt Management',\n    prompt: 'Provide strategies for managing customer debts effectively'\n  },\n  {\n    icon: BarChart3,\n    title: 'Business Insights',\n    prompt: 'Give me insights on how to grow my sari-sari store business'\n  },\n  {\n    icon: Lightbulb,\n    title: 'Marketing Ideas',\n    prompt: 'Suggest marketing strategies to attract more customers'\n  },\n  {\n    icon: HelpCircle,\n    title: 'General Help',\n    prompt: 'What can you help me with regarding my store management?'\n  }\n]\n\nexport default function AISupport() {\n  const { resolvedTheme } = useTheme()\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      type: 'ai',\n      content: 'Welcome to AI Support! I\\'m here to help you manage your Revantad Store more effectively. I can assist with business analytics, inventory management, customer relationships, financial planning, and much more. How can I help you today?',\n      timestamp: new Date()\n    }\n  ])\n  const [inputMessage, setInputMessage] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  useEffect(() => {\n    if (inputRef.current) {\n      inputRef.current.focus()\n    }\n  }, [])\n\n  const sendMessage = async (messageContent?: string) => {\n    const content = messageContent || inputMessage.trim()\n    if (!content || isLoading) return\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      type: 'user',\n      content,\n      timestamp: new Date()\n    }\n\n    setMessages(prev => [...prev, userMessage])\n    setInputMessage('')\n    setIsLoading(true)\n\n    try {\n      const response = await fetch('/api/ai', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: content,\n          context: 'ai-support'\n        })\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        const aiMessage: Message = {\n          id: (Date.now() + 1).toString(),\n          type: 'ai',\n          content: data.response,\n          timestamp: new Date()\n        }\n        setMessages(prev => [...prev, aiMessage])\n      } else {\n        throw new Error(data.error || 'Failed to get AI response')\n      }\n    } catch (error) {\n      const errorMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        type: 'ai',\n        content: 'Sorry, I encountered an error. Please try again later.',\n        timestamp: new Date()\n      }\n      setMessages(prev => [...prev, errorMessage])\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      sendMessage()\n    }\n  }\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('en-US', { \n      hour: '2-digit', \n      minute: '2-digit',\n      hour12: true \n    })\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center mb-4\">\n          <motion.div\n            className=\"p-3 rounded-xl mr-3\"\n            style={{\n              background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n              boxShadow: '0 4px 8px rgba(139, 92, 246, 0.3)'\n            }}\n            whileHover={{ scale: 1.05 }}\n          >\n            <Brain className=\"w-6 h-6 text-white\" />\n          </motion.div>\n          <div>\n            <h2\n              className=\"text-xl font-bold\"\n              style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}\n            >\n              AI Business Assistant\n            </h2>\n            <p\n              className=\"text-sm opacity-80\"\n              style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#64748b' }}\n            >\n              Intelligent support for your sari-sari store operations\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"mb-6\">\n        <h3\n          className=\"text-lg font-semibold mb-4 flex items-center\"\n          style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}\n        >\n          <Zap className=\"w-5 h-5 mr-2 text-purple-500\" />\n          Quick Actions\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {quickPrompts.map((prompt, index) => {\n            const Icon = prompt.icon\n            return (\n              <motion.button\n                key={index}\n                onClick={() => sendMessage(prompt.prompt)}\n                className=\"p-5 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 text-left\"\n                style={{\n                  backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)',\n                  borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(229, 231, 235, 0.8)',\n                  boxShadow: resolvedTheme === 'dark'\n                    ? '0 4px 6px rgba(0, 0, 0, 0.1)'\n                    : '0 4px 6px rgba(0, 0, 0, 0.05)'\n                }}\n                whileHover={{ \n                  scale: 1.03,\n                  boxShadow: resolvedTheme === 'dark'\n                    ? '0 8px 25px rgba(139, 92, 246, 0.2)'\n                    : '0 8px 25px rgba(139, 92, 246, 0.15)'\n                }}\n                whileTap={{ scale: 0.98 }}\n                disabled={isLoading}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <div className=\"flex items-center mb-3\">\n                  <motion.div\n                    className=\"p-2 rounded-lg mr-3\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.2)' : 'rgba(139, 92, 246, 0.1)',\n                      border: `1px solid ${resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.3)' : 'rgba(139, 92, 246, 0.2)'}`\n                    }}\n                    whileHover={{ scale: 1.1, rotate: 5 }}\n                  >\n                    <Icon className=\"w-5 h-5 text-purple-500\" />\n                  </motion.div>\n                  <span\n                    className=\"font-semibold text-sm\"\n                    style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}\n                  >\n                    {prompt.title}\n                  </span>\n                </div>\n                <p\n                  className=\"text-xs leading-relaxed\"\n                  style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#64748b' }}\n                >\n                  {prompt.prompt}\n                </p>\n              </motion.button>\n            )\n          })}\n        </div>\n      </div>\n\n      {/* Chat Interface */}\n      <div className=\"flex-1 flex flex-col\">\n        <div\n          className=\"flex-1 rounded-xl border p-4 mb-4 overflow-hidden\"\n          style={{\n            backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.5)' : 'rgba(255, 255, 255, 0.8)',\n            borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(229, 231, 235, 0.8)',\n            boxShadow: resolvedTheme === 'dark'\n              ? '0 4px 6px rgba(0, 0, 0, 0.1)'\n              : '0 4px 6px rgba(0, 0, 0, 0.05)'\n          }}\n        >\n          <div className=\"h-96 overflow-y-auto space-y-4 mb-4\">\n            {messages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div className={`flex items-start space-x-3 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                  <div\n                    className=\"p-2 rounded-full flex-shrink-0\"\n                    style={{\n                      backgroundColor: message.type === 'user' \n                        ? (resolvedTheme === 'dark' ? '#22c55e' : '#16a34a')\n                        : (resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed')\n                    }}\n                  >\n                    {message.type === 'user' ? (\n                      <User className=\"w-4 h-4 text-white\" />\n                    ) : (\n                      <Bot className=\"w-4 h-4 text-white\" />\n                    )}\n                  </div>\n                  <div\n                    className={`p-4 rounded-2xl ${message.type === 'user' ? 'rounded-tr-md' : 'rounded-tl-md'}`}\n                    style={{\n                      backgroundColor: message.type === 'user'\n                        ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')\n                        : (resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.4)' : 'rgba(243, 244, 246, 0.9)'),\n                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                    }}\n                  >\n                    <p className=\"text-sm whitespace-pre-wrap leading-relaxed\">{message.content}</p>\n                    <p\n                      className=\"text-xs mt-2 opacity-70\"\n                      style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}\n                    >\n                      {formatTime(message.timestamp)}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            ))}\n            \n            {isLoading && (\n              <div className=\"flex justify-start\">\n                <div className=\"flex items-start space-x-3\">\n                  <div\n                    className=\"p-2 rounded-full\"\n                    style={{ backgroundColor: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }}\n                  >\n                    <Bot className=\"w-4 h-4 text-white\" />\n                  </div>\n                  <div\n                    className=\"p-4 rounded-2xl rounded-tl-md flex items-center space-x-3\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.4)' : 'rgba(243, 244, 246, 0.9)'\n                    }}\n                  >\n                    <Loader2 className=\"w-4 h-4 animate-spin\" style={{ color: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }} />\n                    <span className=\"text-sm\" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>\n                      Analyzing your request...\n                    </span>\n                  </div>\n                </div>\n              </div>\n            )}\n            <div ref={messagesEndRef} />\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            <input\n              ref={inputRef}\n              type=\"text\"\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Ask me anything about your store management...\"\n              disabled={isLoading}\n              className=\"flex-1 p-4 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',\n                borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(229, 231, 235, 0.8)',\n                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n              }}\n            />\n            <button\n              onClick={() => sendMessage()}\n              disabled={!inputMessage.trim() || isLoading}\n              className=\"p-4 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              style={{\n                background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n                boxShadow: '0 4px 8px rgba(139, 92, 246, 0.3)'\n              }}\n            >\n              <Send className=\"w-5 h-5 text-white\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAlBA;;;;;AA2BA,MAAM,eAAe;IACnB;QACE,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM,qNAAA,CAAA,YAAS;QACf,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM,+MAAA,CAAA,YAAS;QACf,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM,iOAAA,CAAA,aAAU;QAChB,OAAO;QACP,QAAQ;IACV;CACD;AAEc,SAAS;;IACtB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,KAAK;YACxB;QACF;8BAAG,EAAE;IAEL,MAAM,cAAc,OAAO;QACzB,MAAM,UAAU,kBAAkB,aAAa,IAAI;QACnD,IAAI,CAAC,WAAW,WAAW;QAE3B,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN;YACA,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,WAAW;gBACtC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,YAAqB;oBACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,MAAM;oBACN,SAAS,KAAK,QAAQ;oBACtB,WAAW,IAAI;gBACjB;gBACA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAU;YAC1C,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAwB;gBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,WAAW;4BACb;4BACA,YAAY;gCAAE,OAAO;4BAAK;sCAE1B,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,6LAAC;;8CACC,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,kBAAkB,SAAS,YAAY;oCAAU;8CAClE;;;;;;8CAGD,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,kBAAkB,SAAS,YAAY;oCAAU;8CAClE;;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO,kBAAkB,SAAS,YAAY;wBAAU;;0CAEjE,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAiC;;;;;;;kCAGlD,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,QAAQ;4BACzB,MAAM,OAAO,OAAO,IAAI;4BACxB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS,IAAM,YAAY,OAAO,MAAM;gCACxC,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,2BAA2B;oCACvE,aAAa,kBAAkB,SAAS,6BAA6B;oCACrE,WAAW,kBAAkB,SACzB,iCACA;gCACN;gCACA,YAAY;oCACV,OAAO;oCACP,WAAW,kBAAkB,SACzB,uCACA;gCACN;gCACA,UAAU;oCAAE,OAAO;gCAAK;gCACxB,UAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,OAAO;oDACL,iBAAiB,kBAAkB,SAAS,4BAA4B;oDACxE,QAAQ,CAAC,UAAU,EAAE,kBAAkB,SAAS,4BAA4B,2BAA2B;gDACzG;gDACA,YAAY;oDAAE,OAAO;oDAAK,QAAQ;gDAAE;0DAEpC,cAAA,6LAAC;oDAAK,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,kBAAkB,SAAS,YAAY;gDAAU;0DAEhE,OAAO,KAAK;;;;;;;;;;;;kDAGjB,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,kBAAkB,SAAS,YAAY;wCAAU;kDAEhE,OAAO,MAAM;;;;;;;+BA5CX;;;;;wBAgDX;;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB,kBAAkB,SAAS,0BAA0B;wBACtE,aAAa,kBAAkB,SAAS,6BAA6B;wBACrE,WAAW,kBAAkB,SACzB,iCACA;oBACN;;sCAEA,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;kDAE9E,cAAA,6LAAC;4CAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,IAAI,KAAK,SAAS,qCAAqC,IAAI;;8DAC3H,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,iBAAiB,QAAQ,IAAI,KAAK,SAC7B,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;oDAC9C;8DAEC,QAAQ,IAAI,KAAK,uBAChB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;6EAEhB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAGnB,6LAAC;oDACC,WAAW,CAAC,gBAAgB,EAAE,QAAQ,IAAI,KAAK,SAAS,kBAAkB,iBAAiB;oDAC3F,OAAO;wDACL,iBAAiB,QAAQ,IAAI,KAAK,SAC7B,kBAAkB,SAAS,2BAA2B,2BACtD,kBAAkB,SAAS,2BAA2B;wDAC3D,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;sEAEA,6LAAC;4DAAE,WAAU;sEAA+C,QAAQ,OAAO;;;;;;sEAC3E,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,kBAAkB,SAAS,YAAY;4DAAU;sEAEhE,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;uCAhC9B,QAAQ,EAAE;;;;;gCAuClB,2BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,kBAAkB,SAAS,YAAY;gDAAU;0DAE3E,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,iBAAiB,kBAAkB,SAAS,2BAA2B;gDACzE;;kEAEA,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;wDAAuB,OAAO;4DAAE,OAAO,kBAAkB,SAAS,YAAY;wDAAU;;;;;;kEAC3G,6LAAC;wDAAK,WAAU;wDAAU,OAAO;4DAAE,OAAO,kBAAkB,SAAS,YAAY;wDAAU;kEAAG;;;;;;;;;;;;;;;;;;;;;;;8CAOtG,6LAAC;oCAAI,KAAK;;;;;;;;;;;;sCAGZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,KAAK;oCACL,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,YAAY;oCACZ,aAAY;oCACZ,UAAU;oCACV,WAAU;oCACV,OAAO;wCACL,iBAAiB,kBAAkB,SAAS,YAAY;wCACxD,aAAa,kBAAkB,SAAS,6BAA6B;wCACrE,OAAO,kBAAkB,SAAS,YAAY;oCAChD;;;;;;8CAEF,6LAAC;oCACC,SAAS,IAAM;oCACf,UAAU,CAAC,aAAa,IAAI,MAAM;oCAClC,WAAU;oCACV,OAAO;wCACL,YAAY;wCACZ,WAAW;oCACb;8CAEA,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B;GAnTwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 6462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  color?: 'primary' | 'secondary' | 'white'\n  text?: string\n}\n\nexport default function LoadingSpinner({ \n  size = 'md', \n  color = 'primary', \n  text \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  }\n\n  const colorClasses = {\n    primary: 'border-green-500 border-t-transparent',\n    secondary: 'border-yellow-400 border-t-transparent',\n    white: 'border-white border-t-transparent'\n  }\n\n  return (\n    <div className=\"flex flex-col items-center justify-center space-y-3\">\n      <motion.div\n        className={`${sizeClasses[size]} border-2 ${colorClasses[color]} rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n      />\n      {text && (\n        <motion.p\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.2 }}\n          className=\"text-sm text-gray-600 dark:text-gray-400\"\n        >\n          {text}\n        </motion.p>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,eAAe,EACrC,OAAO,IAAI,EACX,QAAQ,SAAS,EACjB,IAAI,EACgB;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC;gBAC9E,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;YAED,sBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAET;;;;;;;;;;;;AAKX;KAxCwB", "debugId": null}}, {"offset": {"line": 6535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse\">\n            <span className=\"text-white font-bold text-2xl\">R</span>\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            Loading Revantad Store\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Please wait while we prepare your dashboard...\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <span className=\"text-red-600 dark:text-red-400 font-bold text-2xl\">!</span>\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            Access Denied\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Redirecting to login page...\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAuB;;IACtE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAElD,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAoD;;;;;;;;;;;kCAEtE,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,qBAAO;kBAAG;;AACZ;GA/CwB;;QACiB,kIAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 6683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/index.ts"], "sourcesContent": ["// Component exports for better organization and cleaner imports\n// This file serves as a central export point for all components\n\n// Layout Components\nexport { default as AdminHeader } from './AdminHeader'\nexport { default as Sidebar } from './Sidebar'\n\n// Dashboard Components\nexport { default as DashboardStats } from './DashboardStats'\nexport { default as APIGraphing } from './APIGraphing'\n\n// Product Management Components\nexport { default as ProductsSection } from './ProductsSection'\nexport { default as ProductModal } from './ProductModal'\n\n// Debt Management Components\nexport { default as DebtsSection } from './DebtsSection'\nexport { default as DebtModal } from './DebtModal'\n\n// Feature Components\nexport { default as FamilyGallery } from './FamilyGallery'\nexport { default as Calendar } from './Calendar'\nexport { default as History } from './History'\nexport { default as Settings } from './Settings'\nexport { default as AIAssistant } from './AIAssistant'\nexport { default as AISupport } from './AISupport'\n\n// Utility Components\nexport { default as LoadingSpinner } from './LoadingSpinner'\nexport { default as ProtectedRoute } from './ProtectedRoute'\nexport { ThemeProvider } from './ThemeProvider'\n\n// Re-export commonly used types\nexport type { Product, CustomerDebt } from '@/lib/supabase'\n"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,gEAAgE;AAEhE,oBAAoB;;AACpB;AACA;AAEA,uBAAuB;AACvB;AACA;AAEA,gCAAgC;AAChC;AACA;AAEA,6BAA6B;AAC7B;AACA;AAEA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AAEA,qBAAqB;AACrB;AACA;AACA", "debugId": null}}]}