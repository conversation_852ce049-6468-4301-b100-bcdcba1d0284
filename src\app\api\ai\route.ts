import { GoogleGenerativeAI } from '@google/generative-ai'

import { NextRequest, NextResponse } from 'next/server'
import { config } from '@/lib/env'

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(config.ai.geminiApiKey || 'AIzaSyA_rZzSvLvHQ3RksK0gyssYX0c4jNAymB4')

export async function POST(request: NextRequest) {
  try {
    const { message, context } = await request.json()

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      )
    }

    // Get the generative model
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' })

    // Create a comprehensive system prompt for the admin assistant
    const systemPrompt = `You are an AI assistant for Revantad Store, a professional sari-sari store management system. You help store owners and administrators with:

1. **Business Analytics & Insights**: Analyze sales data, inventory trends, customer behavior
2. **Inventory Management**: Product stock levels, reorder suggestions, category optimization
3. **Customer Debt Management**: Payment tracking, debt analysis, customer relationship insights
4. **Financial Planning**: Revenue analysis, expense tracking, profit optimization
5. **Store Operations**: Daily operations, staff management, process improvements
6. **Marketing Strategies**: Customer engagement, promotional ideas, sales growth tactics

**Context about the store:**
- This is a Filipino sari-sari store (neighborhood convenience store)
- The system manages products, customer debts, inventory, and business analytics
- Users are store owners/administrators who need practical business advice
- Focus on actionable insights and practical solutions

**Your personality:**
- Professional but friendly and approachable
- Knowledgeable about Filipino business culture and sari-sari store operations
- Provide specific, actionable advice
- Use clear, easy-to-understand language
- Be encouraging and supportive

**Current context:** ${context || 'General admin dashboard'}

Please provide helpful, relevant responses based on the user's question about their store management needs.`

    // Combine system prompt with user message
    const fullPrompt = `${systemPrompt}\n\nUser Question: ${message}`

    // Generate response
    const result = await model.generateContent(fullPrompt)
    const response = await result.response
    const text = response.text()

    return NextResponse.json({
      success: true,
      response: text,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('AI API Error:', error)
    
    // Handle specific API errors
    if (error instanceof Error) {
      if (error.message.includes('API_KEY')) {
        return NextResponse.json(
          { error: 'AI service configuration error. Please check API key.' },
          { status: 500 }
        )
      }
      
      if (error.message.includes('quota') || error.message.includes('limit')) {
        return NextResponse.json(
          { error: 'AI service quota exceeded. Please try again later.' },
          { status: 429 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to process AI request. Please try again.' },
      { status: 500 }
    )
  }
}

// Handle GET requests for health check
export async function GET() {
  try {
    const hasApiKey = !!config.ai.geminiApiKey || !!process.env.GEMINI_API_KEY
    
    return NextResponse.json({
      status: 'AI service is running',
      configured: hasApiKey,
      timestamp: new Date().toISOString()
    })
  } catch {
    return NextResponse.json(
      { error: 'AI service health check failed' },
      { status: 500 }
    )
  }
}
