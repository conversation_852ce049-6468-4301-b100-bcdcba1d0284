import { NextRequest } from 'next/server'

import {
  successResponse,
  errorResponse,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validateRequestBody,
  validateRequired<PERSON>ields,
  handleDatabaseError,
  parsePaginationParams,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'


// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all products with pagination and filtering
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const { page, limit, offset } = parsePaginationParams(searchParams)

  // Optional filters
  const category = searchParams.get('category')
  const search = searchParams.get('search')
  const lowStock = searchParams.get('lowStock') === 'true'

  let query = supabase
    .from('products')
    .select('*', { count: 'exact' })
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1)

  // Apply filters
  if (category) {
    query = query.eq('category', category)
  }

  if (search) {
    query = query.ilike('name', `%${search}%`)
  }

  if (lowStock) {
    query = query.lt('stock_quantity', 10)
  }

  const { data: products, error, count } = await query

  if (error) {
    handleDatabaseError(error)
  }

  return successResponse({
    products: products || [],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  })
})

// POST - Create new product
export const POST = withErrorHandler(async (request: NextRequest) => {
  const productData = await validateRequestBody(request, (body) => {
    // Validate required fields
    validateRequiredFields(body, ['name', 'net_weight', 'price', 'category'])

    return {
      name: String(body.name).trim(),
      image_url: body.image_url ? String(body.image_url).trim() : null,
      net_weight: String(body.net_weight).trim(),
      price: parseFloat(body.price),
      stock_quantity: parseInt(body.stock_quantity) || 0,
      category: String(body.category).trim(),
    }
  })

  // Additional validation
  if (productData.price < 0) {
    return errorResponse('Price must be a positive number', 400)
  }

  if (productData.stock_quantity < 0) {
    return errorResponse('Stock quantity must be a positive number', 400)
  }

  const { data: product, error } = await supabase
    .from('products')
    .insert([productData])
    .select()
    .single()

  if (error) {
    handleDatabaseError(error)
  }

  return successResponse(product, 'Product created successfully', 201)
})
