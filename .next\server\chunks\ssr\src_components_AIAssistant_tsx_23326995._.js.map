{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/AIAssistant.tsx"], "sourcesContent": ["'use client'\n\nimport { motion, AnimatePresence } from 'framer-motion'\nimport {\n  <PERSON>,\n  Bot,\n  User,\n  Loader2,\n  Sparkles,\n  X,\n  Minimize2,\n  Maximize2,\n  Mic,\n  Square,\n  RotateCcw,\n  Copy,\n  Check\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useRef, useEffect } from 'react'\n\ninterface Message {\n  id: string\n  type: 'user' | 'ai'\n  content: string\n  timestamp: Date\n  isTyping?: boolean\n  reactions?: string[]\n}\n\ninterface AIAssistantProps {\n  context?: string\n}\n\nexport default function AIAssistant({ context = 'dashboard' }: AIAssistantProps) {\n  const { resolvedTheme } = useTheme()\n  const [isOpen, setIsOpen] = useState(false)\n  const [isMinimized, setIsMinimized] = useState(false)\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      type: 'ai',\n      content: 'Hello! I\\'m your AI assistant for Revantad Store. I can help you with business analytics, inventory management, customer debt tracking, and store operations. How can I assist you today?',\n      timestamp: new Date()\n    }\n  ])\n  const [inputMessage, setInputMessage] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [isRecording, setIsRecording] = useState(false)\n  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)\n  const [audioStream, setAudioStream] = useState<MediaStream | null>(null)\n  const [recordingTime, setRecordingTime] = useState(0)\n  const [isProcessingAudio, setIsProcessingAudio] = useState(false)\n\n  // Recording timer effect\n  useEffect(() => {\n    let interval: NodeJS.Timeout\n    if (isRecording) {\n      interval = setInterval(() => {\n        setRecordingTime(prev => prev + 1)\n      }, 1000)\n    } else {\n      setRecordingTime(0)\n    }\n    return () => clearInterval(interval)\n  }, [isRecording])\n\n  // Cleanup audio stream on unmount\n  useEffect(() => {\n    return () => {\n      if (audioStream) {\n        audioStream.getTracks().forEach(track => track.stop())\n      }\n    }\n  }, [audioStream])\n  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const inputRef = useRef<HTMLTextAreaElement>(null)\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  useEffect(() => {\n    if (isOpen && !isMinimized && inputRef.current) {\n      inputRef.current.focus()\n    }\n  }, [isOpen, isMinimized])\n\n  const sendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputMessage.trim(),\n      timestamp: new Date()\n    }\n\n    setMessages(prev => [...prev, userMessage])\n    setInputMessage('')\n    setIsLoading(true)\n\n    // Add typing indicator\n    const typingMessage: Message = {\n      id: 'typing',\n      type: 'ai',\n      content: '',\n      timestamp: new Date(),\n      isTyping: true\n    }\n    setMessages(prev => [...prev, typingMessage])\n\n    try {\n      const response = await fetch('/api/ai', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: userMessage.content,\n          context: context\n        })\n      })\n\n      const data = await response.json()\n\n      // Remove typing indicator\n      setMessages(prev => prev.filter(msg => msg.id !== 'typing'))\n\n      if (data.success) {\n        const aiMessage: Message = {\n          id: (Date.now() + 1).toString(),\n          type: 'ai',\n          content: data.response,\n          timestamp: new Date()\n        }\n        setMessages(prev => [...prev, aiMessage])\n\n        // AI response received successfully\n      } else {\n        throw new Error(data.error || 'Failed to get AI response')\n      }\n    } catch {\n      // Remove typing indicator\n      setMessages(prev => prev.filter(msg => msg.id !== 'typing'))\n\n      const errorMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        type: 'ai',\n        content: 'Sorry, I encountered an error. Please try again later.',\n        timestamp: new Date()\n      }\n      setMessages(prev => [...prev, errorMessage])\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Start voice recording\n  const startRecording = async () => {\n    try {\n      setIsProcessingAudio(true)\n\n      // Request microphone permission\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          sampleRate: 44100\n        }\n      })\n\n      setAudioStream(stream)\n\n      // Create MediaRecorder\n      const recorder = new MediaRecorder(stream, {\n        mimeType: 'audio/webm;codecs=opus'\n      })\n\n      const audioChunks: Blob[] = []\n\n      recorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunks.push(event.data)\n        }\n      }\n\n      recorder.onstop = async () => {\n        const audioBlob = new Blob(audioChunks, { type: 'audio/webm;codecs=opus' })\n        await processAudioToText(audioBlob)\n\n        // Stop all tracks\n        stream.getTracks().forEach(track => track.stop())\n        setAudioStream(null)\n        setIsProcessingAudio(false)\n      }\n\n      setMediaRecorder(recorder)\n      recorder.start()\n      setIsRecording(true)\n      setIsProcessingAudio(false)\n\n    } catch (error) {\n      console.error('Error starting recording:', error)\n      setIsProcessingAudio(false)\n      alert('Unable to access microphone. Please check your permissions.')\n    }\n  }\n\n  // Stop voice recording\n  const stopRecording = () => {\n    if (mediaRecorder && isRecording) {\n      mediaRecorder.stop()\n      setIsRecording(false)\n    }\n  }\n\n  // Process audio to text using Google Speech-to-Text API\n  const processAudioToText = async (audioBlob: Blob) => {\n    try {\n      setIsProcessingAudio(true)\n\n      // Convert audio blob to base64\n      const reader = new FileReader()\n      reader.readAsDataURL(audioBlob)\n\n      reader.onloadend = async () => {\n        try {\n          const base64Audio = reader.result as string\n\n          // Send to our Speech-to-Text API\n          const response = await fetch('/api/speech-to-text', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({\n              audioData: base64Audio,\n              language: 'en-US',\n              sampleRate: 44100\n            })\n          })\n\n          const result = await response.json()\n\n          if (result.success && result.transcript) {\n            // Set the transcribed text in the input field\n            setInputMessage(result.transcript)\n\n            // Professional success feedback\n            if (result.confidence && result.confidence > 0.9) {\n              console.warn(`🎯 Excellent transcription quality: ${(result.confidence * 100).toFixed(1)}%`)\n            } else if (result.confidence && result.confidence > 0.8) {\n              console.warn(`✅ Good transcription quality: ${(result.confidence * 100).toFixed(1)}%`)\n            } else if (result.confidence && result.confidence > 0.7) {\n              console.warn(`⚠️ Moderate transcription quality: ${(result.confidence * 100).toFixed(1)}%`)\n            }\n\n            // Log alternatives for debugging (in production, you might show these to user)\n            if (result.alternatives && result.alternatives.length > 0) {\n              console.warn('📝 Alternative transcriptions available:', result.alternatives)\n            }\n\n            // Add a subtle success indicator\n            console.warn('🔊 Voice message successfully transcribed by Google Speech-to-Text')\n\n          } else {\n            throw new Error(result.error || 'Google Speech-to-Text failed to transcribe audio')\n          }\n\n        } catch (apiError) {\n          console.error('Speech-to-text API error:', apiError)\n          setInputMessage('Sorry, I couldn\\'t understand that. Please try speaking more clearly or type your message.')\n        } finally {\n          setIsProcessingAudio(false)\n        }\n      }\n\n      reader.onerror = () => {\n        console.error('Error reading audio file')\n        setIsProcessingAudio(false)\n        alert('Error reading audio file. Please try again.')\n      }\n\n    } catch (error) {\n      console.error('Error processing audio:', error)\n      setIsProcessingAudio(false)\n      alert('Error processing voice recording. Please try again.')\n    }\n  }\n\n  const copyMessage = async (content: string, messageId: string) => {\n    try {\n      await navigator.clipboard.writeText(content)\n      setCopiedMessageId(messageId)\n      setTimeout(() => setCopiedMessageId(null), 2000)\n    } catch (error) {\n      console.error('Failed to copy message:', error)\n    }\n  }\n\n  const clearChat = () => {\n    setMessages([\n      {\n        id: '1',\n        type: 'ai',\n        content: 'Hello! I\\'m your AI assistant for Revantad Store. I can help you with business analytics, inventory management, customer debt tracking, and store operations. How can I assist you today?',\n        timestamp: new Date()\n      }\n    ])\n  }\n\n\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('en-US', { \n      hour: '2-digit', \n      minute: '2-digit',\n      hour12: true \n    })\n  }\n\n  return (\n    <>\n      {/* AI Assistant Toggle Button - Professional Design */}\n      <motion.div\n        className={`fixed bottom-6 right-6 z-50 ${isOpen ? 'hidden' : 'block'}`}\n        initial={{ scale: 0, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        transition={{ type: 'spring', stiffness: 300, damping: 25, delay: 0.1 }}\n      >\n        <motion.button\n          onClick={() => setIsOpen(true)}\n          className=\"relative group\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          {/* Main Button Container */}\n          <div\n            className=\"relative w-16 h-16 rounded-2xl shadow-xl transition-all duration-300 overflow-hidden\"\n            style={{\n              background: resolvedTheme === 'dark'\n                ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'\n                : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',\n              border: resolvedTheme === 'dark'\n                ? '1px solid rgba(148, 163, 184, 0.2)'\n                : '1px solid rgba(226, 232, 240, 0.8)',\n              boxShadow: resolvedTheme === 'dark'\n                ? '0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05)'\n                : '0 10px 25px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.05)'\n            }}\n          >\n            {/* Gradient Overlay on Hover */}\n            <div\n              className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              style={{\n                background: 'linear-gradient(135deg, rgba(217, 119, 6, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%)'\n              }}\n            />\n\n            {/* AI Icon */}\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <div\n                className=\"p-2 rounded-xl transition-all duration-300 group-hover:scale-110\"\n                style={{\n                  background: 'linear-gradient(135deg, #d97706 0%, #059669 100%)',\n                  boxShadow: '0 4px 12px rgba(217, 119, 6, 0.3)'\n                }}\n              >\n                <Bot className=\"w-6 h-6 text-white\" />\n              </div>\n            </div>\n\n            {/* Status Indicator */}\n            <div className=\"absolute top-2 right-2\">\n              <div className=\"w-3 h-3 bg-emerald-500 rounded-full animate-pulse shadow-sm\">\n                <div className=\"absolute inset-0 bg-emerald-500 rounded-full animate-ping opacity-75\" />\n              </div>\n            </div>\n\n            {/* Notification Badge */}\n            <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-amber-600 rounded-full flex items-center justify-center shadow-lg\">\n              <span className=\"text-xs font-bold text-white\">!</span>\n            </div>\n          </div>\n\n          {/* Professional Tooltip */}\n          <div\n            className=\"absolute right-full mr-4 top-1/2 transform -translate-y-1/2 px-4 py-2 rounded-xl text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap shadow-lg\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#ffffff',\n              color: resolvedTheme === 'dark' ? '#f1f5f9' : '#0f172a',\n              border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(226, 232, 240, 0.8)',\n              boxShadow: resolvedTheme === 'dark'\n                ? '0 8px 25px rgba(0, 0, 0, 0.4)'\n                : '0 8px 25px rgba(0, 0, 0, 0.15)'\n            }}\n          >\n            <div className=\"flex items-center space-x-2\">\n              <Sparkles className=\"w-4 h-4 text-emerald-500\" />\n              <span>AI Assistant</span>\n            </div>\n            {/* Tooltip Arrow */}\n            <div\n              className=\"absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0\"\n              style={{\n                borderTop: '6px solid transparent',\n                borderBottom: '6px solid transparent',\n                borderLeft: `6px solid ${resolvedTheme === 'dark' ? '#0f172a' : '#ffffff'}`\n              }}\n            />\n          </div>\n        </motion.button>\n      </motion.div>\n\n      {/* AI Chat Interface - Professional Design */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            className={`fixed bottom-6 right-6 z-50 rounded-2xl shadow-2xl overflow-hidden backdrop-blur-md flex flex-col ${\n              isMinimized ? 'w-80 h-[60px]' : 'w-[420px] h-[550px]'\n            }`}\n            style={{\n              backgroundColor: resolvedTheme === 'dark'\n                ? 'rgba(15, 23, 42, 0.95)'\n                : 'rgba(255, 255, 255, 0.95)',\n              border: resolvedTheme === 'dark'\n                ? '1px solid rgba(217, 119, 6, 0.3)'\n                : '1px solid rgba(16, 185, 129, 0.3)',\n              boxShadow: resolvedTheme === 'dark'\n                ? '0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(217, 119, 6, 0.2)'\n                : '0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(16, 185, 129, 0.2)'\n            }}\n            initial={{ opacity: 0, scale: 0.9, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 20 }}\n            transition={{ duration: 0.3, ease: \"easeOut\" }}\n          >\n            {/* Professional Header - Always Visible */}\n            <div\n              className={`flex items-center justify-between px-4 flex-shrink-0 ${\n                isMinimized ? 'py-2 border-0' : 'py-3 border-b'\n              }`}\n              style={{\n                background: resolvedTheme === 'dark'\n                  ? 'linear-gradient(135deg, rgba(120, 53, 15, 0.9) 0%, rgba(6, 78, 59, 0.8) 100%)'\n                  : 'linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(16, 185, 129, 0.15) 100%)',\n                borderColor: resolvedTheme === 'dark' ? 'rgba(217, 119, 6, 0.3)' : 'rgba(16, 185, 129, 0.3)',\n                height: isMinimized ? '60px' : 'auto',\n                minHeight: '60px'\n              }}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <div\n                  className={`rounded-xl ${isMinimized ? 'p-1.5' : 'p-2'}`}\n                  style={{\n                    background: 'linear-gradient(135deg, #d97706 0%, #059669 100%)',\n                    boxShadow: '0 4px 12px rgba(217, 119, 6, 0.3)'\n                  }}\n                >\n                  <Bot className={`text-white ${isMinimized ? 'w-3.5 h-3.5' : 'w-4 h-4'}`} />\n                </div>\n                {!isMinimized && (\n                  <div>\n                    <h3\n                      className=\"font-semibold text-sm\"\n                      style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#0f172a' }}\n                    >\n                      AI Assistant\n                    </h3>\n                    <div className=\"flex items-center space-x-2\">\n                      <div className=\"w-2 h-2 bg-emerald-500 rounded-full animate-pulse\" />\n                      <p\n                        className=\"text-xs\"\n                        style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}\n                      >\n                        Online\n                      </p>\n                    </div>\n                  </div>\n                )}\n                {isMinimized && (\n                  <div className=\"flex items-center space-x-2\">\n                    <h3\n                      className=\"font-semibold text-sm\"\n                      style={{ color: resolvedTheme === 'dark' ? '#f1f5f9' : '#0f172a' }}\n                    >\n                      AI Assistant\n                    </h3>\n                    <div className=\"w-2 h-2 bg-emerald-500 rounded-full animate-pulse\" />\n                  </div>\n                )}\n              </div>\n              <div className={`flex items-center ${isMinimized ? 'space-x-0.5' : 'space-x-1'}`}>\n                {/* Voice Recording Button */}\n                {!isMinimized && (\n                  <div className=\"relative group\">\n                    <motion.button\n                      onClick={isRecording ? stopRecording : startRecording}\n                      disabled={isProcessingAudio}\n                      className={`p-2 rounded-lg transition-all duration-200 relative ${\n                        isRecording\n                          ? 'bg-amber-50 dark:bg-amber-900/20 hover:bg-amber-100 dark:hover:bg-amber-900/30'\n                          : isProcessingAudio\n                          ? 'bg-emerald-50 dark:bg-emerald-900/20'\n                          : 'hover:bg-amber-50 dark:hover:bg-amber-900/20'\n                      } ${isProcessingAudio ? 'cursor-not-allowed opacity-70' : ''}`}\n                      title={\n                        isProcessingAudio\n                          ? 'Processing with Google Speech-to-Text...'\n                          : isRecording\n                          ? 'Recording voice message - Click to stop'\n                          : 'Voice Recording powered by Google'\n                      }\n                      whileHover={!isProcessingAudio ? { scale: 1.05 } : {}}\n                      whileTap={!isProcessingAudio ? { scale: 0.95 } : {}}\n                    >\n                    {isProcessingAudio ? (\n                      <Loader2\n                        className=\"w-4 h-4 animate-spin\"\n                        style={{ color: resolvedTheme === 'dark' ? '#10b981' : '#059669' }}\n                      />\n                    ) : isRecording ? (\n                      <div className=\"relative\">\n                        <Square\n                          className=\"w-4 h-4 fill-current\"\n                          style={{ color: resolvedTheme === 'dark' ? '#d97706' : '#b45309' }}\n                        />\n                        {/* Recording pulse animation */}\n                        <motion.div\n                          className=\"absolute -inset-1 rounded-full border-2 border-amber-500\"\n                          animate={{\n                            scale: [1, 1.3, 1],\n                            opacity: [0.7, 0.3, 0.7]\n                          }}\n                          transition={{\n                            duration: 1,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                          }}\n                        />\n                      </div>\n                    ) : (\n                      <Mic\n                        className=\"w-4 h-4\"\n                        style={{ color: resolvedTheme === 'dark' ? '#10b981' : '#059669' }}\n                      />\n                    )}\n\n                    {/* Recording status indicator */}\n                    <div\n                      className={`absolute -top-1 -right-1 w-2 h-2 rounded-full transition-all duration-200 ${\n                        isRecording\n                          ? 'bg-amber-600 animate-pulse shadow-sm'\n                          : isProcessingAudio\n                          ? 'bg-emerald-500 shadow-sm'\n                          : 'bg-amber-400 dark:bg-amber-600'\n                      }`}\n                    />\n                  </motion.button>\n\n                  {/* Voice Recording Tooltip */}\n                  <div\n                    className=\"absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 px-3 py-2 rounded-lg text-xs font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap shadow-lg z-10\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#ffffff',\n                      color: resolvedTheme === 'dark' ? '#f1f5f9' : '#0f172a',\n                      border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(226, 232, 240, 0.8)',\n                      boxShadow: resolvedTheme === 'dark'\n                        ? '0 8px 25px rgba(0, 0, 0, 0.4)'\n                        : '0 8px 25px rgba(0, 0, 0, 0.15)'\n                    }}\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <div className={`w-2 h-2 rounded-full ${\n                        isRecording ? 'bg-amber-600 animate-pulse' :\n                        isProcessingAudio ? 'bg-emerald-500' : 'bg-amber-400'\n                      }`} />\n                      <span>\n                        {isProcessingAudio\n                          ? 'Google Speech-to-Text Processing...'\n                          : isRecording\n                          ? `Recording ${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`\n                          : 'Voice Recording (Google Powered)'\n                        }\n                      </span>\n                    </div>\n                    {/* Tooltip Arrow */}\n                    <div\n                      className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0\"\n                      style={{\n                        borderLeft: '6px solid transparent',\n                        borderRight: '6px solid transparent',\n                        borderTop: `6px solid ${resolvedTheme === 'dark' ? '#0f172a' : '#ffffff'}`\n                      }}\n                    />\n                  </div>\n                </div>\n                )}\n\n                {/* Control Buttons */}\n                {!isMinimized && (\n                  <>\n                    <button\n                      onClick={clearChat}\n                      className=\"p-2 rounded-lg transition-all duration-200 hover:bg-amber-50 dark:hover:bg-amber-900/20\"\n                      title=\"Clear chat\"\n                    >\n                      <RotateCcw className=\"w-4 h-4\" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />\n                    </button>\n                  </>\n                )}\n\n                {/* Always visible buttons */}\n                <button\n                  onClick={() => setIsMinimized(!isMinimized)}\n                  className={`rounded-lg transition-all duration-200 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 ${\n                    isMinimized ? 'p-1.5' : 'p-2'\n                  }`}\n                  title={isMinimized ? 'Maximize' : 'Minimize'}\n                >\n                  {isMinimized ? (\n                    <Maximize2 className={`${isMinimized ? 'w-3.5 h-3.5' : 'w-4 h-4'}`} style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />\n                  ) : (\n                    <Minimize2 className={`${isMinimized ? 'w-3.5 h-3.5' : 'w-4 h-4'}`} style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />\n                  )}\n                </button>\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className={`rounded-lg transition-all duration-200 hover:bg-amber-100 dark:hover:bg-amber-900/20 ${\n                    isMinimized ? 'p-1.5' : 'p-2'\n                  }`}\n                  title=\"Close chat\"\n                >\n                  <X className={`hover:text-amber-600 ${isMinimized ? 'w-3.5 h-3.5' : 'w-4 h-4'}`} style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />\n                </button>\n              </div>\n            </div>\n\n            {!isMinimized && (\n              <>\n                {/* Messages Area - Professional Design */}\n                <div className=\"flex-1 overflow-y-auto p-3 space-y-3 h-[380px] scrollbar-thin scrollbar-thumb-amber-300 dark:scrollbar-thumb-amber-600 scrollbar-track-transparent\">\n                  {messages.map((message) => {\n                    if (message.isTyping) {\n                      return (\n                        <motion.div\n                          key={message.id}\n                          className=\"flex justify-start\"\n                          initial={{ opacity: 0, y: 20 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          exit={{ opacity: 0, y: -20 }}\n                        >\n                          <div className=\"flex items-start space-x-2\">\n                            <div\n                              className=\"p-2 rounded-full\"\n                              style={{ backgroundColor: resolvedTheme === 'dark' ? '#059669' : '#10b981' }}\n                            >\n                              <Bot className=\"w-4 h-4 text-white\" />\n                            </div>\n                            <div\n                              className=\"p-3 rounded-2xl rounded-tl-md flex items-center space-x-2\"\n                              style={{\n                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)'\n                              }}\n                            >\n                              <div className=\"flex space-x-1\">\n                                <motion.div\n                                  className=\"w-2 h-2 rounded-full\"\n                                  style={{ backgroundColor: resolvedTheme === 'dark' ? '#059669' : '#10b981' }}\n                                  animate={{ scale: [1, 1.2, 1] }}\n                                  transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}\n                                />\n                                <motion.div\n                                  className=\"w-2 h-2 rounded-full\"\n                                  style={{ backgroundColor: resolvedTheme === 'dark' ? '#059669' : '#10b981' }}\n                                  animate={{ scale: [1, 1.2, 1] }}\n                                  transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}\n                                />\n                                <motion.div\n                                  className=\"w-2 h-2 rounded-full\"\n                                  style={{ backgroundColor: resolvedTheme === 'dark' ? '#059669' : '#10b981' }}\n                                  animate={{ scale: [1, 1.2, 1] }}\n                                  transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}\n                                />\n                              </div>\n                              <span className=\"text-sm\" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>\n                                AI is thinking...\n                              </span>\n                            </div>\n                          </div>\n                        </motion.div>\n                      )\n                    }\n\n                    return (\n                      <motion.div\n                        key={message.id}\n                        className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} group`}\n                        initial={{ opacity: 0, y: 20, scale: 0.95 }}\n                        animate={{ opacity: 1, y: 0, scale: 1 }}\n                        transition={{ duration: 0.3, type: 'spring', stiffness: 200 }}\n                      >\n                        <div className={`flex items-start space-x-2 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                          <motion.div\n                            className=\"p-2 rounded-full flex-shrink-0 relative overflow-hidden\"\n                            style={{\n                              backgroundColor: message.type === 'user'\n                                ? (resolvedTheme === 'dark' ? '#22c55e' : '#16a34a')\n                                : (resolvedTheme === 'dark' ? '#059669' : '#10b981')\n                            }}\n                            whileHover={{ scale: 1.1 }}\n                          >\n                            {/* Shimmer effect */}\n                            <div\n                              className=\"absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-300\"\n                              style={{\n                                background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)',\n                                transform: 'translateX(-100%)',\n                                animation: 'shimmer 1.5s infinite'\n                              }}\n                            />\n                            {message.type === 'user' ? (\n                              <User className=\"w-4 h-4 text-white relative z-10\" />\n                            ) : (\n                              <Bot className=\"w-4 h-4 text-white relative z-10\" />\n                            )}\n                          </motion.div>\n                          <div className=\"relative\">\n                            <motion.div\n                              className={`p-3 rounded-2xl ${message.type === 'user' ? 'rounded-tr-md' : 'rounded-tl-md'} relative overflow-hidden`}\n                              style={{\n                                backgroundColor: message.type === 'user'\n                                  ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')\n                                  : (resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)'),\n                                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',\n                                border: `1px solid ${message.type === 'user'\n                                  ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.3)' : 'rgba(34, 197, 94, 0.2)')\n                                  : (resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.3)' : 'rgba(139, 92, 246, 0.2)')}`\n                              }}\n                              whileHover={{ scale: 1.02 }}\n                            >\n                              <p className=\"text-sm whitespace-pre-wrap leading-relaxed\">{message.content}</p>\n                              <div className=\"flex items-center justify-between mt-2\">\n                                <p\n                                  className=\"text-xs opacity-70\"\n                                  style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}\n                                >\n                                  {formatTime(message.timestamp)}\n                                </p>\n                                {message.type === 'ai' && (\n                                  <button\n                                    onClick={() => copyMessage(message.content, message.id)}\n                                    className=\"opacity-0 group-hover:opacity-100 p-1 rounded transition-all duration-200 hover:bg-emerald-100 dark:hover:bg-emerald-900/20\"\n                                    title=\"Copy message\"\n                                  >\n                                    {copiedMessageId === message.id ? (\n                                      <Check className=\"w-3 h-3 text-emerald-500\" />\n                                    ) : (\n                                      <Copy className=\"w-3 h-3\" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />\n                                    )}\n                                  </button>\n                                )}\n                              </div>\n                            </motion.div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    )\n                  })}\n\n                  <div ref={messagesEndRef} />\n                </div>\n\n                {/* Professional Input Section */}\n                <div\n                  className=\"p-4 border-t\"\n                  style={{\n                    borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.2)' : 'rgba(226, 232, 240, 0.8)',\n                    background: resolvedTheme === 'dark'\n                      ? 'rgba(15, 23, 42, 0.8)'\n                      : 'rgba(248, 250, 252, 0.8)'\n                  }}\n                >\n                  {/* Voice Recording Status */}\n                  {(isRecording || isProcessingAudio) && (\n                    <motion.div\n                      className=\"mb-3 p-3 rounded-lg border\"\n                      style={{\n                        backgroundColor: isRecording\n                          ? (resolvedTheme === 'dark' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(254, 226, 226, 0.8)')\n                          : (resolvedTheme === 'dark' ? 'rgba(251, 191, 36, 0.1)' : 'rgba(254, 243, 199, 0.8)'),\n                        borderColor: isRecording\n                          ? (resolvedTheme === 'dark' ? 'rgba(239, 68, 68, 0.3)' : 'rgba(239, 68, 68, 0.3)')\n                          : (resolvedTheme === 'dark' ? 'rgba(251, 191, 36, 0.3)' : 'rgba(251, 191, 36, 0.3)')\n                      }}\n                      initial={{ opacity: 0, y: -10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: -10 }}\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"flex items-center space-x-2\">\n                          {isRecording ? (\n                            <motion.div\n                              className=\"w-3 h-3 bg-amber-600 rounded-full\"\n                              animate={{ scale: [1, 1.2, 1] }}\n                              transition={{ duration: 1, repeat: Infinity }}\n                            />\n                          ) : (\n                            <Loader2 className=\"w-4 h-4 animate-spin text-emerald-500\" />\n                          )}\n                          <span\n                            className=\"text-sm font-medium\"\n                            style={{\n                              color: isRecording\n                                ? (resolvedTheme === 'dark' ? '#d97706' : '#b45309')\n                                : (resolvedTheme === 'dark' ? '#10b981' : '#059669')\n                            }}\n                          >\n                            {isRecording\n                              ? `🎙️ Recording... ${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`\n                              : '🔄 Google Speech-to-Text Processing...'\n                            }\n                          </span>\n                        </div>\n                        {isRecording && (\n                          <button\n                            onClick={stopRecording}\n                            className=\"px-3 py-1 text-xs rounded-lg bg-amber-600 hover:bg-amber-700 text-white transition-colors duration-200\"\n                          >\n                            Stop\n                          </button>\n                        )}\n                      </div>\n                    </motion.div>\n                  )}\n                  <div className=\"flex items-end space-x-3\">\n                    <div className=\"flex-1 relative\">\n                      <div className=\"relative\">\n                        <textarea\n                          ref={inputRef}\n                          value={inputMessage}\n                          onChange={(e) => {\n                            setInputMessage(e.target.value)\n                            // Auto-resize functionality\n                            const target = e.target as HTMLTextAreaElement\n                            target.style.height = 'auto'\n                            const scrollHeight = target.scrollHeight\n                            const newHeight = Math.min(Math.max(scrollHeight, 44), 120)\n                            target.style.height = newHeight + 'px'\n                          }}\n                          onKeyDown={(e) => {\n                            if (e.key === 'Enter' && !e.shiftKey) {\n                              e.preventDefault()\n                              sendMessage()\n                            }\n                          }}\n                          placeholder=\"Ask me anything about your store...\"\n                          disabled={isLoading}\n                          rows={1}\n                          className=\"w-full px-4 py-3 pr-12 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 resize-none overflow-y-auto text-sm\"\n                          style={{\n                            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                            borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(226, 232, 240, 0.8)',\n                            color: resolvedTheme === 'dark' ? '#f1f5f9' : '#0f172a',\n                            minHeight: '44px',\n                            maxHeight: '120px',\n                            lineHeight: '1.5'\n                          }}\n                        />\n\n                        {/* Character count */}\n                        <div\n                          className=\"absolute bottom-2 right-12 text-xs px-2 py-1 rounded\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#64748b' : '#94a3b8',\n                            backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.8)' : 'rgba(255, 255, 255, 0.8)'\n                          }}\n                        >\n                          {inputMessage.length}\n                        </div>\n                      </div>\n                    </div>\n\n                    <motion.button\n                      onClick={sendMessage}\n                      disabled={!inputMessage.trim() || isLoading}\n                      className=\"p-3 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-emerald-500 flex-shrink-0\"\n                      style={{\n                        background: !inputMessage.trim() || isLoading\n                          ? (resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.6)' : 'rgba(156, 163, 175, 0.6)')\n                          : 'linear-gradient(135deg, #d97706 0%, #059669 100%)',\n                        boxShadow: !inputMessage.trim() || isLoading\n                          ? 'none'\n                          : '0 4px 12px rgba(217, 119, 6, 0.3)',\n                        height: '44px',\n                        width: '44px'\n                      }}\n                      whileHover={!inputMessage.trim() || isLoading ? {} : { scale: 1.05 }}\n                      whileTap={!inputMessage.trim() || isLoading ? {} : { scale: 0.95 }}\n                    >\n                      <div className=\"flex items-center justify-center\">\n                        {isLoading ? (\n                          <Loader2 className=\"w-4 h-4 text-white animate-spin\" />\n                        ) : (\n                          <Send className=\"w-4 h-4 text-white\" />\n                        )}\n                      </div>\n                    </motion.button>\n                  </div>\n\n                  {/* Quick Actions & Google Branding */}\n                  {!inputMessage && (\n                    <div className=\"mt-3\">\n                      <div className=\"flex flex-wrap gap-2 mb-2\">\n                        {['Sales trends', 'Inventory tips', 'Debt analysis'].map((suggestion) => (\n                          <button\n                            key={suggestion}\n                            onClick={() => setInputMessage(`Show me ${suggestion.toLowerCase()}`)}\n                            className=\"px-3 py-1 text-xs rounded-lg transition-colors duration-200\"\n                            style={{\n                              backgroundColor: resolvedTheme === 'dark' ? 'rgba(16, 185, 129, 0.1)' : 'rgba(16, 185, 129, 0.1)',\n                              color: resolvedTheme === 'dark' ? '#34d399' : '#059669',\n                              border: resolvedTheme === 'dark' ? '1px solid rgba(16, 185, 129, 0.2)' : '1px solid rgba(16, 185, 129, 0.2)'\n                            }}\n                          >\n                            {suggestion}\n                          </button>\n                        ))}\n                      </div>\n\n                      {/* Google Speech-to-Text Branding */}\n                      <div className=\"flex items-center justify-center space-x-2 text-xs opacity-60\">\n                        <Mic className=\"w-3 h-3\" />\n                        <span style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}>\n                          Voice powered by Google Speech-to-Text\n                        </span>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </>\n            )}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AAnBA;;;;;;AAkCe,SAAS,YAAY,EAAE,UAAU,WAAW,EAAoB;IAC7E,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QACJ,IAAI,aAAa;YACf,WAAW,YAAY;gBACrB,iBAAiB,CAAA,OAAQ,OAAO;YAClC,GAAG;QACL,OAAO;YACL,iBAAiB;QACnB;QACA,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAY;IAEhB,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,aAAa;gBACf,YAAY,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YACrD;QACF;IACF,GAAG;QAAC;KAAY;IAChB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAE7C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,CAAC,eAAe,SAAS,OAAO,EAAE;YAC9C,SAAS,OAAO,CAAC,KAAK;QACxB;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,cAAc;QAClB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS,aAAa,IAAI;YAC1B,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,uBAAuB;QACvB,MAAM,gBAAyB;YAC7B,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI;YACf,UAAU;QACZ;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAc;QAE5C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,WAAW;gBACtC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,YAAY,OAAO;oBAC5B,SAAS;gBACX;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YAElD,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,YAAqB;oBACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,MAAM;oBACN,SAAS,KAAK,QAAQ;oBACtB,WAAW,IAAI;gBACjB;gBACA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAU;YAExC,oCAAoC;YACtC,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAM;YACN,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YAElD,MAAM,eAAwB;gBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,wBAAwB;IACxB,MAAM,iBAAiB;QACrB,IAAI;YACF,qBAAqB;YAErB,gCAAgC;YAChC,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBACL,kBAAkB;oBAClB,kBAAkB;oBAClB,YAAY;gBACd;YACF;YAEA,eAAe;YAEf,uBAAuB;YACvB,MAAM,WAAW,IAAI,cAAc,QAAQ;gBACzC,UAAU;YACZ;YAEA,MAAM,cAAsB,EAAE;YAE9B,SAAS,eAAe,GAAG,CAAC;gBAC1B,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG;oBACvB,YAAY,IAAI,CAAC,MAAM,IAAI;gBAC7B;YACF;YAEA,SAAS,MAAM,GAAG;gBAChB,MAAM,YAAY,IAAI,KAAK,aAAa;oBAAE,MAAM;gBAAyB;gBACzE,MAAM,mBAAmB;gBAEzB,kBAAkB;gBAClB,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;gBAC9C,eAAe;gBACf,qBAAqB;YACvB;YAEA,iBAAiB;YACjB,SAAS,KAAK;YACd,eAAe;YACf,qBAAqB;QAEvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,qBAAqB;YACrB,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,gBAAgB;QACpB,IAAI,iBAAiB,aAAa;YAChC,cAAc,IAAI;YAClB,eAAe;QACjB;IACF;IAEA,wDAAwD;IACxD,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,qBAAqB;YAErB,+BAA+B;YAC/B,MAAM,SAAS,IAAI;YACnB,OAAO,aAAa,CAAC;YAErB,OAAO,SAAS,GAAG;gBACjB,IAAI;oBACF,MAAM,cAAc,OAAO,MAAM;oBAEjC,iCAAiC;oBACjC,MAAM,WAAW,MAAM,MAAM,uBAAuB;wBAClD,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB,WAAW;4BACX,UAAU;4BACV,YAAY;wBACd;oBACF;oBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;oBAElC,IAAI,OAAO,OAAO,IAAI,OAAO,UAAU,EAAE;wBACvC,8CAA8C;wBAC9C,gBAAgB,OAAO,UAAU;wBAEjC,gCAAgC;wBAChC,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,GAAG,KAAK;4BAChD,QAAQ,IAAI,CAAC,CAAC,oCAAoC,EAAE,CAAC,OAAO,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wBAC7F,OAAO,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,GAAG,KAAK;4BACvD,QAAQ,IAAI,CAAC,CAAC,8BAA8B,EAAE,CAAC,OAAO,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wBACvF,OAAO,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,GAAG,KAAK;4BACvD,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,CAAC,OAAO,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wBAC5F;wBAEA,+EAA+E;wBAC/E,IAAI,OAAO,YAAY,IAAI,OAAO,YAAY,CAAC,MAAM,GAAG,GAAG;4BACzD,QAAQ,IAAI,CAAC,4CAA4C,OAAO,YAAY;wBAC9E;wBAEA,iCAAiC;wBACjC,QAAQ,IAAI,CAAC;oBAEf,OAAO;wBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;oBAClC;gBAEF,EAAE,OAAO,UAAU;oBACjB,QAAQ,KAAK,CAAC,6BAA6B;oBAC3C,gBAAgB;gBAClB,SAAU;oBACR,qBAAqB;gBACvB;YACF;YAEA,OAAO,OAAO,GAAG;gBACf,QAAQ,KAAK,CAAC;gBACd,qBAAqB;gBACrB,MAAM;YACR;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,qBAAqB;YACrB,MAAM;QACR;IACF;IAEA,MAAM,cAAc,OAAO,SAAiB;QAC1C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,mBAAmB;YACnB,WAAW,IAAM,mBAAmB,OAAO;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,YAAY;QAChB,YAAY;YACV;gBACE,IAAI;gBACJ,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;SACD;IACH;IAIA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE;;0BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,4BAA4B,EAAE,SAAS,WAAW,SAAS;gBACvE,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;oBAAI,OAAO;gBAAI;0BAEtE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS,IAAM,UAAU;oBACzB,WAAU;oBACV,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;;sCAGxB,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY,kBAAkB,SAC1B,sDACA;gCACJ,QAAQ,kBAAkB,SACtB,uCACA;gCACJ,WAAW,kBAAkB,SACzB,wEACA;4BACN;;8CAGA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,YAAY;oCACd;;;;;;8CAIF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,YAAY;4CACZ,WAAW;wCACb;kDAEA,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAKnB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;8CAKnB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;;;;;;;sCAKnD,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAAS,YAAY;gCACxD,OAAO,kBAAkB,SAAS,YAAY;gCAC9C,QAAQ,kBAAkB,SAAS,uCAAuC;gCAC1E,WAAW,kBAAkB,SACzB,kCACA;4BACN;;8CAEA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;8CAGR,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,WAAW;wCACX,cAAc;wCACd,YAAY,CAAC,UAAU,EAAE,kBAAkB,SAAS,YAAY,WAAW;oCAC7E;;;;;;;;;;;;;;;;;;;;;;;0BAOR,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,CAAC,kGAAkG,EAC5G,cAAc,kBAAkB,uBAChC;oBACF,OAAO;wBACL,iBAAiB,kBAAkB,SAC/B,2BACA;wBACJ,QAAQ,kBAAkB,SACtB,qCACA;wBACJ,WAAW,kBAAkB,SACzB,qEACA;oBACN;oBACA,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAG7C,8OAAC;4BACC,WAAW,CAAC,qDAAqD,EAC/D,cAAc,kBAAkB,iBAChC;4BACF,OAAO;gCACL,YAAY,kBAAkB,SAC1B,kFACA;gCACJ,aAAa,kBAAkB,SAAS,2BAA2B;gCACnE,QAAQ,cAAc,SAAS;gCAC/B,WAAW;4BACb;;8CAEA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAW,CAAC,WAAW,EAAE,cAAc,UAAU,OAAO;4CACxD,OAAO;gDACL,YAAY;gDACZ,WAAW;4CACb;sDAEA,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAW,CAAC,WAAW,EAAE,cAAc,gBAAgB,WAAW;;;;;;;;;;;wCAExE,CAAC,6BACA,8OAAC;;8DACC,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,kBAAkB,SAAS,YAAY;oDAAU;8DAClE;;;;;;8DAGD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,kBAAkB,SAAS,YAAY;4DAAU;sEAClE;;;;;;;;;;;;;;;;;;wCAMN,6BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,kBAAkB,SAAS,YAAY;oDAAU;8DAClE;;;;;;8DAGD,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAIrB,8OAAC;oCAAI,WAAW,CAAC,kBAAkB,EAAE,cAAc,gBAAgB,aAAa;;wCAE7E,CAAC,6BACA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,SAAS,cAAc,gBAAgB;oDACvC,UAAU;oDACV,WAAW,CAAC,oDAAoD,EAC9D,cACI,mFACA,oBACA,yCACA,+CACL,CAAC,EAAE,oBAAoB,kCAAkC,IAAI;oDAC9D,OACE,oBACI,6CACA,cACA,4CACA;oDAEN,YAAY,CAAC,oBAAoB;wDAAE,OAAO;oDAAK,IAAI,CAAC;oDACpD,UAAU,CAAC,oBAAoB;wDAAE,OAAO;oDAAK,IAAI,CAAC;;wDAEnD,kCACC,8OAAC,iNAAA,CAAA,UAAO;4DACN,WAAU;4DACV,OAAO;gEAAE,OAAO,kBAAkB,SAAS,YAAY;4DAAU;;;;;mEAEjE,4BACF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEACL,WAAU;oEACV,OAAO;wEAAE,OAAO,kBAAkB,SAAS,YAAY;oEAAU;;;;;;8EAGnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAU;oEACV,SAAS;wEACP,OAAO;4EAAC;4EAAG;4EAAK;yEAAE;wEAClB,SAAS;4EAAC;4EAAK;4EAAK;yEAAI;oEAC1B;oEACA,YAAY;wEACV,UAAU;wEACV,QAAQ;wEACR,MAAM;oEACR;;;;;;;;;;;iFAIJ,8OAAC,gMAAA,CAAA,MAAG;4DACF,WAAU;4DACV,OAAO;gEAAE,OAAO,kBAAkB,SAAS,YAAY;4DAAU;;;;;;sEAKrE,8OAAC;4DACC,WAAW,CAAC,0EAA0E,EACpF,cACI,yCACA,oBACA,6BACA,kCACJ;;;;;;;;;;;;8DAKN,8OAAC;oDACC,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;wDACxD,OAAO,kBAAkB,SAAS,YAAY;wDAC9C,QAAQ,kBAAkB,SAAS,uCAAuC;wDAC1E,WAAW,kBAAkB,SACzB,kCACA;oDACN;;sEAEA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAW,CAAC,qBAAqB,EACpC,cAAc,+BACd,oBAAoB,mBAAmB,gBACvC;;;;;;8EACF,8OAAC;8EACE,oBACG,wCACA,cACA,CAAC,UAAU,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM,GACjG;;;;;;;;;;;;sEAKR,8OAAC;4DACC,WAAU;4DACV,OAAO;gEACL,YAAY;gEACZ,aAAa;gEACb,WAAW,CAAC,UAAU,EAAE,kBAAkB,SAAS,YAAY,WAAW;4DAC5E;;;;;;;;;;;;;;;;;;wCAOL,CAAC,6BACA;sDACE,cAAA,8OAAC;gDACC,SAAS;gDACT,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,OAAO;wDAAE,OAAO,kBAAkB,SAAS,YAAY;oDAAU;;;;;;;;;;;;sDAMtG,8OAAC;4CACC,SAAS,IAAM,eAAe,CAAC;4CAC/B,WAAW,CAAC,wFAAwF,EAClG,cAAc,UAAU,OACxB;4CACF,OAAO,cAAc,aAAa;sDAEjC,4BACC,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAW,GAAG,cAAc,gBAAgB,WAAW;gDAAE,OAAO;oDAAE,OAAO,kBAAkB,SAAS,YAAY;gDAAU;;;;;qEAErI,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAW,GAAG,cAAc,gBAAgB,WAAW;gDAAE,OAAO;oDAAE,OAAO,kBAAkB,SAAS,YAAY;gDAAU;;;;;;;;;;;sDAGzI,8OAAC;4CACC,SAAS,IAAM,UAAU;4CACzB,WAAW,CAAC,qFAAqF,EAC/F,cAAc,UAAU,OACxB;4CACF,OAAM;sDAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAW,CAAC,qBAAqB,EAAE,cAAc,gBAAgB,WAAW;gDAAE,OAAO;oDAAE,OAAO,kBAAkB,SAAS,YAAY;gDAAU;;;;;;;;;;;;;;;;;;;;;;;wBAKvJ,CAAC,6BACA;;8CAEE,8OAAC;oCAAI,WAAU;;wCACZ,SAAS,GAAG,CAAC,CAAC;4CACb,IAAI,QAAQ,QAAQ,EAAE;gDACpB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,MAAM;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;8DAE3B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,kBAAkB,SAAS,YAAY;gEAAU;0EAE3E,cAAA,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEACC,WAAU;gEACV,OAAO;oEACL,iBAAiB,kBAAkB,SAAS,2BAA2B;gEACzE;;kFAEA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gFACT,WAAU;gFACV,OAAO;oFAAE,iBAAiB,kBAAkB,SAAS,YAAY;gFAAU;gFAC3E,SAAS;oFAAE,OAAO;wFAAC;wFAAG;wFAAK;qFAAE;gFAAC;gFAC9B,YAAY;oFAAE,UAAU;oFAAK,QAAQ;oFAAU,OAAO;gFAAE;;;;;;0FAE1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gFACT,WAAU;gFACV,OAAO;oFAAE,iBAAiB,kBAAkB,SAAS,YAAY;gFAAU;gFAC3E,SAAS;oFAAE,OAAO;wFAAC;wFAAG;wFAAK;qFAAE;gFAAC;gFAC9B,YAAY;oFAAE,UAAU;oFAAK,QAAQ;oFAAU,OAAO;gFAAI;;;;;;0FAE5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gFACT,WAAU;gFACV,OAAO;oFAAE,iBAAiB,kBAAkB,SAAS,YAAY;gFAAU;gFAC3E,SAAS;oFAAE,OAAO;wFAAC;wFAAG;wFAAK;qFAAE;gFAAC;gFAC9B,YAAY;oFAAE,UAAU;oFAAK,QAAQ;oFAAU,OAAO;gFAAI;;;;;;;;;;;;kFAG9D,8OAAC;wEAAK,WAAU;wEAAU,OAAO;4EAAE,OAAO,kBAAkB,SAAS,YAAY;wEAAU;kFAAG;;;;;;;;;;;;;;;;;;mDAvC7F,QAAQ,EAAE;;;;;4CA8CrB;4CAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,gBAAgB,MAAM,CAAC;gDACpF,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAI,OAAO;gDAAK;gDAC1C,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAG,OAAO;gDAAE;gDACtC,YAAY;oDAAE,UAAU;oDAAK,MAAM;oDAAU,WAAW;gDAAI;0DAE5D,cAAA,8OAAC;oDAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,IAAI,KAAK,SAAS,qCAAqC,IAAI;;sEAC3H,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,OAAO;gEACL,iBAAiB,QAAQ,IAAI,KAAK,SAC7B,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;4DAC9C;4DACA,YAAY;gEAAE,OAAO;4DAAI;;8EAGzB,8OAAC;oEACC,WAAU;oEACV,OAAO;wEACL,YAAY;wEACZ,WAAW;wEACX,WAAW;oEACb;;;;;;gEAED,QAAQ,IAAI,KAAK,uBAChB,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;yFAEhB,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;sEAGnB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAW,CAAC,gBAAgB,EAAE,QAAQ,IAAI,KAAK,SAAS,kBAAkB,gBAAgB,yBAAyB,CAAC;gEACpH,OAAO;oEACL,iBAAiB,QAAQ,IAAI,KAAK,SAC7B,kBAAkB,SAAS,2BAA2B,2BACtD,kBAAkB,SAAS,2BAA2B;oEAC3D,OAAO,kBAAkB,SAAS,YAAY;oEAC9C,QAAQ,CAAC,UAAU,EAAE,QAAQ,IAAI,KAAK,SACjC,kBAAkB,SAAS,2BAA2B,2BACtD,kBAAkB,SAAS,4BAA4B,2BAA4B;gEAC1F;gEACA,YAAY;oEAAE,OAAO;gEAAK;;kFAE1B,8OAAC;wEAAE,WAAU;kFAA+C,QAAQ,OAAO;;;;;;kFAC3E,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,WAAU;gFACV,OAAO;oFAAE,OAAO,kBAAkB,SAAS,YAAY;gFAAU;0FAEhE,WAAW,QAAQ,SAAS;;;;;;4EAE9B,QAAQ,IAAI,KAAK,sBAChB,8OAAC;gFACC,SAAS,IAAM,YAAY,QAAQ,OAAO,EAAE,QAAQ,EAAE;gFACtD,WAAU;gFACV,OAAM;0FAEL,oBAAoB,QAAQ,EAAE,iBAC7B,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;yGAEjB,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;oFAAU,OAAO;wFAAE,OAAO,kBAAkB,SAAS,YAAY;oFAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CA9DpG,QAAQ,EAAE;;;;;wCAwErB;sDAEA,8OAAC;4CAAI,KAAK;;;;;;;;;;;;8CAIZ,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,aAAa,kBAAkB,SAAS,6BAA6B;wCACrE,YAAY,kBAAkB,SAC1B,0BACA;oCACN;;wCAGC,CAAC,eAAe,iBAAiB,mBAChC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,OAAO;gDACL,iBAAiB,cACZ,kBAAkB,SAAS,2BAA2B,6BACtD,kBAAkB,SAAS,4BAA4B;gDAC5D,aAAa,cACR,kBAAkB,SAAS,2BAA2B,2BACtD,kBAAkB,SAAS,4BAA4B;4CAC9D;4CACA,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;sDAE3B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,SAAS;oEAAE,OAAO;wEAAC;wEAAG;wEAAK;qEAAE;gEAAC;gEAC9B,YAAY;oEAAE,UAAU;oEAAG,QAAQ;gEAAS;;;;;qFAG9C,8OAAC,iNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EAErB,8OAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,cACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;gEAC9C;0EAEC,cACG,CAAC,iBAAiB,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM,GACxG;;;;;;;;;;;;oDAIP,6BACC,8OAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;sDAOT,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,KAAK;gEACL,OAAO;gEACP,UAAU,CAAC;oEACT,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEAC9B,4BAA4B;oEAC5B,MAAM,SAAS,EAAE,MAAM;oEACvB,OAAO,KAAK,CAAC,MAAM,GAAG;oEACtB,MAAM,eAAe,OAAO,YAAY;oEACxC,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,cAAc,KAAK;oEACvD,OAAO,KAAK,CAAC,MAAM,GAAG,YAAY;gEACpC;gEACA,WAAW,CAAC;oEACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;wEACpC,EAAE,cAAc;wEAChB;oEACF;gEACF;gEACA,aAAY;gEACZ,UAAU;gEACV,MAAM;gEACN,WAAU;gEACV,OAAO;oEACL,iBAAiB,kBAAkB,SAAS,YAAY;oEACxD,aAAa,kBAAkB,SAAS,6BAA6B;oEACrE,OAAO,kBAAkB,SAAS,YAAY;oEAC9C,WAAW;oEACX,WAAW;oEACX,YAAY;gEACd;;;;;;0EAIF,8OAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,kBAAkB,SAAS,YAAY;oEAC9C,iBAAiB,kBAAkB,SAAS,0BAA0B;gEACxE;0EAEC,aAAa,MAAM;;;;;;;;;;;;;;;;;8DAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,SAAS;oDACT,UAAU,CAAC,aAAa,IAAI,MAAM;oDAClC,WAAU;oDACV,OAAO;wDACL,YAAY,CAAC,aAAa,IAAI,MAAM,YAC/B,kBAAkB,SAAS,2BAA2B,6BACvD;wDACJ,WAAW,CAAC,aAAa,IAAI,MAAM,YAC/B,SACA;wDACJ,QAAQ;wDACR,OAAO;oDACT;oDACA,YAAY,CAAC,aAAa,IAAI,MAAM,YAAY,CAAC,IAAI;wDAAE,OAAO;oDAAK;oDACnE,UAAU,CAAC,aAAa,IAAI,MAAM,YAAY,CAAC,IAAI;wDAAE,OAAO;oDAAK;8DAEjE,cAAA,8OAAC;wDAAI,WAAU;kEACZ,0BACC,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;wCAOvB,CAAC,8BACA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAgB;wDAAkB;qDAAgB,CAAC,GAAG,CAAC,CAAC,2BACxD,8OAAC;4DAEC,SAAS,IAAM,gBAAgB,CAAC,QAAQ,EAAE,WAAW,WAAW,IAAI;4DACpE,WAAU;4DACV,OAAO;gEACL,iBAAiB,kBAAkB,SAAS,4BAA4B;gEACxE,OAAO,kBAAkB,SAAS,YAAY;gEAC9C,QAAQ,kBAAkB,SAAS,sCAAsC;4DAC3E;sEAEC;2DATI;;;;;;;;;;8DAeX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,8OAAC;4DAAK,OAAO;gEAAE,OAAO,kBAAkB,SAAS,YAAY;4DAAU;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAclG", "debugId": null}}]}