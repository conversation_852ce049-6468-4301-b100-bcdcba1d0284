{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/History.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  Search, Download, Clock, User, Package, DollarSign,\n  Eye, Trash2, Archive, RefreshCw,\n  TrendingUp, BarChart3, FileText, Settings, CheckSquare,\n  Square, X, ChevronDown, ChevronRight, AlertCircle,\n  Activity, Zap, Shield, Database, Bell, Star,\n  Wifi, WifiOff, Timer, Hash\n} from 'lucide-react'\nimport { useState, useMemo, useEffect } from 'react'\n\ninterface HistoryItem {\n  id: string\n  type: 'product' | 'debt' | 'payment' | 'login' | 'system' | 'security' | 'backup' | 'notification'\n  action: string\n  description: string\n  user: string\n  timestamp: string\n  priority: 'low' | 'medium' | 'high' | 'critical'\n  status: 'success' | 'warning' | 'error' | 'info'\n  category: string\n  ipAddress?: string\n  userAgent?: string\n  details?: Record<string, unknown>\n  tags?: string[]\n}\n\ninterface FilterOptions {\n  types: string[]\n  priorities: string[]\n  statuses: string[]\n  dateRange: string\n  users: string[]\n  categories: string[]\n}\n\nexport default function History() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filterType, setFilterType] = useState('all')\n  const [dateRange, setDateRange] = useState('7days')\n  const [selectedItems, setSelectedItems] = useState<string[]>([])\n  const [showFilters, setShowFilters] = useState(false)\n  const [selectedActivity, setSelectedActivity] = useState<HistoryItem | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const [viewMode, setViewMode] = useState<'list' | 'timeline' | 'grid'>('list')\n  const [sortBy, setSortBy] = useState<'timestamp' | 'type' | 'priority'>('timestamp')\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\n  const [autoRefresh, setAutoRefresh] = useState(false)\n  const [lastUpdated, setLastUpdated] = useState(new Date())\n  const [connectionStatus, setConnectionStatus] = useState<'online' | 'offline'>('online')\n\n  const [filters, setFilters] = useState<FilterOptions>({\n    types: [],\n    priorities: [],\n    statuses: [],\n    dateRange: '7days',\n    users: [],\n    categories: []\n  })\n\n  // Enhanced history data with more comprehensive information\n  const historyData: HistoryItem[] = useMemo(() => [\n    {\n      id: '1',\n      type: 'product',\n      action: 'Product Added',\n      description: 'Added \"Lucky Me Pancit Canton\" to product list',\n      user: 'Admin',\n      timestamp: '2024-01-20T10:30:00Z',\n      priority: 'medium',\n      status: 'success',\n      category: 'Inventory Management',\n      tags: ['product', 'inventory', 'add'],\n      details: {\n        productName: 'Lucky Me Pancit Canton',\n        price: 15.00,\n        sku: 'LMC001',\n        quantity: 50,\n        supplier: 'Lucky Me Foods'\n      }\n    },\n    {\n      id: '2',\n      type: 'debt',\n      action: 'Debt Recorded',\n      description: 'New debt record for Juan Dela Cruz',\n      user: 'Admin',\n      timestamp: '2024-01-20T09:15:00Z',\n      priority: 'high',\n      status: 'warning',\n      category: 'Customer Management',\n      tags: ['debt', 'customer', 'record'],\n      details: {\n        customer: 'Juan Dela Cruz',\n        amount: 45.00,\n        dueDate: '2024-02-20',\n        contactNumber: '09123456789',\n        address: 'Barangay San Jose, Cebu City'\n      }\n    },\n    {\n      id: '3',\n      type: 'payment',\n      action: 'Payment Received',\n      description: 'Payment received from Maria Santos',\n      user: 'Admin',\n      timestamp: '2024-01-19T16:45:00Z',\n      priority: 'medium',\n      status: 'success',\n      category: 'Financial Transaction',\n      tags: ['payment', 'customer', 'income'],\n      details: {\n        customer: 'Maria Santos',\n        amount: 120.00,\n        paymentMethod: 'Cash',\n        previousBalance: 200.00,\n        newBalance: 80.00\n      }\n    },\n    {\n      id: '4',\n      type: 'product',\n      action: 'Stock Updated',\n      description: 'Updated stock quantity for Coca-Cola',\n      user: 'Admin',\n      timestamp: '2024-01-19T14:20:00Z',\n      priority: 'low',\n      status: 'success',\n      category: 'Inventory Management',\n      tags: ['product', 'stock', 'update'],\n      details: {\n        productName: 'Coca-Cola',\n        oldStock: 25,\n        newStock: 50,\n        reason: 'New delivery received',\n        supplier: 'Coca-Cola Bottlers Philippines'\n      }\n    },\n    {\n      id: '5',\n      type: 'login',\n      action: 'User Login',\n      description: 'Admin user logged into the system',\n      user: 'Admin',\n      timestamp: '2024-01-19T08:00:00Z',\n      priority: 'low',\n      status: 'info',\n      category: 'Security',\n      tags: ['login', 'security', 'access'],\n      ipAddress: '*************',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      details: {\n        ipAddress: '*************',\n        location: 'Cebu City, Philippines',\n        device: 'Desktop - Windows 10',\n        sessionDuration: '4 hours 30 minutes'\n      }\n    },\n    {\n      id: '6',\n      type: 'system',\n      action: 'Backup Created',\n      description: 'Automatic database backup completed',\n      user: 'System',\n      timestamp: '2024-01-19T02:00:00Z',\n      priority: 'medium',\n      status: 'success',\n      category: 'System Maintenance',\n      tags: ['backup', 'database', 'maintenance'],\n      details: {\n        backupSize: '2.5MB',\n        backupType: 'Full Backup',\n        location: 'Cloud Storage',\n        duration: '45 seconds',\n        tablesBackedUp: 8\n      }\n    },\n    {\n      id: '7',\n      type: 'debt',\n      action: 'Debt Updated',\n      description: 'Updated debt record for Ana Reyes',\n      user: 'Admin',\n      timestamp: '2024-01-18T15:30:00Z',\n      priority: 'high',\n      status: 'warning',\n      category: 'Customer Management',\n      tags: ['debt', 'customer', 'update'],\n      details: {\n        customer: 'Ana Reyes',\n        oldAmount: 75.00,\n        newAmount: 100.00,\n        reason: 'Additional purchase',\n        dueDate: '2024-02-18',\n        contactNumber: '09987654321'\n      }\n    },\n    {\n      id: '8',\n      type: 'product',\n      action: 'Product Deleted',\n      description: 'Removed \"Expired Milk\" from product list',\n      user: 'Admin',\n      timestamp: '2024-01-18T11:10:00Z',\n      priority: 'high',\n      status: 'error',\n      category: 'Inventory Management',\n      tags: ['product', 'delete', 'expired'],\n      details: {\n        productName: 'Expired Milk',\n        reason: 'Product expired',\n        expiryDate: '2024-01-15',\n        quantityRemoved: 12,\n        loss: 180.00\n      }\n    },\n    {\n      id: '9',\n      type: 'security',\n      action: 'Failed Login Attempt',\n      description: 'Multiple failed login attempts detected',\n      user: 'Unknown',\n      timestamp: '2024-01-18T03:45:00Z',\n      priority: 'critical',\n      status: 'error',\n      category: 'Security Alert',\n      tags: ['security', 'login', 'failed'],\n      ipAddress: '*************',\n      details: {\n        attempts: 5,\n        ipAddress: '*************',\n        location: 'Unknown',\n        blocked: true,\n        duration: '24 hours'\n      }\n    },\n    {\n      id: '10',\n      type: 'notification',\n      action: 'Low Stock Alert',\n      description: 'Stock level below minimum threshold for multiple products',\n      user: 'System',\n      timestamp: '2024-01-17T18:00:00Z',\n      priority: 'high',\n      status: 'warning',\n      category: 'Inventory Alert',\n      tags: ['stock', 'alert', 'inventory'],\n      details: {\n        affectedProducts: ['Rice 25kg', 'Cooking Oil 1L', 'Sugar 1kg'],\n        minimumThreshold: 10,\n        currentStock: [5, 3, 7],\n        recommendedOrder: [50, 20, 30]\n      }\n    },\n  ], [])\n\n  // Auto-refresh and connection monitoring\n  useEffect(() => {\n    let interval: NodeJS.Timeout\n\n    if (autoRefresh) {\n      interval = setInterval(() => {\n        setLastUpdated(new Date())\n        // In a real app, this would fetch new data from the API\n        setIsLoading(true)\n        setTimeout(() => setIsLoading(false), 1000)\n      }, 30000) // Refresh every 30 seconds\n    }\n\n    return () => {\n      if (interval) clearInterval(interval)\n    }\n  }, [autoRefresh])\n\n  // Monitor connection status\n  useEffect(() => {\n    const handleOnline = () => setConnectionStatus('online')\n    const handleOffline = () => setConnectionStatus('offline')\n\n    window.addEventListener('online', handleOnline)\n    window.addEventListener('offline', handleOffline)\n\n    return () => {\n      window.removeEventListener('online', handleOnline)\n      window.removeEventListener('offline', handleOffline)\n    }\n  }, [])\n\n  // Enhanced utility functions\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'product':\n        return <Package className=\"h-4 w-4\" />\n      case 'debt':\n        return <DollarSign className=\"h-4 w-4 text-red-500\" />\n      case 'payment':\n        return <DollarSign className=\"h-4 w-4 text-green-500\" />\n      case 'login':\n        return <User className=\"h-4 w-4\" />\n      case 'system':\n        return <Database className=\"h-4 w-4\" />\n      case 'security':\n        return <Shield className=\"h-4 w-4\" />\n      case 'backup':\n        return <Archive className=\"h-4 w-4\" />\n      case 'notification':\n        return <Bell className=\"h-4 w-4\" />\n      default:\n        return <Activity className=\"h-4 w-4\" />\n    }\n  }\n\n  const getPriorityIcon = (priority: string) => {\n    switch (priority) {\n      case 'critical':\n        return <AlertCircle className=\"h-4 w-4 text-red-600\" />\n      case 'high':\n        return <Zap className=\"h-4 w-4 text-orange-500\" />\n      case 'medium':\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />\n      case 'low':\n        return <Star className=\"h-4 w-4 text-blue-500\" />\n      default:\n        return <Clock className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'product':\n        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800'\n      case 'debt':\n        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 border border-red-200 dark:border-red-800'\n      case 'payment':\n        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800'\n      case 'login':\n        return 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400 border border-purple-200 dark:border-purple-800'\n      case 'system':\n        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400 border border-gray-200 dark:border-gray-800'\n      case 'security':\n        return 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400 border border-orange-200 dark:border-orange-800'\n      case 'backup':\n        return 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-400 border border-indigo-200 dark:border-indigo-800'\n      case 'notification':\n        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800'\n      default:\n        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400 border border-gray-200 dark:border-gray-800'\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'success':\n        return 'bg-green-50 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'\n      case 'warning':\n        return 'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800'\n      case 'error':\n        return 'bg-red-50 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800'\n      case 'info':\n        return 'bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800'\n      default:\n        return 'bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800'\n    }\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'critical':\n        return 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400 dark:border-red-700'\n      case 'high':\n        return 'bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-700'\n      case 'medium':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-700'\n      case 'low':\n        return 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-700'\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-900/30 dark:text-gray-400 dark:border-gray-700'\n    }\n  }\n\n  // Advanced filtering and sorting logic\n  const filteredHistory = useMemo(() => {\n    const filtered = historyData.filter(item => {\n      // Search filter\n      const matchesSearch = searchTerm === '' ||\n        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.action.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.user.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (item.tags && item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))\n\n      // Type filter\n      const matchesType = filterType === 'all' || item.type === filterType\n\n      // Advanced filters\n      const matchesTypes = filters.types.length === 0 || filters.types.includes(item.type)\n      const matchesPriorities = filters.priorities.length === 0 || filters.priorities.includes(item.priority)\n      const matchesStatuses = filters.statuses.length === 0 || filters.statuses.includes(item.status)\n      const matchesUsers = filters.users.length === 0 || filters.users.includes(item.user)\n      const matchesCategories = filters.categories.length === 0 || filters.categories.includes(item.category)\n\n      // Date range filter\n      const itemDate = new Date(item.timestamp)\n      const now = new Date()\n      let matchesDateRange = true\n\n      switch (dateRange) {\n        case '24hours':\n          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 24 * 60 * 60 * 1000\n          break\n        case '7days':\n          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 7 * 24 * 60 * 60 * 1000\n          break\n        case '30days':\n          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 30 * 24 * 60 * 60 * 1000\n          break\n        case '90days':\n          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 90 * 24 * 60 * 60 * 1000\n          break\n        case 'all':\n          matchesDateRange = true\n          break\n      }\n\n      return matchesSearch && matchesType && matchesTypes && matchesPriorities &&\n             matchesStatuses && matchesUsers && matchesCategories && matchesDateRange\n    })\n\n    // Sorting\n    filtered.sort((a, b) => {\n      let comparison = 0\n\n      switch (sortBy) {\n        case 'timestamp':\n          comparison = new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()\n          break\n        case 'type':\n          comparison = a.type.localeCompare(b.type)\n          break\n        case 'priority':\n          const priorityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 }\n          comparison = (priorityOrder[a.priority as keyof typeof priorityOrder] || 0) -\n                      (priorityOrder[b.priority as keyof typeof priorityOrder] || 0)\n          break\n      }\n\n      return sortOrder === 'desc' ? -comparison : comparison\n    })\n\n    return filtered\n  }, [historyData, searchTerm, filterType, filters, dateRange, sortBy, sortOrder])\n\n  // Get unique values for filter options\n  const uniquePriorities = [...new Set(historyData.map(item => item.priority))]\n  const uniqueStatuses = [...new Set(historyData.map(item => item.status))]\n  const uniqueUsers = [...new Set(historyData.map(item => item.user))]\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))\n    const diffInHours = Math.floor(diffInMinutes / 60)\n    const diffInDays = Math.floor(diffInHours / 24)\n\n    if (diffInMinutes < 1) {\n      return 'Just now'\n    } else if (diffInMinutes < 60) {\n      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`\n    } else if (diffInHours < 24) {\n      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`\n    } else if (diffInDays < 7) {\n      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`\n    } else {\n      return date.toLocaleDateString('en-PH', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      })\n    }\n  }\n\n  // Bulk operations\n  const handleSelectAll = () => {\n    if (selectedItems.length === filteredHistory.length) {\n      setSelectedItems([])\n    } else {\n      setSelectedItems(filteredHistory.map(item => item.id))\n    }\n  }\n\n  const handleSelectItem = (id: string) => {\n    setSelectedItems(prev =>\n      prev.includes(id)\n        ? prev.filter(item => item !== id)\n        : [...prev, id]\n    )\n  }\n\n  const handleBulkDelete = () => {\n    if (selectedItems.length > 0) {\n      // In a real app, this would make an API call\n      // For now, just clear the selection\n      setSelectedItems([])\n    }\n  }\n\n  const handleBulkExport = () => {\n    const selectedData = filteredHistory.filter(item => selectedItems.includes(item.id))\n    exportHistory(selectedData)\n  }\n\n  const exportHistory = (data = filteredHistory) => {\n    const csvContent = [\n      ['Timestamp', 'Type', 'Action', 'Description', 'User', 'Priority', 'Status', 'Category'],\n      ...data.map(item => [\n        item.timestamp,\n        item.type,\n        item.action,\n        item.description,\n        item.user,\n        item.priority,\n        item.status,\n        item.category\n      ])\n    ].map(row => row.join(',')).join('\\n')\n\n    const blob = new Blob([csvContent], { type: 'text/csv' })\n    const url = window.URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `revantad-store-history-${new Date().toISOString().split('T')[0]}.csv`\n    a.click()\n    window.URL.revokeObjectURL(url)\n  }\n\n  // Activity statistics\n  const activityStats = useMemo(() => {\n    const stats = {\n      total: filteredHistory.length,\n      byType: {} as Record<string, number>,\n      byPriority: {} as Record<string, number>,\n      byStatus: {} as Record<string, number>,\n      byUser: {} as Record<string, number>,\n      todayCount: 0,\n      weekCount: 0\n    }\n\n    const now = new Date()\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())\n    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)\n\n    filteredHistory.forEach(item => {\n      // Count by type\n      stats.byType[item.type] = (stats.byType[item.type] || 0) + 1\n\n      // Count by priority\n      stats.byPriority[item.priority] = (stats.byPriority[item.priority] || 0) + 1\n\n      // Count by status\n      stats.byStatus[item.status] = (stats.byStatus[item.status] || 0) + 1\n\n      // Count by user\n      stats.byUser[item.user] = (stats.byUser[item.user] || 0) + 1\n\n      // Count today and this week\n      const itemDate = new Date(item.timestamp)\n      if (itemDate >= today) {\n        stats.todayCount++\n      }\n      if (itemDate >= weekAgo) {\n        stats.weekCount++\n      }\n    })\n\n    return stats\n  }, [filteredHistory])\n\n  return (\n    <div className=\"space-y-6 animate-fade-in\">\n      {/* Enhanced Header with Stats */}\n      <div className=\"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4\">\n        <div className=\"flex-1\">\n          <div className=\"flex items-center gap-3 mb-2\">\n            <div className=\"p-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg\">\n              <Activity className=\"h-6 w-6 text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                Activity History\n              </h2>\n              <p className=\"text-gray-600 dark:text-gray-400\">\n                Comprehensive tracking sa lahat ng activities sa inyong tindahan - makita ang lahat ng nangyari\n              </p>\n            </div>\n          </div>\n\n          {/* Quick Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3 mt-4\">\n            <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-blue-600 dark:text-blue-400\">Total Activities</p>\n                  <p className=\"text-lg font-bold text-blue-900 dark:text-blue-300\">{activityStats.total}</p>\n                </div>\n                <BarChart3 className=\"h-5 w-5 text-blue-500\" />\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-3 rounded-lg border border-green-200 dark:border-green-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-green-600 dark:text-green-400\">Today</p>\n                  <p className=\"text-lg font-bold text-green-900 dark:text-green-300\">{activityStats.todayCount}</p>\n                </div>\n                <TrendingUp className=\"h-5 w-5 text-green-500\" />\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-yellow-600 dark:text-yellow-400\">This Week</p>\n                  <p className=\"text-lg font-bold text-yellow-900 dark:text-yellow-300\">{activityStats.weekCount}</p>\n                </div>\n                <Clock className=\"h-5 w-5 text-yellow-500\" />\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-3 rounded-lg border border-purple-200 dark:border-purple-800\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs font-medium text-purple-600 dark:text-purple-400\">Selected</p>\n                  <p className=\"text-lg font-bold text-purple-900 dark:text-purple-300\">{selectedItems.length}</p>\n                </div>\n                <CheckSquare className=\"h-5 w-5 text-purple-500\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-wrap gap-2\">\n          {/* Connection Status */}\n          <div className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium ${\n            connectionStatus === 'online'\n              ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'\n              : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'\n          }`}>\n            {connectionStatus === 'online' ? (\n              <Wifi className=\"h-4 w-4\" />\n            ) : (\n              <WifiOff className=\"h-4 w-4\" />\n            )}\n            {connectionStatus === 'online' ? 'Online' : 'Offline'}\n          </div>\n\n          {/* Auto Refresh Toggle */}\n          <button\n            onClick={() => setAutoRefresh(!autoRefresh)}\n            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n              autoRefresh\n                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'\n                : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'\n            }`}\n          >\n            <Timer className={`h-4 w-4 ${autoRefresh ? 'animate-pulse' : ''}`} />\n            Auto Refresh\n          </button>\n\n          <button\n            onClick={() => setIsLoading(!isLoading)}\n            className=\"btn-outline flex items-center gap-2 text-sm\"\n            disabled={isLoading}\n          >\n            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />\n            Refresh\n          </button>\n\n          {selectedItems.length > 0 && (\n            <>\n              <button\n                onClick={handleBulkExport}\n                className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all\"\n              >\n                <Download className=\"h-4 w-4\" />\n                Export Selected\n              </button>\n\n              <button\n                onClick={handleBulkDelete}\n                className=\"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all\"\n              >\n                <Trash2 className=\"h-4 w-4\" />\n                Delete Selected\n              </button>\n            </>\n          )}\n\n          <button\n            onClick={() => exportHistory()}\n            className=\"btn-primary flex items-center gap-2 text-sm\"\n          >\n            <Download className=\"h-4 w-4\" />\n            Export All\n          </button>\n        </div>\n      </div>\n\n      {/* Enhanced Filters */}\n      <div className=\"card p-6 border-l-4 border-l-green-500\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2\">\n            <Search className=\"h-5 w-5 text-green-500\" />\n            Search & Filters\n          </h3>\n          <div className=\"flex items-center gap-2\">\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white flex items-center gap-1 transition-colors\"\n            >\n              {showFilters ? <ChevronDown className=\"h-4 w-4\" /> : <ChevronRight className=\"h-4 w-4\" />}\n              Advanced Filters\n            </button>\n\n            {/* View Mode Toggle */}\n            <div className=\"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n              {(['list', 'timeline', 'grid'] as const).map((mode) => (\n                <button\n                  key={mode}\n                  onClick={() => setViewMode(mode)}\n                  className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${\n                    viewMode === mode\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\n                  }`}\n                >\n                  {mode.charAt(0).toUpperCase() + mode.slice(1)}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Basic Filters */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\">\n          {/* Search */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search activities, users, descriptions...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm\"\n            />\n          </div>\n\n          {/* Type Filter */}\n          <select\n            value={filterType}\n            onChange={(e) => setFilterType(e.target.value)}\n            className=\"px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm\"\n          >\n            <option value=\"all\">All Types</option>\n            <option value=\"product\">Product Activities</option>\n            <option value=\"debt\">Debt Activities</option>\n            <option value=\"payment\">Payment Activities</option>\n            <option value=\"login\">Login Activities</option>\n            <option value=\"system\">System Activities</option>\n            <option value=\"security\">Security Activities</option>\n            <option value=\"notification\">Notifications</option>\n          </select>\n\n          {/* Date Range */}\n          <select\n            value={dateRange}\n            onChange={(e) => setDateRange(e.target.value)}\n            className=\"px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm\"\n          >\n            <option value=\"24hours\">Last 24 hours</option>\n            <option value=\"7days\">Last 7 days</option>\n            <option value=\"30days\">Last 30 days</option>\n            <option value=\"90days\">Last 90 days</option>\n            <option value=\"all\">All time</option>\n          </select>\n\n          {/* Sort Options */}\n          <div className=\"flex gap-2\">\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value as 'timestamp' | 'type' | 'priority')}\n              className=\"flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm\"\n            >\n              <option value=\"timestamp\">Sort by Time</option>\n              <option value=\"type\">Sort by Type</option>\n              <option value=\"priority\">Sort by Priority</option>\n            </select>\n            <button\n              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}\n              className=\"px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n              title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}\n            >\n              {sortOrder === 'asc' ? '↑' : '↓'}\n            </button>\n          </div>\n        </div>\n\n        {/* Advanced Filters (Collapsible) */}\n        {showFilters && (\n          <div className=\"border-t border-gray-200 dark:border-gray-700 pt-4 animate-fade-in-up\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {/* Priority Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Priority Levels\n                </label>\n                <div className=\"space-y-2\">\n                  {uniquePriorities.map(priority => (\n                    <label key={priority} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={filters.priorities.includes(priority)}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setFilters(prev => ({\n                              ...prev,\n                              priorities: [...prev.priorities, priority]\n                            }))\n                          } else {\n                            setFilters(prev => ({\n                              ...prev,\n                              priorities: prev.priorities.filter(p => p !== priority)\n                            }))\n                          }\n                        }}\n                        className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                      />\n                      <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300 capitalize flex items-center gap-1\">\n                        {getPriorityIcon(priority)}\n                        {priority}\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n\n              {/* Status Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Status Types\n                </label>\n                <div className=\"space-y-2\">\n                  {uniqueStatuses.map(status => (\n                    <label key={status} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={filters.statuses.includes(status)}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setFilters(prev => ({\n                              ...prev,\n                              statuses: [...prev.statuses, status]\n                            }))\n                          } else {\n                            setFilters(prev => ({\n                              ...prev,\n                              statuses: prev.statuses.filter(s => s !== status)\n                            }))\n                          }\n                        }}\n                        className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                      />\n                      <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>\n                        {status}\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n\n              {/* User Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Users\n                </label>\n                <div className=\"space-y-2\">\n                  {uniqueUsers.map(user => (\n                    <label key={user} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={filters.users.includes(user)}\n                        onChange={(e) => {\n                          if (e.target.checked) {\n                            setFilters(prev => ({\n                              ...prev,\n                              users: [...prev.users, user]\n                            }))\n                          } else {\n                            setFilters(prev => ({\n                              ...prev,\n                              users: prev.users.filter(u => u !== user)\n                            }))\n                          }\n                        }}\n                        className=\"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                      />\n                      <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300 flex items-center gap-1\">\n                        <User className=\"h-3 w-3\" />\n                        {user}\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Clear Filters */}\n            <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <button\n                onClick={() => {\n                  setFilters({\n                    types: [],\n                    priorities: [],\n                    statuses: [],\n                    dateRange: '7days',\n                    users: [],\n                    categories: []\n                  })\n                  setSearchTerm('')\n                  setFilterType('all')\n                  setDateRange('7days')\n                }}\n                className=\"text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 flex items-center gap-1 transition-colors\"\n              >\n                <X className=\"h-4 w-4\" />\n                Clear All Filters\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Activity Stats with Analytics */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6\">\n        {/* Activity Types Distribution */}\n        <div className=\"card p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2\">\n            <BarChart3 className=\"h-5 w-5 text-blue-500\" />\n            Activity Distribution\n          </h3>\n          <div className=\"space-y-3\">\n            {Object.entries(activityStats.byType).map(([type, count]) => {\n              const percentage = ((count / activityStats.total) * 100).toFixed(1)\n              return (\n                <div key={type} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    <div className={`p-1.5 rounded-lg ${getTypeColor(type)}`}>\n                      {getTypeIcon(type)}\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300 capitalize\">\n                      {type}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                      <div\n                        className=\"bg-blue-500 h-2 rounded-full transition-all duration-500\"\n                        style={{ width: `${percentage}%` }}\n                      ></div>\n                    </div>\n                    <span className=\"text-sm font-bold text-gray-900 dark:text-white w-8\">\n                      {count}\n                    </span>\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400 w-10\">\n                      {percentage}%\n                    </span>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Priority Analysis */}\n        <div className=\"card p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2\">\n            <AlertCircle className=\"h-5 w-5 text-orange-500\" />\n            Priority Analysis\n          </h3>\n          <div className=\"space-y-3\">\n            {Object.entries(activityStats.byPriority).map(([priority, count]) => {\n              const percentage = ((count / activityStats.total) * 100).toFixed(1)\n              return (\n                <div key={priority} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    {getPriorityIcon(priority)}\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300 capitalize\">\n                      {priority}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                      <div\n                        className={`h-2 rounded-full transition-all duration-500 ${\n                          priority === 'critical' ? 'bg-red-500' :\n                          priority === 'high' ? 'bg-orange-500' :\n                          priority === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'\n                        }`}\n                        style={{ width: `${percentage}%` }}\n                      ></div>\n                    </div>\n                    <span className=\"text-sm font-bold text-gray-900 dark:text-white w-8\">\n                      {count}\n                    </span>\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400 w-10\">\n                      {percentage}%\n                    </span>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* User Activity Summary */}\n        <div className=\"card p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2\">\n            <User className=\"h-5 w-5 text-green-500\" />\n            User Activity\n          </h3>\n          <div className=\"space-y-3\">\n            {Object.entries(activityStats.byUser).map(([user, count]) => {\n              const percentage = ((count / activityStats.total) * 100).toFixed(1)\n              return (\n                <div key={user} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-xs font-bold\">\n                        {user.charAt(0).toUpperCase()}\n                      </span>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      {user}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                      <div\n                        className=\"bg-green-500 h-2 rounded-full transition-all duration-500\"\n                        style={{ width: `${percentage}%` }}\n                      ></div>\n                    </div>\n                    <span className=\"text-sm font-bold text-gray-900 dark:text-white w-8\">\n                      {count}\n                    </span>\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400 w-10\">\n                      {percentage}%\n                    </span>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Enhanced Activity List */}\n      <div className=\"card overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2\">\n                <FileText className=\"h-5 w-5 text-green-500\" />\n                Activity Timeline ({filteredHistory.length})\n              </h3>\n\n              {/* Bulk Selection */}\n              {filteredHistory.length > 0 && (\n                <div className=\"flex items-center gap-2\">\n                  <button\n                    onClick={handleSelectAll}\n                    className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors\"\n                  >\n                    {selectedItems.length === filteredHistory.length ? (\n                      <CheckSquare className=\"h-4 w-4 text-green-500\" />\n                    ) : (\n                      <Square className=\"h-4 w-4\" />\n                    )}\n                    Select All\n                  </button>\n\n                  {selectedItems.length > 0 && (\n                    <span className=\"text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-2 py-1 rounded-full\">\n                      {selectedItems.length} selected\n                    </span>\n                  )}\n                </div>\n              )}\n            </div>\n\n            {/* Loading Indicator */}\n            {isLoading && (\n              <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\">\n                <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                Loading...\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div className=\"divide-y divide-gray-200 dark:divide-gray-700 max-h-[600px] overflow-y-auto main-content-scroll\">\n          {filteredHistory.map((item, index) => (\n            <div\n              key={item.id}\n              className={`p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 group ${\n                selectedItems.includes(item.id) ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500' : ''\n              }`}\n              style={{ animationDelay: `${index * 50}ms` }}\n            >\n              <div className=\"flex items-start space-x-4\">\n                {/* Selection Checkbox */}\n                <button\n                  onClick={() => handleSelectItem(item.id)}\n                  className=\"mt-1 opacity-0 group-hover:opacity-100 transition-opacity\"\n                >\n                  {selectedItems.includes(item.id) ? (\n                    <CheckSquare className=\"h-4 w-4 text-green-500\" />\n                  ) : (\n                    <Square className=\"h-4 w-4 text-gray-400 hover:text-gray-600\" />\n                  )}\n                </button>\n\n                {/* Activity Icon */}\n                <div className={`p-3 rounded-xl shadow-sm ${getTypeColor(item.type)} transition-all group-hover:scale-105`}>\n                  {getTypeIcon(item.type)}\n                </div>\n\n                <div className=\"flex-1 min-w-0\">\n                  {/* Header */}\n                  <div className=\"flex items-start justify-between gap-4 mb-2\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center gap-2 mb-1\">\n                        <h4 className=\"text-sm font-semibold text-gray-900 dark:text-white\">\n                          {item.action}\n                        </h4>\n\n                        {/* Priority Badge */}\n                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(item.priority)}`}>\n                          {getPriorityIcon(item.priority)}\n                          {item.priority}\n                        </span>\n\n                        {/* Status Badge */}\n                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>\n                          {item.status}\n                        </span>\n                      </div>\n\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 leading-relaxed\">\n                        {item.description}\n                      </p>\n\n                      {/* Tags */}\n                      {item.tags && item.tags.length > 0 && (\n                        <div className=\"flex flex-wrap gap-1 mt-2\">\n                          {item.tags.map(tag => (\n                            <span\n                              key={tag}\n                              className=\"inline-flex px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-md\"\n                            >\n                              #{tag}\n                            </span>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"text-right\">\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400 font-medium\">\n                        {formatTimestamp(item.timestamp)}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Footer */}\n                  <div className=\"flex items-center justify-between mt-3 pt-3 border-t border-gray-100 dark:border-gray-700\">\n                    <div className=\"flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400\">\n                      <span className=\"flex items-center gap-1\">\n                        <User className=\"h-3 w-3\" />\n                        {item.user}\n                      </span>\n\n                      <span className=\"flex items-center gap-1\">\n                        <Settings className=\"h-3 w-3\" />\n                        {item.category}\n                      </span>\n\n                      {item.ipAddress && (\n                        <span className=\"flex items-center gap-1\">\n                          <Shield className=\"h-3 w-3\" />\n                          {item.ipAddress}\n                        </span>\n                      )}\n                    </div>\n\n                    {item.details && (\n                      <button\n                        onClick={() => setSelectedActivity(item)}\n                        className=\"text-xs text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 font-medium flex items-center gap-1 transition-colors\"\n                      >\n                        <Eye className=\"h-3 w-3\" />\n                        View Details\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Enhanced Empty State */}\n        {filteredHistory.length === 0 && (\n          <div className=\"p-16 text-center\">\n            <div className=\"bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6\">\n              <Activity className=\"h-12 w-12 text-gray-400\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-3\">\n              Walang Activities na Nakita\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              Try i-adjust ang inyong search terms o filter criteria para makita ang activities na inyong gipangita.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\n              <button\n                onClick={() => {\n                  setSearchTerm('')\n                  setFilterType('all')\n                  setFilters({\n                    types: [],\n                    priorities: [],\n                    statuses: [],\n                    dateRange: '7days',\n                    users: [],\n                    categories: []\n                  })\n                }}\n                className=\"btn-outline\"\n              >\n                Clear Filters\n              </button>\n              <button\n                onClick={() => setIsLoading(!isLoading)}\n                className=\"btn-primary\"\n              >\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                Refresh Data\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Activity Details Modal */}\n      {selectedActivity && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 animate-fade-in\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            {/* Modal Header */}\n            <div className=\"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <div className={`p-3 rounded-xl ${getTypeColor(selectedActivity.type)}`}>\n                    {getTypeIcon(selectedActivity.type)}\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                      Activity Details\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {selectedActivity.action}\n                    </p>\n                  </div>\n                </div>\n                <button\n                  onClick={() => setSelectedActivity(null)}\n                  className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  <X className=\"h-5 w-5 text-gray-500\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Modal Content */}\n            <div className=\"p-6 space-y-6\">\n              {/* Basic Information */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-3\">\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      Activity Type\n                    </label>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${getTypeColor(selectedActivity.type)}`}>\n                        {getTypeIcon(selectedActivity.type)}\n                        {selectedActivity.type}\n                      </span>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      Priority Level\n                    </label>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(selectedActivity.priority)}`}>\n                        {getPriorityIcon(selectedActivity.priority)}\n                        {selectedActivity.priority}\n                      </span>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      Status\n                    </label>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedActivity.status)}`}>\n                        {selectedActivity.status}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      User\n                    </label>\n                    <p className=\"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1\">\n                      <User className=\"h-4 w-4\" />\n                      {selectedActivity.user}\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      Category\n                    </label>\n                    <p className=\"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1\">\n                      <Settings className=\"h-4 w-4\" />\n                      {selectedActivity.category}\n                    </p>\n                  </div>\n\n                  <div>\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                      Timestamp\n                    </label>\n                    <p className=\"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1\">\n                      <Clock className=\"h-4 w-4\" />\n                      {new Date(selectedActivity.timestamp).toLocaleString('en-PH')}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Description */}\n              <div>\n                <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                  Description\n                </label>\n                <p className=\"text-sm text-gray-900 dark:text-white mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                  {selectedActivity.description}\n                </p>\n              </div>\n\n              {/* Tags */}\n              {selectedActivity.tags && selectedActivity.tags.length > 0 && (\n                <div>\n                  <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                    Tags\n                  </label>\n                  <div className=\"flex flex-wrap gap-2 mt-2\">\n                    {selectedActivity.tags.map(tag => (\n                      <span\n                        key={tag}\n                        className=\"inline-flex px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 text-sm rounded-full\"\n                      >\n                        #{tag}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Additional Details */}\n              {selectedActivity.details && (\n                <div>\n                  <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                    Additional Details\n                  </label>\n                  <div className=\"mt-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <pre className=\"text-sm text-gray-900 dark:text-white whitespace-pre-wrap font-mono\">\n                      {JSON.stringify(selectedActivity.details, null, 2)}\n                    </pre>\n                  </div>\n                </div>\n              )}\n\n              {/* Security Information */}\n              {(selectedActivity.ipAddress || selectedActivity.userAgent) && (\n                <div>\n                  <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">\n                    Security Information\n                  </label>\n                  <div className=\"mt-2 space-y-2\">\n                    {selectedActivity.ipAddress && (\n                      <p className=\"text-sm text-gray-900 dark:text-white flex items-center gap-2\">\n                        <Shield className=\"h-4 w-4 text-orange-500\" />\n                        <span className=\"font-medium\">IP Address:</span>\n                        {selectedActivity.ipAddress}\n                      </p>\n                    )}\n                    {selectedActivity.userAgent && (\n                      <p className=\"text-sm text-gray-900 dark:text-white flex items-center gap-2\">\n                        <Settings className=\"h-4 w-4 text-blue-500\" />\n                        <span className=\"font-medium\">User Agent:</span>\n                        <span className=\"truncate\">{selectedActivity.userAgent}</span>\n                      </p>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Modal Footer */}\n            <div className=\"p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50\">\n              <div className=\"flex justify-end gap-3\">\n                <button\n                  onClick={() => setSelectedActivity(null)}\n                  className=\"px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  Close\n                </button>\n                <button\n                  onClick={() => {\n                    exportHistory([selectedActivity])\n                    setSelectedActivity(null)\n                  }}\n                  className=\"btn-primary flex items-center gap-2\"\n                >\n                  <Download className=\"h-4 w-4\" />\n                  Export This Activity\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Real-time Status Footer */}\n      <div className=\"card p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-t-2 border-t-green-500\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <div className=\"flex items-center space-x-4\">\n            <div className={`w-3 h-3 rounded-full animate-pulse ${\n              autoRefresh ? 'bg-green-500' : 'bg-gray-400'\n            }`}></div>\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              {autoRefresh ? 'Real-time Updates Active' : 'Real-time Updates Paused'}\n            </span>\n            {isLoading && (\n              <div className=\"flex items-center gap-2\">\n                <RefreshCw className=\"h-4 w-4 animate-spin text-blue-600\" />\n                <span className=\"text-sm text-blue-600 dark:text-blue-400\">Updating...</span>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400\">\n            <div className=\"flex items-center space-x-2\">\n              <Clock className=\"h-4 w-4\" />\n              <span>Last updated: {lastUpdated.toLocaleTimeString('en-PH')}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Activity className=\"h-4 w-4\" />\n              <span>Next update: {autoRefresh ? '30s' : 'Manual'}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Hash className=\"h-4 w-4\" />\n              <span>Total: {filteredHistory.length} activities</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAVA;;;AAqCe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAE/E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,OAAO,EAAE;QACT,YAAY,EAAE;QACd,UAAU,EAAE;QACZ,WAAW;QACX,OAAO,EAAE;QACT,YAAY,EAAE;IAChB;IAEA,4DAA4D;IAC5D,MAAM,cAA6B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wCAAE,IAAM;gBAC/C;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAW;wBAAa;qBAAM;oBACrC,SAAS;wBACP,aAAa;wBACb,OAAO;wBACP,KAAK;wBACL,UAAU;wBACV,UAAU;oBACZ;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAQ;wBAAY;qBAAS;oBACpC,SAAS;wBACP,UAAU;wBACV,QAAQ;wBACR,SAAS;wBACT,eAAe;wBACf,SAAS;oBACX;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAW;wBAAY;qBAAS;oBACvC,SAAS;wBACP,UAAU;wBACV,QAAQ;wBACR,eAAe;wBACf,iBAAiB;wBACjB,YAAY;oBACd;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAW;wBAAS;qBAAS;oBACpC,SAAS;wBACP,aAAa;wBACb,UAAU;wBACV,UAAU;wBACV,QAAQ;wBACR,UAAU;oBACZ;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAS;wBAAY;qBAAS;oBACrC,WAAW;oBACX,WAAW;oBACX,SAAS;wBACP,WAAW;wBACX,UAAU;wBACV,QAAQ;wBACR,iBAAiB;oBACnB;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAU;wBAAY;qBAAc;oBAC3C,SAAS;wBACP,YAAY;wBACZ,YAAY;wBACZ,UAAU;wBACV,UAAU;wBACV,gBAAgB;oBAClB;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAQ;wBAAY;qBAAS;oBACpC,SAAS;wBACP,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,QAAQ;wBACR,SAAS;wBACT,eAAe;oBACjB;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAW;wBAAU;qBAAU;oBACtC,SAAS;wBACP,aAAa;wBACb,QAAQ;wBACR,YAAY;wBACZ,iBAAiB;wBACjB,MAAM;oBACR;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAY;wBAAS;qBAAS;oBACrC,WAAW;oBACX,SAAS;wBACP,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,SAAS;wBACT,UAAU;oBACZ;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,MAAM;wBAAC;wBAAS;wBAAS;qBAAY;oBACrC,SAAS;wBACP,kBAAkB;4BAAC;4BAAa;4BAAkB;yBAAY;wBAC9D,kBAAkB;wBAClB,cAAc;4BAAC;4BAAG;4BAAG;yBAAE;wBACvB,kBAAkB;4BAAC;4BAAI;4BAAI;yBAAG;oBAChC;gBACF;aACD;uCAAE,EAAE;IAEL,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI;YAEJ,IAAI,aAAa;gBACf,WAAW;yCAAY;wBACrB,eAAe,IAAI;wBACnB,wDAAwD;wBACxD,aAAa;wBACb;iDAAW,IAAM,aAAa;gDAAQ;oBACxC;wCAAG,OAAO,2BAA2B;;YACvC;YAEA;qCAAO;oBACL,IAAI,UAAU,cAAc;gBAC9B;;QACF;4BAAG;QAAC;KAAY;IAEhB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;kDAAe,IAAM,oBAAoB;;YAC/C,MAAM;mDAAgB,IAAM,oBAAoB;;YAEhD,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YAEnC;qCAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;4BAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YAC9B,MAAM,WAAW,YAAY,MAAM;6DAAC,CAAA;oBAClC,gBAAgB;oBAChB,MAAM,gBAAgB,eAAe,MACnC,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI;qEAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;oBAEvF,cAAc;oBACd,MAAM,cAAc,eAAe,SAAS,KAAK,IAAI,KAAK;oBAE1D,mBAAmB;oBACnB,MAAM,eAAe,QAAQ,KAAK,CAAC,MAAM,KAAK,KAAK,QAAQ,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI;oBACnF,MAAM,oBAAoB,QAAQ,UAAU,CAAC,MAAM,KAAK,KAAK,QAAQ,UAAU,CAAC,QAAQ,CAAC,KAAK,QAAQ;oBACtG,MAAM,kBAAkB,QAAQ,QAAQ,CAAC,MAAM,KAAK,KAAK,QAAQ,QAAQ,CAAC,QAAQ,CAAC,KAAK,MAAM;oBAC9F,MAAM,eAAe,QAAQ,KAAK,CAAC,MAAM,KAAK,KAAK,QAAQ,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI;oBACnF,MAAM,oBAAoB,QAAQ,UAAU,CAAC,MAAM,KAAK,KAAK,QAAQ,UAAU,CAAC,QAAQ,CAAC,KAAK,QAAQ;oBAEtG,oBAAoB;oBACpB,MAAM,WAAW,IAAI,KAAK,KAAK,SAAS;oBACxC,MAAM,MAAM,IAAI;oBAChB,IAAI,mBAAmB;oBAEvB,OAAQ;wBACN,KAAK;4BACH,mBAAmB,AAAC,IAAI,OAAO,KAAK,SAAS,OAAO,MAAO,KAAK,KAAK,KAAK;4BAC1E;wBACF,KAAK;4BACH,mBAAmB,AAAC,IAAI,OAAO,KAAK,SAAS,OAAO,MAAO,IAAI,KAAK,KAAK,KAAK;4BAC9E;wBACF,KAAK;4BACH,mBAAmB,AAAC,IAAI,OAAO,KAAK,SAAS,OAAO,MAAO,KAAK,KAAK,KAAK,KAAK;4BAC/E;wBACF,KAAK;4BACH,mBAAmB,AAAC,IAAI,OAAO,KAAK,SAAS,OAAO,MAAO,KAAK,KAAK,KAAK,KAAK;4BAC/E;wBACF,KAAK;4BACH,mBAAmB;4BACnB;oBACJ;oBAEA,OAAO,iBAAiB,eAAe,gBAAgB,qBAChD,mBAAmB,gBAAgB,qBAAqB;gBACjE;;YAEA,UAAU;YACV,SAAS,IAAI;oDAAC,CAAC,GAAG;oBAChB,IAAI,aAAa;oBAEjB,OAAQ;wBACN,KAAK;4BACH,aAAa,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;4BAC5E;wBACF,KAAK;4BACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;4BACxC;wBACF,KAAK;4BACH,MAAM,gBAAgB;gCAAE,YAAY;gCAAG,QAAQ;gCAAG,UAAU;gCAAG,OAAO;4BAAE;4BACxE,aAAa,CAAC,aAAa,CAAC,EAAE,QAAQ,CAA+B,IAAI,CAAC,IAC9D,CAAC,aAAa,CAAC,EAAE,QAAQ,CAA+B,IAAI,CAAC;4BACzE;oBACJ;oBAEA,OAAO,cAAc,SAAS,CAAC,aAAa;gBAC9C;;YAEA,OAAO;QACT;2CAAG;QAAC;QAAa;QAAY;QAAY;QAAS;QAAW;QAAQ;KAAU;IAE/E,uCAAuC;IACvC,MAAM,mBAAmB;WAAI,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;KAAG;IAC7E,MAAM,iBAAiB;WAAI,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;KAAG;IACzE,MAAM,cAAc;WAAI,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;KAAG;IAEpE,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAC9E,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAE5C,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;QACrE,OAAO,IAAI,cAAc,IAAI;YAC3B,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;QAC/D,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;QAC5D,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,MAAM;gBACN,OAAO;gBACP,KAAK;gBACL,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IAEA,kBAAkB;IAClB,MAAM,kBAAkB;QACtB,IAAI,cAAc,MAAM,KAAK,gBAAgB,MAAM,EAAE;YACnD,iBAAiB,EAAE;QACrB,OAAO;YACL,iBAAiB,gBAAgB,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;QACtD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,MACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,MAC7B;mBAAI;gBAAM;aAAG;IAErB;IAEA,MAAM,mBAAmB;QACvB,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,6CAA6C;YAC7C,oCAAoC;YACpC,iBAAiB,EAAE;QACrB;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,eAAe,gBAAgB,MAAM,CAAC,CAAA,OAAQ,cAAc,QAAQ,CAAC,KAAK,EAAE;QAClF,cAAc;IAChB;IAEA,MAAM,gBAAgB,CAAC,OAAO,eAAe;QAC3C,MAAM,aAAa;YACjB;gBAAC;gBAAa;gBAAQ;gBAAU;gBAAe;gBAAQ;gBAAY;gBAAU;aAAW;eACrF,KAAK,GAAG,CAAC,CAAA,OAAQ;oBAClB,KAAK,SAAS;oBACd,KAAK,IAAI;oBACT,KAAK,MAAM;oBACX,KAAK,WAAW;oBAChB,KAAK,IAAI;oBACT,KAAK,QAAQ;oBACb,KAAK,MAAM;oBACX,KAAK,QAAQ;iBACd;SACF,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;QAEjC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,uBAAuB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACnF,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,sBAAsB;IACtB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE;YAC5B,MAAM,QAAQ;gBACZ,OAAO,gBAAgB,MAAM;gBAC7B,QAAQ,CAAC;gBACT,YAAY,CAAC;gBACb,UAAU,CAAC;gBACX,QAAQ,CAAC;gBACT,YAAY;gBACZ,WAAW;YACb;YAEA,MAAM,MAAM,IAAI;YAChB,MAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;YACrE,MAAM,UAAU,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;YAE5D,gBAAgB,OAAO;kDAAC,CAAA;oBACtB,gBAAgB;oBAChB,MAAM,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI;oBAE3D,oBAAoB;oBACpB,MAAM,UAAU,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,MAAM,UAAU,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI;oBAE3E,kBAAkB;oBAClB,MAAM,QAAQ,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;oBAEnE,gBAAgB;oBAChB,MAAM,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI;oBAE3D,4BAA4B;oBAC5B,MAAM,WAAW,IAAI,KAAK,KAAK,SAAS;oBACxC,IAAI,YAAY,OAAO;wBACrB,MAAM,UAAU;oBAClB;oBACA,IAAI,YAAY,SAAS;wBACvB,MAAM,SAAS;oBACjB;gBACF;;YAEA,OAAO;QACT;yCAAG;QAAC;KAAgB;IAEpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;0CAOpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAuD;;;;;;sEACpE,6LAAC;4DAAE,WAAU;sEAAsD,cAAc,KAAK;;;;;;;;;;;;8DAExF,6LAAC,qNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAyD;;;;;;sEACtE,6LAAC;4DAAE,WAAU;sEAAwD,cAAc,UAAU;;;;;;;;;;;;8DAE/F,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAI1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA2D;;;;;;sEACxE,6LAAC;4DAAE,WAAU;sEAA0D,cAAc,SAAS;;;;;;;;;;;;8DAEhG,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIrB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA2D;;;;;;sEACxE,6LAAC;4DAAE,WAAU;sEAA0D,cAAc,MAAM;;;;;;;;;;;;8DAE7F,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAW,CAAC,iEAAiE,EAChF,qBAAqB,WACjB,yEACA,gEACJ;;oCACC,qBAAqB,yBACpB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;6DAEhB,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAEpB,qBAAqB,WAAW,WAAW;;;;;;;0CAI9C,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAW,CAAC,6FAA6F,EACvG,cACI,qEACA,iEACJ;;kDAEF,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,kBAAkB,IAAI;;;;;;oCAAI;;;;;;;0CAIvE,6LAAC;gCACC,SAAS,IAAM,aAAa,CAAC;gCAC7B,WAAU;gCACV,UAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,iBAAiB,IAAI;;;;;;oCAAI;;;;;;;4BAIvE,cAAc,MAAM,GAAG,mBACtB;;kDACE,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIlC,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;0CAMpC,6LAAC;gCACC,SAAS,IAAM;gCACf,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;0BAOtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA2B;;;;;;;0CAG/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;;4CAET,4BAAc,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAAe,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAa;;;;;;;kDAK5F,6LAAC;wCAAI,WAAU;kDACZ,AAAC;4CAAC;4CAAQ;4CAAY;yCAAO,CAAW,GAAG,CAAC,CAAC,qBAC5C,6LAAC;gDAEC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,wDAAwD,EAClE,aAAa,OACT,sEACA,8EACJ;0DAED,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;+CARtC;;;;;;;;;;;;;;;;;;;;;;kCAgBf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,6LAAC;wCAAO,OAAM;kDAAe;;;;;;;;;;;;0CAI/B,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;;0CAItB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;kDAE3B,6LAAC;wCACC,SAAS,IAAM,aAAa,cAAc,QAAQ,SAAS;wCAC3D,WAAU;wCACV,OAAO,CAAC,KAAK,EAAE,cAAc,QAAQ,eAAe,aAAa;kDAEhE,cAAc,QAAQ,MAAM;;;;;;;;;;;;;;;;;;oBAMlC,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDAAI,WAAU;0DACZ,iBAAiB,GAAG,CAAC,CAAA,yBACpB,6LAAC;wDAAqB,WAAU;;0EAC9B,6LAAC;gEACC,MAAK;gEACL,SAAS,QAAQ,UAAU,CAAC,QAAQ,CAAC;gEACrC,UAAU,CAAC;oEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;wEACpB,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,YAAY;uFAAI,KAAK,UAAU;oFAAE;iFAAS;4EAC5C,CAAC;oEACH,OAAO;wEACL,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,YAAY,KAAK,UAAU,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;4EAChD,CAAC;oEACH;gEACF;gEACA,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;;oEACb,gBAAgB;oEAChB;;;;;;;;uDArBO;;;;;;;;;;;;;;;;kDA6BlB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDAAI,WAAU;0DACZ,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC;wDAAmB,WAAU;;0EAC5B,6LAAC;gEACC,MAAK;gEACL,SAAS,QAAQ,QAAQ,CAAC,QAAQ,CAAC;gEACnC,UAAU,CAAC;oEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;wEACpB,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,UAAU;uFAAI,KAAK,QAAQ;oFAAE;iFAAO;4EACtC,CAAC;oEACH,OAAO;wEACL,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;4EAC5C,CAAC;oEACH;gEACF;gEACA,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAW,CAAC,gDAAgD,EAAE,eAAe,SAAS;0EACzF;;;;;;;uDApBO;;;;;;;;;;;;;;;;kDA4BlB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;wDAAiB,WAAU;;0EAC1B,6LAAC;gEACC,MAAK;gEACL,SAAS,QAAQ,KAAK,CAAC,QAAQ,CAAC;gEAChC,UAAU,CAAC;oEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;wEACpB,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,OAAO;uFAAI,KAAK,KAAK;oFAAE;iFAAK;4EAC9B,CAAC;oEACH,OAAO;wEACL,WAAW,CAAA,OAAQ,CAAC;gFAClB,GAAG,IAAI;gFACP,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;4EACtC,CAAC;oEACH;gEACF;gEACA,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;;kFACd,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEACf;;;;;;;;uDArBO;;;;;;;;;;;;;;;;;;;;;;0CA8BpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;wCACP,WAAW;4CACT,OAAO,EAAE;4CACT,YAAY,EAAE;4CACd,UAAU,EAAE;4CACZ,WAAW;4CACX,OAAO,EAAE;4CACT,YAAY,EAAE;wCAChB;wCACA,cAAc;wCACd,cAAc;wCACd,aAAa;oCACf;oCACA,WAAU;;sDAEV,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;0BASnC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAGjD,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,cAAc,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM;oCACtD,MAAM,aAAa,CAAC,AAAC,QAAQ,cAAc,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;oCACjE,qBACE,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,iBAAiB,EAAE,aAAa,OAAO;kEACrD,YAAY;;;;;;kEAEf,6LAAC;wDAAK,WAAU;kEACb;;;;;;;;;;;;0DAGL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,GAAG,WAAW,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAGrC,6LAAC;wDAAK,WAAU;kEACb;;;;;;kEAEH,6LAAC;wDAAK,WAAU;;4DACb;4DAAW;;;;;;;;;;;;;;uCApBR;;;;;gCAyBd;;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAA4B;;;;;;;0CAGrD,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,cAAc,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM;oCAC9D,MAAM,aAAa,CAAC,AAAC,QAAQ,cAAc,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;oCACjE,qBACE,6LAAC;wCAAmB,WAAU;;0DAC5B,6LAAC;gDAAI,WAAU;;oDACZ,gBAAgB;kEACjB,6LAAC;wDAAK,WAAU;kEACb;;;;;;;;;;;;0DAGL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAW,CAAC,6CAA6C,EACvD,aAAa,aAAa,eAC1B,aAAa,SAAS,kBACtB,aAAa,WAAW,kBAAkB,eAC1C;4DACF,OAAO;gEAAE,OAAO,GAAG,WAAW,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAGrC,6LAAC;wDAAK,WAAU;kEACb;;;;;;kEAEH,6LAAC;wDAAK,WAAU;;4DACb;4DAAW;;;;;;;;;;;;;;uCAtBR;;;;;gCA2Bd;;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA2B;;;;;;;0CAG7C,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,cAAc,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM;oCACtD,MAAM,aAAa,CAAC,AAAC,QAAQ,cAAc,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;oCACjE,qBACE,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,KAAK,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;kEAG/B,6LAAC;wDAAK,WAAU;kEACb;;;;;;;;;;;;0DAGL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,GAAG,WAAW,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAGrC,6LAAC;wDAAK,WAAU;kEACb;;;;;;kEAEH,6LAAC;wDAAK,WAAU;;4DACb;4DAAW;;;;;;;;;;;;;;uCAtBR;;;;;gCA2Bd;;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAA2B;gDAC3B,gBAAgB,MAAM;gDAAC;;;;;;;wCAI5C,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,WAAU;;wDAET,cAAc,MAAM,KAAK,gBAAgB,MAAM,iBAC9C,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAClB;;;;;;;gDAIH,cAAc,MAAM,GAAG,mBACtB,6LAAC;oDAAK,WAAU;;wDACb,cAAc,MAAM;wDAAC;;;;;;;;;;;;;;;;;;;gCAQ/B,2BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAyB;;;;;;;;;;;;;;;;;;kCAOtD,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC;gCAEC,WAAW,CAAC,kFAAkF,EAC5F,cAAc,QAAQ,CAAC,KAAK,EAAE,IAAI,mEAAmE,IACrG;gCACF,OAAO;oCAAE,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;gCAAC;0CAE3C,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;4CACvC,WAAU;sDAET,cAAc,QAAQ,CAAC,KAAK,EAAE,kBAC7B,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAKtB,6LAAC;4CAAI,WAAW,CAAC,yBAAyB,EAAE,aAAa,KAAK,IAAI,EAAE,qCAAqC,CAAC;sDACvG,YAAY,KAAK,IAAI;;;;;;sDAGxB,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFACX,KAAK,MAAM;;;;;;sFAId,6LAAC;4EAAK,WAAW,CAAC,0EAA0E,EAAE,iBAAiB,KAAK,QAAQ,GAAG;;gFAC5H,gBAAgB,KAAK,QAAQ;gFAC7B,KAAK,QAAQ;;;;;;;sFAIhB,6LAAC;4EAAK,WAAW,CAAC,uDAAuD,EAAE,eAAe,KAAK,MAAM,GAAG;sFACrG,KAAK,MAAM;;;;;;;;;;;;8EAIhB,6LAAC;oEAAE,WAAU;8EACV,KAAK,WAAW;;;;;;gEAIlB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,6LAAC;oEAAI,WAAU;8EACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAA,oBACb,6LAAC;4EAEC,WAAU;;gFACX;gFACG;;2EAHG;;;;;;;;;;;;;;;;sEAUf,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,gBAAgB,KAAK,SAAS;;;;;;;;;;;;;;;;;8DAMrC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEACf,KAAK,IAAI;;;;;;;8EAGZ,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEACnB,KAAK,QAAQ;;;;;;;gEAGf,KAAK,SAAS,kBACb,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,KAAK,SAAS;;;;;;;;;;;;;wDAKpB,KAAK,OAAO,kBACX,6LAAC;4DACC,SAAS,IAAM,oBAAoB;4DACnC,WAAU;;8EAEV,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;+BAjGhC,KAAK,EAAE;;;;;;;;;;oBA6GjB,gBAAgB,MAAM,KAAK,mBAC1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;4CACP,cAAc;4CACd,cAAc;4CACd,WAAW;gDACT,OAAO,EAAE;gDACT,YAAY,EAAE;gDACd,UAAU,EAAE;gDACZ,WAAW;gDACX,OAAO,EAAE;gDACT,YAAY,EAAE;4CAChB;wCACF;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa,CAAC;wCAC7B,WAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YAS/C,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,eAAe,EAAE,aAAa,iBAAiB,IAAI,GAAG;0DACpE,YAAY,iBAAiB,IAAI;;;;;;0DAEpC,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsD;;;;;;kEAGpE,6LAAC;wDAAE,WAAU;kEACV,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;kDAI9B,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAW,CAAC,0EAA0E,EAAE,aAAa,iBAAiB,IAAI,GAAG;;oEAChI,YAAY,iBAAiB,IAAI;oEACjC,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;8DAK5B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAW,CAAC,0EAA0E,EAAE,iBAAiB,iBAAiB,QAAQ,GAAG;;oEACxI,gBAAgB,iBAAiB,QAAQ;oEACzC,iBAAiB,QAAQ;;;;;;;;;;;;;;;;;;8DAKhC,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAW,CAAC,uDAAuD,EAAE,eAAe,iBAAiB,MAAM,GAAG;0EACjH,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;sDAMhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,iBAAiB,IAAI;;;;;;;;;;;;;8DAI1B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,iBAAiB,QAAQ;;;;;;;;;;;;;8DAI9B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+E;;;;;;sEAGhG,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,IAAI,KAAK,iBAAiB,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;8CAO7D,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+E;;;;;;sDAGhG,6LAAC;4CAAE,WAAU;sDACV,iBAAiB,WAAW;;;;;;;;;;;;gCAKhC,iBAAiB,IAAI,IAAI,iBAAiB,IAAI,CAAC,MAAM,GAAG,mBACvD,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+E;;;;;;sDAGhG,6LAAC;4CAAI,WAAU;sDACZ,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAA,oBACzB,6LAAC;oDAEC,WAAU;;wDACX;wDACG;;mDAHG;;;;;;;;;;;;;;;;gCAWd,iBAAiB,OAAO,kBACvB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+E;;;;;;sDAGhG,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ,KAAK,SAAS,CAAC,iBAAiB,OAAO,EAAE,MAAM;;;;;;;;;;;;;;;;;gCAOvD,CAAC,iBAAiB,SAAS,IAAI,iBAAiB,SAAS,mBACxD,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+E;;;;;;sDAGhG,6LAAC;4CAAI,WAAU;;gDACZ,iBAAiB,SAAS,kBACzB,6LAAC;oDAAE,WAAU;;sEACX,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAC7B,iBAAiB,SAAS;;;;;;;gDAG9B,iBAAiB,SAAS,kBACzB,6LAAC;oDAAE,WAAU;;sEACX,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAAc;;;;;;sEAC9B,6LAAC;4DAAK,WAAU;sEAAY,iBAAiB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;4CACP,cAAc;gDAAC;6CAAiB;4CAChC,oBAAoB;wCACtB;wCACA,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAC,mCAAmC,EAClD,cAAc,iBAAiB,eAC/B;;;;;;8CACF,6LAAC;oCAAK,WAAU;8CACb,cAAc,6BAA6B;;;;;;gCAE7C,2BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;sCAKjE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;;gDAAK;gDAAe,YAAY,kBAAkB,CAAC;;;;;;;;;;;;;8CAEtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;;gDAAK;gDAAc,cAAc,QAAQ;;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;;gDAAK;gDAAQ,gBAAgB,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD;GAt7CwB;KAAA", "debugId": null}}]}